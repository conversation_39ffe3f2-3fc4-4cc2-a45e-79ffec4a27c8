#!/bin/bash

# <PERSON>ript to fix PostgreSQL index syntax in migration files

MIGRATIONS_DIR="/home/<USER>/Websites/NWAPromote/prisma/migrations"

echo "Fixing PostgreSQL index syntax in migration files..."

# Process each migration.sql file
find "$MIGRATIONS_DIR" -name "migration.sql" | while read -r file; do
    echo "Processing $file..."
    
    # Fix inline indexes to separate CREATE INDEX statements
    # First, let's extract table names and their indexes, then create separate CREATE INDEX statements
    
    # Create a temporary file
    TEMP_FILE="${file}.temp"
    
    # Process the file line by line
    while IFS= read -r line; do
        # Check if this is an inline index
        if [[ $line =~ INDEX\ \"([^\"]+)\"(\([^\)]+\)) ]]; then
            # Extract index name and columns
            index_name="${BASH_REMATCH[1]}"
            columns="${BASH_REMATCH[2]}"
            # Convert to CREATE INDEX statement (to be added after table creation)
            echo "-- Index will be created separately: $index_name on $columns" >> "$TEMP_FILE"
        elif [[ $line =~ UNIQUE\ INDEX\ \"([^\"]+)\"(\([^\)]+\)) ]]; then
            # Extract unique index name and columns
            index_name="${BASH_REMATCH[1]}"
            columns="${BASH_REMATCH[2]}"
            # Convert to CREATE UNIQUE INDEX statement (to be added after table creation)
            echo "-- Unique index will be created separately: $index_name on $columns" >> "$TEMP_FILE"
        else
            # Keep the line as is
            echo "$line" >> "$TEMP_FILE"
        fi
    done < "$file"
    
    # Replace the original file
    mv "$TEMP_FILE" "$file"
    
    echo "Fixed $file"
done

echo "Index syntax fixed. Now creating separate CREATE INDEX statements..."