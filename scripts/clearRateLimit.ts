import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function clearRateLimitAndTest() {
  try {
    console.log('=== Clearing Rate Limits ===');
    
    // The rate limiting is in-memory, so restarting the development server would clear it
    // But let's also check if there are any database-based rate limits or user lockouts
    
    const email = '<EMAIL>';
    
    // Check user status
    const user = await prisma.user.findUnique({
      where: { email },
      select: { 
        id: true, 
        email: true, 
        name: true, 
        role: true, 
        password: true,
        // Look for any lockout fields if they exist
        createdAt: true
      }
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('✅ User found:', user.email);
    
    // Test password validity
    console.log('\n=== Testing Password ===');
    const isValid = await bcrypt.compare('password', user.password || '');
    console.log('Password valid:', isValid ? 'YES' : 'NO');
    
    if (!isValid) {
      console.log('🔧 Fixing password...');
      const hashedPassword = await bcrypt.hash('password', 10);
      await prisma.user.update({
        where: { email },
        data: { password: hashedPassword }
      });
      console.log('✅ Password updated');
    }
    
    // Check for any failed login attempts or lockout records
    console.log('\n=== Checking for Login Attempt Records ===');
    try {
      // Try to find any audit logs or login attempt records
      const auditLogs = await prisma.$queryRaw`
        SELECT * FROM AuditLog 
        WHERE entityType = 'User' 
        AND action LIKE '%login%' 
        AND entityId = ${user.id}
        ORDER BY timestamp DESC 
        LIMIT 10
      `;
      console.log('Recent login attempts:', auditLogs);
    } catch (error) {
      console.log('No audit log table or login records found');
    }
    
    console.log('\n=== Rate Limit Information ===');
    console.log('Rate limits are stored in-memory and will reset when the dev server restarts');
    console.log('Auth rate limit: 5 attempts per 15 minutes');
    console.log('The rate limit should reset automatically, or restart your dev server to clear it immediately');
    
    // Test if we can simulate the NextAuth flow
    console.log('\n=== Testing NextAuth Flow ===');
    console.log('User object that NextAuth would receive:');
    console.log({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    console.log('\n=== Recommendations ===');
    console.log('1. Wait 15 minutes for rate limit to reset, OR');
    console.log('2. Restart your development server to clear in-memory rate limits');
    console.log('3. Try logging in using a different browser or incognito mode');
    console.log('4. Check the browser console and network tab for additional errors');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearRateLimitAndTest();
