const { JwtValidationService } = require('../src/services/JwtValidationService.ts');

// Mock request object
const mockRequest = {
  cookies: {
    get: (name) => ({
      value: process.env[`COOKIE_${name.toUpperCase()}`] || ''
    })
  },
  headers: {
    get: (name) => process.env[`HEADER_${name.toUpperCase()}`] || ''
  }
};

async function testJwtValidation() {
  console.log('Testing JWT validation...');
  console.log('Environment check:');
  console.log('- NEXTAUTH_SECRET configured:', !!process.env.NEXTAUTH_SECRET);
  console.log('- Secret length:', process.env.NEXTAUTH_SECRET?.length);

  try {
    const result = await JwtValidationService.validateToken(mockRequest);

    console.log('Validation result:', result);

    if (!result.valid) {
      console.error('JWT validation failed:', result.error);
      process.exit(1);
    }

    console.log('JWT validation successful!');
    console.log('User:', result.user);
  } catch (error) {
    console.error('Test execution error:', error);
    process.exit(1);
  }
}

testJwtValidation();