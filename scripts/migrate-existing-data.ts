import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

// Function to detect platform from URL
function detectPlatformFromUrl(url: string): { platform: string; platformUrl: string } | null {
  const lowerUrl = url.toLowerCase();
  
  if (lowerUrl.includes('youtube.com') || lowerUrl.includes('youtu.be')) {
    return { platform: 'youtube', platformUrl: url };
  } else if (lowerUrl.includes('tiktok.com')) {
    return { platform: 'tiktok', platformUrl: url };
  } else if (lowerUrl.includes('rumble.com')) {
    return { platform: 'rumble', platformUrl: url };
  }
  
  return null;
}

async function migrateVideoUrls() {
  console.log('🔄 Migrating existing video URLs to platform-specific fields...');
  
  try {
    // Get all videos that have a URL but no platform-specific URLs
    const videosToMigrate = await prisma.video.findMany({
      where: {
        url: {
          not: ''
        },
        AND: [
          { youtubeUrl: null },
          { tiktokUrl: null },
          { rumbleUrl: null }
        ]
      }
    });

    console.log(`Found ${videosToMigrate.length} videos to migrate`);

    let migratedCount = 0;
    for (const video of videosToMigrate) {
      const platformInfo = detectPlatformFromUrl(video.url);
      
      if (platformInfo) {
        const updateData: Record<string, unknown> = {};
        
        switch (platformInfo.platform) {
          case 'youtube':
            updateData.youtubeUrl = platformInfo.platformUrl;
            break;
          case 'tiktok':
            updateData.tiktokUrl = platformInfo.platformUrl;
            break;
          case 'rumble':
            updateData.rumbleUrl = platformInfo.platformUrl;
            break;
        }

        await prisma.video.update({
          where: { id: video.id },
          data: updateData
        });

        migratedCount++;
        console.log(`✅ Migrated video "${video.title}" - detected ${platformInfo.platform}`);
      } else {
        console.log(`⚠️  Could not detect platform for video "${video.title}" with URL: ${video.url}`);
      }
    }

    console.log(`✅ Successfully migrated ${migratedCount} video URLs`);
  } catch (error) {
    console.error('❌ Error migrating video URLs:', error);
    throw error;
  }
}

async function setDefaultNotificationPreferences() {
  console.log('🔄 Setting default notification preferences for existing users...');
  
  try {
    // Get all users to check their current notification preferences
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        nwaVideoNotifications: true,
        userVideoNotifications: true
      }
    });

    console.log(`Found ${allUsers.length} users to check`);

    let updatedCount = 0;
    for (const user of allUsers) {
      // Check if user needs notification preferences updated
      // Since the schema has defaults, we'll ensure they're set to true if they're false
      const needsUpdate = user.nwaVideoNotifications === false || user.userVideoNotifications === false;
      
      if (needsUpdate) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            nwaVideoNotifications: true,  // Default to enabled
            userVideoNotifications: true  // Default to enabled
          }
        });

        updatedCount++;
        console.log(`✅ Updated notification preferences for user: ${user.email}`);
      } else {
        console.log(`ℹ️  User ${user.email} already has notification preferences set`);
      }
    }

    console.log(`✅ Successfully updated notification preferences for ${updatedCount} users`);
  } catch (error) {
    console.error('❌ Error setting default notification preferences:', error);
    throw error;
  }
}

async function updateEngagementRecords() {
  console.log('🔄 Updating existing engagement records with platform information...');
  
  try {
    // Get all engagement records that don't have platform information
    const engagementsToUpdate = await prisma.userEngagement.findMany({
      where: {
        OR: [
          { platform: '' },
          { platform: 'unknown' },
          { platform: { contains: '' } }
        ]
      },
      include: {
        Video: true
      }
    });

    console.log(`Found ${engagementsToUpdate.length} engagement records to update`);

    let updatedCount = 0;
    for (const engagement of engagementsToUpdate) {
      let detectedPlatform = 'unknown';
      
      // Try to detect platform from the video's URL or platform-specific URLs
      if (engagement.Video) {
        const video = engagement.Video;
        
        if (video.youtubeUrl) {
          detectedPlatform = 'youtube';
        } else if (video.tiktokUrl) {
          detectedPlatform = 'tiktok';
        } else if (video.rumbleUrl) {
          detectedPlatform = 'rumble';
        } else if (video.url) {
          const platformInfo = detectPlatformFromUrl(video.url);
          if (platformInfo) {
            detectedPlatform = platformInfo.platform;
          }
        } else if (video.platform && video.platform !== '') {
          detectedPlatform = video.platform.toLowerCase();
        }
      }

      await prisma.userEngagement.update({
        where: { id: engagement.id },
        data: {
          platform: detectedPlatform
        }
      });

      updatedCount++;
      console.log(`✅ Updated engagement record - detected platform: ${detectedPlatform}`);
    }

    console.log(`✅ Successfully updated ${updatedCount} engagement records`);
  } catch (error) {
    console.error('❌ Error updating engagement records:', error);
    throw error;
  }
}

async function main() {
  console.log('🚀 Starting data migration to new structure...\n');
  
  try {
    // Step 1: Migrate video URLs to platform-specific fields
    await migrateVideoUrls();
    console.log('');
    
    // Step 2: Set default notification preferences for existing users
    await setDefaultNotificationPreferences();
    console.log('');
    
    // Step 3: Update existing engagement records with platform information
    await updateEngagementRecords();
    console.log('');
    
    console.log('🎉 Data migration completed successfully!');
  } catch (error) {
    console.error('❌ Data migration failed:', error);
    process.exit(1);
  }
}

main()
  .catch((e) => {
    console.error('❌ Migration script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });