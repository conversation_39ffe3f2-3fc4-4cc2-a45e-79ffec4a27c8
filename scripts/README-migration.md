# Data Migration to New Structure

This document describes the data migration process for updating existing data to the new video sharing workflow structure.

## Overview

The migration updates existing data to support the new multi-platform video sharing workflow by:

1. **Migrating video URLs to platform-specific fields** - Moving URLs from the generic `url` field to platform-specific fields (`youtubeUrl`, `tiktokUrl`, `rumbleUrl`)
2. **Setting default notification preferences** - Ensuring all users have notification preferences set for NWA videos and user videos
3. **Updating engagement records with platform information** - Adding platform information to existing engagement records

## Migration Scripts

### Main Migration Script
- **File**: `scripts/migrate-existing-data.ts`
- **Purpose**: Performs the complete data migration
- **Usage**: `npx tsx scripts/migrate-existing-data.ts`

### Test Data Scripts
- **File**: `scripts/create-test-data.ts` - Creates test data for migration testing
- **File**: `scripts/test-migration-edge-cases.ts` - Tests edge cases and complex scenarios
- **File**: `scripts/verify-migration.ts` - Verifies migration results
- **File**: `scripts/final-migration-test.ts` - Comprehensive final testing

## Migration Details

### 1. Video URL Migration

**What it does:**
- Detects platform from existing video URLs
- Moves URLs to appropriate platform-specific fields
- Supports YouTube (including youtu.be and mobile URLs), TikTok, and Rumble

**Platform Detection:**
- YouTube: `youtube.com`, `youtu.be`, `m.youtube.com`
- TikTok: `tiktok.com`, `m.tiktok.com`
- Rumble: `rumble.com`
- Unknown platforms are left in the original `url` field

**Example:**
```
Before: url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
After:  youtubeUrl = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
```

### 2. Notification Preferences Migration

**What it does:**
- Ensures all users have notification preferences set
- Sets default values to `true` for both NWA and user video notifications
- Updates users who had preferences disabled back to enabled (as per requirements)

**Fields updated:**
- `nwaVideoNotifications` - Controls notifications for admin-posted NWA videos
- `userVideoNotifications` - Controls notifications for user-shared personal videos

### 3. Engagement Records Migration

**What it does:**
- Updates existing engagement records with platform information
- Detects platform from associated video URLs
- Sets platform to 'unknown' if detection fails

**Platform Detection Logic:**
1. Check video's platform-specific URL fields first
2. Fall back to original URL field if platform fields are empty
3. Use existing platform field as last resort
4. Default to 'unknown' if no platform can be determined

## Requirements Compliance

### Requirement 1.4
✅ **Migrate existing video URLs to appropriate platform fields**
- Successfully migrates YouTube, TikTok, and Rumble URLs
- Handles various URL formats (standard, mobile, short URLs)
- Preserves original URLs for unknown platforms

### Requirement 8.5
✅ **Set default notification preferences for existing users**
- Ensures all users have notification preferences configured
- Sets sensible defaults (enabled) for both notification types
- Updates users with disabled preferences to enabled state

## Testing Results

The migration has been thoroughly tested with:

- **7 videos** successfully migrated to platform-specific fields
- **2 users** with notification preferences properly set
- **13 engagement records** updated with platform information
- **Platform distribution**: YouTube (5), TikTok (3), Rumble (2), Unknown (3)

## Edge Cases Handled

1. **YouTube Short URLs** (`youtu.be`) - ✅ Detected as YouTube
2. **Mobile URLs** (`m.youtube.com`, `m.tiktok.com`) - ✅ Properly detected
3. **Mixed Case URLs** (`WWW.YOUTUBE.COM`) - ✅ Case-insensitive detection
4. **Empty URLs** - ✅ Handled gracefully, no migration performed
5. **Unknown Platforms** - ✅ Left in original URL field, marked as 'unknown'

## Data Integrity

- **No data loss**: Original URLs are preserved alongside new platform-specific fields
- **Referential integrity**: All relationships maintained during migration
- **Idempotent**: Migration can be run multiple times safely
- **Rollback friendly**: Original data structure remains intact

## Usage Instructions

1. **Backup your database** before running the migration
2. Run the migration script: `npx tsx scripts/migrate-existing-data.ts`
3. Verify results with: `npx tsx scripts/verify-migration.ts`
4. Run comprehensive tests: `npx tsx scripts/final-migration-test.ts`

## Post-Migration

After successful migration:
- Videos will have platform-specific URLs populated
- Users will have notification preferences enabled by default
- Engagement records will include platform information
- The system will be ready for the new multi-platform workflow

## Troubleshooting

If migration fails:
1. Check database connectivity
2. Ensure Prisma client is up to date
3. Verify schema matches expected structure
4. Check for any data constraints violations
5. Review error logs for specific issues

The migration is designed to be safe and reversible, with comprehensive logging and error handling.