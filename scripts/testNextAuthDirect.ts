import { authOptions } from '../src/lib/auth';
import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function testNextAuthDirectly() {
  try {
    console.log('=== Testing NextAuth Authorize Function Directly ===');
    
    // Get the credentials provider
    const credentialsProvider = authOptions.providers?.find(
      provider => provider.type === 'credentials'
    ) as unknown as { authorize: (credentials: { email: string; password: string }, req: Record<string, unknown>) => Promise<unknown> };
    
    if (!credentialsProvider) {
      console.log('❌ Credentials provider not found');
      return;
    }
    
    console.log('✅ Found credentials provider');
    
    // Test the authorize function directly
    const credentials = {
      email: '<EMAIL>',
      password: 'password'
    };
    
    console.log('\\n=== Testing Authorize Function ===');
    console.log('Credentials:', credentials);
    
    try {
      const result = await credentialsProvider.authorize(credentials, {} as Record<string, unknown>);
      console.log('\\n=== Authorize Result ===');
      console.log('Success:', result ? 'YES' : 'NO');
      console.log('Result:', result);
      
      if (result) {
        console.log('\\n✅ NextAuth authorization would SUCCEED');
        console.log('User object returned:', JSON.stringify(result, null, 2));
      } else {
        console.log('\\n❌ NextAuth authorization FAILED');
        console.log('The authorize function returned null');
      }
      
    } catch (error) {
      console.error('\\n❌ Error in authorize function:', error);
    }
    
    // Also test manually what the authorize function does
    console.log('\\n=== Manual Database Test ===');
    const user = await prisma.user.findUnique({
      where: { email: credentials.email }
    });
    
    if (user && user.password) {
      const isValid = await bcrypt.compare(credentials.password, user.password);
      console.log('Manual password check:', isValid ? 'VALID' : 'INVALID');
      console.log('User exists:', 'YES');
      console.log('Password hash exists:', user.password ? 'YES' : 'NO');
    } else {
      console.log('User lookup failed');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testNextAuthDirectly();
