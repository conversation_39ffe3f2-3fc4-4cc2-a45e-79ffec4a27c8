import { JwtValidationService } from '../src/services/JwtValidationService';

async function testJwtValidation() {
  console.log('Testing JWT validation...');

  // Mock request object
  const mockRequest = {
    cookies: {
      get: (name: string) => ({
        value: process.env[`COOKIE_${name.toUpperCase()}`] || ''
      })
    },
    headers: {
      get: (name: string) => process.env[`HEADER_${name.toUpperCase()}`] || ''
    }
  } as any;

  const result = await JwtValidationService.validateToken(mockRequest);

  console.log('Validation result:', result);

  if (!result.valid) {
    console.error('JWT validation failed:', result.error);
    process.exit(1);
  }

  console.log('JWT validation successful!');
  console.log('User:', result.user);
}

testJwtValidation().catch(console.error);