import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function verifyMigration() {
  console.log('🔍 Verifying data migration results...\n');
  
  try {
    // Check video URL migration
    console.log('📹 Checking video URL migration:');
    const videos = await prisma.video.findMany({
      select: {
        id: true,
        title: true,
        url: true,
        youtubeUrl: true,
        tiktokUrl: true,
        rumbleUrl: true,
        platform: true
      }
    });

    for (const video of videos) {
      console.log(`- ${video.title}:`);
      console.log(`  Original URL: ${video.url}`);
      console.log(`  Platform: ${video.platform}`);
      console.log(`  YouTube URL: ${video.youtubeUrl || 'None'}`);
      console.log(`  TikTok URL: ${video.tiktokUrl || 'None'}`);
      console.log(`  Rumble URL: ${video.rumbleUrl || 'None'}`);
      console.log('');
    }

    // Check user notification preferences
    console.log('👤 Checking user notification preferences:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        nwaVideoNotifications: true,
        userVideoNotifications: true
      }
    });

    for (const user of users) {
      console.log(`- ${user.email}:`);
      console.log(`  NWA Video Notifications: ${user.nwaVideoNotifications}`);
      console.log(`  User Video Notifications: ${user.userVideoNotifications}`);
      console.log('');
    }

    // Check engagement records
    console.log('📊 Checking engagement records:');
    const engagements = await prisma.userEngagement.findMany({
      select: {
        id: true,
        platform: true,
        action: true,
        Video: {
          select: {
            title: true
          }
        }
      }
    });

    for (const engagement of engagements) {
      console.log(`- ${engagement.Video?.title}: ${engagement.action} on ${engagement.platform}`);
    }

    console.log('\n✅ Migration verification completed!');
  } catch (error) {
    console.error('❌ Error verifying migration:', error);
    throw error;
  }
}

verifyMigration()
  .catch((e) => {
    console.error('❌ Verification failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });