import { SecurityRateLimit } from '../src/lib/security-headers';

async function clearSecurityRateLimit() {
  try {
    console.log('=== Clearing Security Rate Limits ===');
    
    // The SecurityRateLimit uses different identifiers:
    // - IP address from x-forwarded-for
    // - x-real-ip 
    // - 'unknown' as fallback
    
    // Common identifiers that might be blocked
    const identifiersToReset = [
      '127.0.0.1',
      '::1',
      'localhost',
      'unknown',
      '*************', // Based on the callback URL I saw earlier
    ];
    
    console.log('Clearing rate limits for common identifiers...');
    
    identifiersToReset.forEach(identifier => {
      SecurityRateLimit.reset(identifier);
      console.log(`✅ Cleared rate limit for: ${identifier}`);
    });
    
    // Also run cleanup to remove expired entries
    SecurityRateLimit.cleanup();
    console.log('✅ Cleaned up expired rate limit entries');
    
    console.log('\n=== Rate Limit Reset Complete ===');
    console.log('You can now try logging in again at: http://localhost:3000/auth/signin');
    console.log('Email: <EMAIL>');
    console.log('Password: password');
    
    console.log('\n=== Debug Info ===');
    console.log('The middleware rate limit is set to 5 attempts per 15 minutes');
    console.log('If you still get blocked, try using a different browser or incognito mode');
    
  } catch (error) {
    console.error('❌ Error clearing rate limits:', error);
  }
}

clearSecurityRateLimit();
