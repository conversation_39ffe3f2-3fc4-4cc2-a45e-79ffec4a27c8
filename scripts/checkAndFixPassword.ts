import { prisma } from '../src/lib/prisma';
import bcrypt from 'bcryptjs';

async function checkAndFixPassword() {
  try {
    const email = '<EMAIL>';
    
    // First, check if the user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, name: true, password: true, role: true }
    });
    
    console.log('=== User Check ===');
    console.log('User found:', user ? 'YES' : 'NO');
    
    if (!user) {
      console.log('❌ User does not exist. Creating user...');
      
      const hashedPassword = await bcrypt.hash('password', 10);
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          password: hashedPassword,
          role: 'USER'
        }
      });
      
      console.log('✅ User created:', newUser.email);
    } else {
      console.log('User details:');
      console.log('- ID:', user.id);
      console.log('- Email:', user.email);
      console.log('- Name:', user.name);
      console.log('- Role:', user.role);
      console.log('- Has password:', user.password ? 'YES' : 'NO');
      
      if (user.password) {
        // Test if current password matches "password"
        const isCurrentPasswordValid = await bcrypt.compare('password', user.password);
        console.log('- Current password is "password":', isCurrentPasswordValid ? 'YES' : 'NO');
        
        if (!isCurrentPasswordValid) {
          console.log('🔧 Updating password to "password"...');
          const newHashedPassword = await bcrypt.hash('password', 10);
          
          await prisma.user.update({
            where: { email },
            data: { password: newHashedPassword },
          });
          
          console.log('✅ Password updated successfully');
          
          // Verify the update
          const updatedUser = await prisma.user.findUnique({
            where: { email },
            select: { password: true }
          });
          
          if (updatedUser?.password) {
            const isNewPasswordValid = await bcrypt.compare('password', updatedUser.password);
            console.log('✓ Verification: New password works:', isNewPasswordValid ? 'YES' : 'NO');
          }
        } else {
          console.log('✅ Password is already correct');
        }
      } else {
        console.log('❌ User has no password set. Setting password...');
        const hashedPassword = await bcrypt.hash('password', 10);
        
        await prisma.user.update({
          where: { email },
          data: { password: hashedPassword },
        });
        
        console.log('✅ Password set successfully');
      }
    }
    
    // Final verification
    console.log('\n=== Final Verification ===');
    const finalUser = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, password: true }
    });
    
    if (finalUser?.password) {
      const finalCheck = await bcrypt.compare('password', finalUser.password);
      console.log('🎯 Final test - login should work:', finalCheck ? 'YES' : 'NO');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAndFixPassword();
