// scripts/test-oauth-config.js

console.log('🔐 Testing OAuth Configuration...\n');

// Check environment variables
const requiredEnvVars = [
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
  'MEMBER_PORTAL_URL',
  'CLIENT_ID',
  'CLIENT_SECRET'
];

console.log('1️⃣ Environment Variables Check:');
let allEnvVarsPresent = true;

requiredEnvVars.forEach(envVar => {
  const value = process.env[envVar];
  const isPresent = !!value;
  const displayValue = envVar.includes('SECRET') ? '[CONFIGURED]' : value;
  
  console.log(`   ${isPresent ? '✅' : '❌'} ${envVar}: ${isPresent ? displayValue : '[MISSING]'}`);
  
  if (!isPresent) {
    allEnvVarsPresent = false;
  }
});

if (!allEnvVarsPresent) {
  console.log('\n❌ Missing required environment variables. Please check your .env.local file.');
  process.exit(1);
}

console.log('\n2️⃣ OAuth Endpoints Check:');
const memberPortalUrl = process.env.MEMBER_PORTAL_URL;
const endpoints = [
  `${memberPortalUrl}/api/oauth/authorize`,
  `${memberPortalUrl}/api/oauth/token`,
  `${memberPortalUrl}/api/oauth/userinfo`
];

endpoints.forEach(endpoint => {
  console.log(`   📍 ${endpoint}`);
});

console.log('\n3️⃣ NextAuth Configuration:');
console.log(`   🌐 NextAuth URL: ${process.env.NEXTAUTH_URL}`);
console.log(`   🔑 Secret Length: ${process.env.NEXTAUTH_SECRET?.length} characters`);
console.log(`   🏠 Member Portal: ${process.env.MEMBER_PORTAL_URL}`);

console.log('\n4️⃣ OAuth Flow URLs:');
const nextAuthUrl = process.env.NEXTAUTH_URL;
console.log(`   🚀 Sign In: ${nextAuthUrl}/api/auth/signin/member-portal`);
console.log(`   🔄 Callback: ${nextAuthUrl}/api/auth/callback/member-portal`);
console.log(`   🚪 Sign Out: ${nextAuthUrl}/api/auth/signout`);

console.log('\n5️⃣ Cookie Configuration:');
console.log('   🍪 State Cookie: next-auth.state');
console.log('   🍪 PKCE Cookie: next-auth.pkce.code_verifier');
console.log('   🍪 Session Cookie: next-auth.session-token');
console.log('   🍪 CSRF Cookie: next-auth.csrf-token');

console.log('\n6️⃣ Troubleshooting Tips:');
console.log('   💡 If you get "State cookie was missing":');
console.log('      - Clear browser cookies for localhost:3002');
console.log('      - Ensure member portal is running on localhost:3001');
console.log('      - Check browser console for cookie errors');
console.log('      - Try in incognito/private browsing mode');

console.log('\n   💡 If OAuth callback fails:');
console.log('      - Verify member portal OAuth client configuration');
console.log('      - Check redirect URI matches exactly');
console.log('      - Ensure CLIENT_ID and CLIENT_SECRET are correct');

console.log('\n✅ OAuth configuration check completed!');
console.log('🌐 Visit http://localhost:3002 to test the OAuth flow');
