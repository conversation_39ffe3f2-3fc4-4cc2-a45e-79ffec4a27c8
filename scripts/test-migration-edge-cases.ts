import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';
import { execSync } from 'child_process';

const prisma = new PrismaClient();

async function createEdgeCaseTestData() {
  console.log('🔄 Creating edge case test data...');
  
  try {
    // Get the admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      throw new Error('Admin user not found.');
    }

    // Create test videos with edge case URLs
    const edgeCaseVideos = [
      {
        title: 'YouTube Short URL',
        description: 'A YouTube video with short URL',
        url: 'https://youtu.be/dQw4w9WgXcQ',
        platform: 'youtube',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'YouTube Mobile URL',
        description: 'A YouTube video with mobile URL',
        url: 'https://m.youtube.com/watch?v=dQw4w9WgXcQ',
        platform: 'youtube',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'TikTok Mobile URL',
        description: 'A TikTok video with mobile URL',
        url: 'https://m.tiktok.com/@user/video/1234567890',
        platform: 'tiktok',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'Empty URL Video',
        description: 'A video with empty URL',
        url: '',
        platform: 'unknown',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'Video with Mixed Case Platform',
        description: 'A video with mixed case platform URL',
        url: 'https://WWW.YOUTUBE.COM/watch?v=test123',
        platform: 'YouTube',
        userId: adminUser.id,
        status: 'published'
      }
    ];

    for (const videoData of edgeCaseVideos) {
      const video = await prisma.video.create({
        data: videoData
      });

      console.log(`✅ Created edge case video: ${video.title}`);

      // Create engagement records with various platform states
      await prisma.userEngagement.create({
        data: {
          userId: adminUser.id,
          videoId: video.id,
          platform: '', // Empty platform
          action: 'like'
        }
      });
    }

    // Create a user with different notification preferences to test
    const hashedPassword = await bcrypt.hash('testpassword', 12);
    await prisma.user.create({
      data: {
        id: 'edge-case-user-1',
        email: '<EMAIL>',
        name: 'Edge Case User',
        password: hashedPassword,
        role: 'USER',
        nwaVideoNotifications: false,  // Set to false to test migration
        userVideoNotifications: false  // Set to false to test migration
      }
    });

    console.log('✅ Created edge case user with disabled notifications');
    console.log('🎉 Edge case test data creation completed!');
  } catch (error) {
    console.error('❌ Error creating edge case test data:', error);
    throw error;
  }
}

async function runEdgeCaseMigration() {
  console.log('🚀 Running migration on edge case data...');
  
  // Import and run the migration functions
  
  try {
    execSync('npx tsx scripts/migrate-existing-data.ts', { stdio: 'inherit' });
    console.log('✅ Edge case migration completed');
  } catch (error) {
    console.error('❌ Edge case migration failed:', error);
    throw error;
  }
}

async function verifyEdgeCases() {
  console.log('🔍 Verifying edge case migration results...\n');
  
  try {
    // Check edge case video migrations
    console.log('📹 Checking edge case video URL migrations:');
    const videos = await prisma.video.findMany({
      where: {
        title: {
          in: [
            'YouTube Short URL',
            'YouTube Mobile URL', 
            'TikTok Mobile URL',
            'Empty URL Video',
            'Video with Mixed Case Platform'
          ]
        }
      },
      select: {
        title: true,
        url: true,
        youtubeUrl: true,
        tiktokUrl: true,
        rumbleUrl: true,
        platform: true
      }
    });

    for (const video of videos) {
      console.log(`- ${video.title}:`);
      console.log(`  Original URL: "${video.url}"`);
      console.log(`  Platform: ${video.platform}`);
      console.log(`  YouTube URL: ${video.youtubeUrl || 'None'}`);
      console.log(`  TikTok URL: ${video.tiktokUrl || 'None'}`);
      console.log(`  Rumble URL: ${video.rumbleUrl || 'None'}`);
      console.log('');
    }

    // Check edge case user notification preferences
    console.log('👤 Checking edge case user notification preferences:');
    const edgeCaseUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        email: true,
        nwaVideoNotifications: true,
        userVideoNotifications: true
      }
    });

    if (edgeCaseUser) {
      console.log(`- ${edgeCaseUser.email}:`);
      console.log(`  NWA Video Notifications: ${edgeCaseUser.nwaVideoNotifications}`);
      console.log(`  User Video Notifications: ${edgeCaseUser.userVideoNotifications}`);
      console.log('');
    }

    console.log('✅ Edge case verification completed!');
  } catch (error) {
    console.error('❌ Error verifying edge cases:', error);
    throw error;
  }
}

async function main() {
  try {
    await createEdgeCaseTestData();
    console.log('');
    await runEdgeCaseMigration();
    console.log('');
    await verifyEdgeCases();
  } catch (error) {
    console.error('❌ Edge case testing failed:', error);
    process.exit(1);
  }
}

main()
  .catch((e) => {
    console.error('❌ Edge case test script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });