import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function createTestEbooks() {
  console.log('=== Creating Test Ebooks ===');
  
  try {
    // Get the admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });
    
    if (!adminUser) {
      throw new Error('Admin user not found. Please run seed first.');
    }
    
    // Create test ebooks
    const testEbooks = [
      {
        type: 'ebook',
        title: 'NWA Peace Manifesto',
        description: 'A comprehensive guide to achieving world peace through the New World Alliance principles.',
        fileUrl: 'https://example.com/peace-manifesto.pdf',
        published: true,
        userId: adminUser.id
      },
      {
        type: 'ebook',
        title: 'Global Prosperity Handbook',
        description: 'Learn how to contribute to global economic prosperity and well-being for all.',
        fileUrl: 'https://example.com/prosperity-handbook.pdf',
        published: true,
        userId: adminUser.id
      },
      {
        type: 'ebook',
        title: 'Media for Change',
        description: 'How to use media effectively to promote positive change in the world.',
        fileUrl: 'https://example.com/media-for-change.pdf',
        published: false, // Unpublished ebook for testing admin-only visibility
        userId: adminUser.id
      }
    ];
    
    let createdCount = 0;
    for (const ebookData of testEbooks) {
      const ebook = await prisma.media.create({
        data: ebookData
      });
      
      console.log(`✅ Created ebook: "${ebook.title}" (Published: ${ebook.published})`);
      createdCount++;
    }
    
    console.log(`🎉 Successfully created ${createdCount} test ebooks!`);
    
  } catch (error) {
    console.error('❌ Error creating test ebooks:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTestEbooks();
