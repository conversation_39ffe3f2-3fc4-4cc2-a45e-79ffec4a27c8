#!/usr/bin/env node

const { default: lighthouse } = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs').promises;
const path = require('path');

async function runLighthouseAudit(url, name) {
  console.log(`Running Lighthouse audit for ${name}...`);
  
  // Launch Chrome
  const chrome = await chromeLauncher.launch({chromeFlags: ['--headless']});
  const options = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
    port: chrome.port
  };
  
  // Run Lighthouse
  const runnerResult = await lighthouse(url, options);
  
  // Save report
  const reportPath = path.join(process.cwd(), `lighthouse-${name.toLowerCase().replace(/\s+/g, '-')}.json`);
  await fs.writeFile(reportPath, JSON.stringify(runnerResult.lhr, null, 2));
  
  // Kill Chrome
  await chrome.kill();
  
  // Extract key metrics
  const categories = runnerResult.lhr.categories;
  console.log(`\n${name} Results:`);
  console.log(`  Performance: ${Math.round(categories.performance.score * 100)}`);
  console.log(`  Accessibility: ${Math.round(categories.accessibility.score * 100)}`);
  console.log(`  Best Practices: ${Math.round(categories['best-practices'].score * 100)}`);
  console.log(`  SEO: ${Math.round(categories.seo.score * 100)}`);
  
  return {
    name,
    scores: {
      performance: Math.round(categories.performance.score * 100),
      accessibility: Math.round(categories.accessibility.score * 100),
      bestPractices: Math.round(categories['best-practices'].score * 100),
      seo: Math.round(categories.seo.score * 100)
    },
    reportPath
  };
}

async function main() {
  console.log('🚀 Starting Lighthouse Audit for Dashboard and User Management pages...\n');
  
  try {
    // Run audits for key pages
    const results = [];
    
    // Dashboard page
    results.push(await runLighthouseAudit('http://localhost:3000/dashboard', 'Dashboard'));
    
    // Admin analytics page (if it exists)
    try {
      results.push(await runLighthouseAudit('http://localhost:3000/admin/analytics', 'Admin Analytics'));
    } catch (error) {
      console.log('Admin Analytics page not found or not accessible');
    }
    
    // Admin users page (if it exists)
    try {
      results.push(await runLighthouseAudit('http://localhost:3000/admin/users', 'Admin Users'));
    } catch (error) {
      console.log('Admin Users page not found or not accessible');
    }
    
    // Generate summary report
    console.log('\n📊 Lighthouse Audit Summary');
    console.log('==========================');
    results.forEach(result => {
      console.log(`\n${result.name}:`);
      console.log(`  Performance: ${result.scores.performance}/100`);
      console.log(`  Accessibility: ${result.scores.accessibility}/100`);
      console.log(`  Best Practices: ${result.scores.bestPractices}/100`);
      console.log(`  SEO: ${result.scores.seo}/100`);
      console.log(`  Report: ${result.reportPath}`);
    });
    
    // Save summary to file
    const summaryPath = path.join(process.cwd(), 'lighthouse-summary.json');
    await fs.writeFile(summaryPath, JSON.stringify(results, null, 2));
    console.log(`\n📋 Summary saved to: ${summaryPath}`);
    
    console.log('\n✅ Lighthouse audit completed successfully!');
    
  } catch (error) {
    console.error('❌ Lighthouse audit failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}