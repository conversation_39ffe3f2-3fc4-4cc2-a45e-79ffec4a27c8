import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function testAuthFlow() {
  try {
    const email = '<EMAIL>';
    const password = 'password';
    
    console.log('=== Testing Authentication Flow ===');
    console.log('Email:', email);
    console.log('Password:', password);
    
    // This mimics what happens in the NextAuth authorize function
    console.log('\n1. Finding user in database...');
    const user = await prisma.user.findUnique({
      where: {
        email: email
      }
    });
    
    console.log('User found:', user ? 'YES' : 'NO');
    
    if (!user) {
      console.log('❌ Authentication would FAIL - user not found');
      return;
    }
    
    console.log('User ID:', user.id);
    console.log('User email:', user.email);
    console.log('User name:', user.name);
    console.log('User role:', user.role);
    
    if (!user.password) {
      console.log('❌ Authentication would FAIL - user has no password');
      return;
    }
    
    console.log('\n2. Checking password...');
    console.log('Password hash exists:', 'YES');
    console.log('Password hash (first 20 chars):', user.password.substring(0, 20) + '...');
    
    console.log('\n3. Comparing password with bcrypt...');
    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log('Password comparison result:', isPasswordValid ? 'VALID' : 'INVALID');
    
    if (!isPasswordValid) {
      console.log('❌ Authentication would FAIL - password mismatch');
      return;
    }
    
    console.log('\n✅ Authentication would SUCCEED');
    console.log('NextAuth would return user object:');
    console.log({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    // Additional checks
    console.log('\n=== Additional Checks ===');
    
    // Check for common issues
    const trimmedEmail = email.trim().toLowerCase();
    const trimmedUserEmail = user.email.trim().toLowerCase();
    console.log('Email case sensitivity check:', trimmedEmail === trimmedUserEmail ? 'PASS' : 'FAIL');
    
    // Check for whitespace issues
    console.log('User email has leading/trailing spaces:', user.email !== user.email.trim() ? 'YES' : 'NO');
    
  } catch (error) {
    console.error('❌ Error during authentication test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuthFlow();
