import { prisma } from '../src/lib/prisma.js';
import bcrypt from 'bcryptjs';

async function checkAdminUser() {
  try {
    const email = '<EMAIL>';
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (user) {
      console.log('User found:');
      console.log(`Email: ${user.email}`);
      console.log(`Name: ${user.name}`);
      console.log(`Role: ${user.role}`);
      console.log(`ID: ${user.id}`);
      console.log(`Created: ${user.createdAt}`);
    } else {
      console.log('User not found. Creating admin user...');
      const hashedPassword = await bcrypt.hash('fcA*5-c0nwmFF!!', 10);
      
      const newAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          password: hashedPassword,
          role: '<PERSON>MIN',
        },
      });
      
      console.log('Admin user created successfully:');
      console.log(`Email: ${newAdmin.email}`);
      console.log(`Name: ${newAdmin.name}`);
      console.log(`Role: ${newAdmin.role}`);
      console.log(`ID: ${newAdmin.id}`);
    }
  } catch (error) {
    console.error('Error checking/creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUser();
