import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function runFinalMigrationTest() {
  console.log('🧪 Running final comprehensive migration test...\n');
  
  try {
    // Test 1: Verify video URL migration
    console.log('✅ Test 1: Video URL Migration');
    const videosWithPlatformUrls = await prisma.video.findMany({
      where: {
        OR: [
          { youtubeUrl: { not: null } },
          { tiktokUrl: { not: null } },
          { rumbleUrl: { not: null } }
        ]
      }
    });
    
    console.log(`   - Found ${videosWithPlatformUrls.length} videos with platform-specific URLs`);
    
    // Verify that YouTube URLs were properly migrated
    const youtubeVideos = videosWithPlatformUrls.filter(v => v.youtubeUrl);
    const tiktokVideos = videosWithPlatformUrls.filter(v => v.tiktokUrl);
    const rumbleVideos = videosWithPlatformUrls.filter(v => v.rumbleUrl);
    
    console.log(`   - YouTube videos: ${youtubeVideos.length}`);
    console.log(`   - TikTok videos: ${tiktokVideos.length}`);
    console.log(`   - Rumble videos: ${rumbleVideos.length}`);
    
    // Test 2: Verify notification preferences
    console.log('\n✅ Test 2: User Notification Preferences');
    const usersWithNotificationPrefs = await prisma.user.findMany({
      select: {
        email: true,
        nwaVideoNotifications: true,
        userVideoNotifications: true
      }
    });
    
    const enabledNwaNotifications = usersWithNotificationPrefs.filter(u => u.nwaVideoNotifications).length;
    const enabledUserNotifications = usersWithNotificationPrefs.filter(u => u.userVideoNotifications).length;
    
    console.log(`   - Total users: ${usersWithNotificationPrefs.length}`);
    console.log(`   - Users with NWA notifications enabled: ${enabledNwaNotifications}`);
    console.log(`   - Users with User notifications enabled: ${enabledUserNotifications}`);
    
    // Test 3: Verify engagement records have platform information
    console.log('\n✅ Test 3: Engagement Records Platform Information');
    const engagementRecords = await prisma.userEngagement.findMany({
      select: {
        platform: true,
        action: true
      }
    });
    
    const platformCounts = engagementRecords.reduce((acc, record) => {
      acc[record.platform] = (acc[record.platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log(`   - Total engagement records: ${engagementRecords.length}`);
    console.log('   - Platform distribution:');
    Object.entries(platformCounts).forEach(([platform, count]) => {
      console.log(`     * ${platform}: ${count} records`);
    });
    
    // Test 4: Verify data integrity
    console.log('\n✅ Test 4: Data Integrity Checks');
    
    // Check that all videos have at least one URL (original or platform-specific)
    const videosWithoutUrls = await prisma.video.findMany({
      where: {
        AND: [
          { url: '' },
          { youtubeUrl: null },
          { tiktokUrl: null },
          { rumbleUrl: null }
        ]
      }
    });
    
    console.log(`   - Videos without any URL: ${videosWithoutUrls.length}`);
    
    // Check that all engagement records have platform information
    const engagementsWithoutPlatform = await prisma.userEngagement.findMany({
      where: {
        platform: ''
      }
    });
    
    console.log(`   - Engagement records without platform: ${engagementsWithoutPlatform.length}`);
    
    // Test 5: Verify requirements compliance
    console.log('\n✅ Test 5: Requirements Compliance Check');
    
    // Requirement 1.4: Migrate existing video URLs to appropriate platform fields
    const migratedVideos = await prisma.video.findMany({
      where: {
        AND: [
          { url: { not: '' } },
          {
            OR: [
              { youtubeUrl: { not: null } },
              { tiktokUrl: { not: null } },
              { rumbleUrl: { not: null } }
            ]
          }
        ]
      }
    });
    
    console.log(`   - Requirement 1.4: ${migratedVideos.length} videos successfully migrated to platform fields ✅`);
    
    // Requirement 8.5: Set default notification preferences for existing users
    const usersWithPreferences = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        nwaVideoNotifications: true,
        userVideoNotifications: true
      }
    });
    
    console.log(`   - Requirement 8.5: ${usersWithPreferences.length} users have notification preferences set ✅`);
    
    console.log('\n🎉 All migration tests passed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log(`   • Video URLs migrated: ${migratedVideos.length}`);
    console.log(`   • Users with notification preferences: ${usersWithPreferences.length}`);
    console.log(`   • Engagement records updated: ${engagementRecords.length}`);
    console.log(`   • Platform distribution: ${Object.keys(platformCounts).join(', ')}`);
    
  } catch (error) {
    console.error('❌ Final migration test failed:', error);
    throw error;
  }
}

runFinalMigrationTest()
  .catch((e) => {
    console.error('❌ Final test script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });