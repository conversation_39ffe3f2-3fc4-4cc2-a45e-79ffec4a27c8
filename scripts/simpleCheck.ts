import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function checkAndFixPassword() {
  try {
    const email = '<EMAIL>';
    
    console.log('=== Checking user ===');
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, name: true, password: true, role: true }
    });
    
    console.log('User found:', user ? 'YES' : 'NO');
    
    if (!user) {
      console.log('❌ User does not exist. Creating user...');
      
      const hashedPassword = await bcrypt.hash('password', 10);
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          password: hashedPassword,
          role: 'USER'
        }
      });
      
      console.log('✅ User created:', newUser.email);
    } else {
      console.log('User details:');
      console.log('- ID:', user.id);
      console.log('- Email:', user.email);
      console.log('- Name:', user.name);
      console.log('- Role:', user.role);
      console.log('- Has password:', user.password ? 'YES' : 'NO');
      
      if (user.password) {
        // Test if current password matches "password"
        const isCurrentPasswordValid = await bcrypt.compare('password', user.password);
        console.log('- Current password is "password":', isCurrentPasswordValid ? 'YES' : 'NO');
        
        if (!isCurrentPasswordValid) {
          console.log('🔧 Updating password to "password"...');
          const newHashedPassword = await bcrypt.hash('password', 10);
          
          await prisma.user.update({
            where: { email },
            data: { password: newHashedPassword },
          });
          
          console.log('✅ Password updated successfully');
        } else {
          console.log('✅ Password is already correct');
        }
      } else {
        console.log('❌ User has no password set. Setting password...');
        const hashedPassword = await bcrypt.hash('password', 10);
        
        await prisma.user.update({
          where: { email },
          data: { password: hashedPassword },
        });
        
        console.log('✅ Password set successfully');
      }
    }
    
    // Final verification
    console.log('\n=== Final Verification ===');
    const finalUser = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, password: true }
    });
    
    if (finalUser?.password) {
      const finalCheck = await bcrypt.compare('password', finalUser.password);
      console.log('🎯 Login should work:', finalCheck ? 'YES' : 'NO');
      
      if (finalCheck) {
        console.log('\n✅ DIAGNOSIS: The password should work for login');
        console.log('Try logging in at: http://localhost:3000/auth/signin');
        console.log('Email: <EMAIL>');
        console.log('Password: password');
      } else {
        console.log('\n❌ DIAGNOSIS: There is still an issue with the password');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAndFixPassword();
