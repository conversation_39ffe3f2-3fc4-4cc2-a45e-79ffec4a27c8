#!/usr/bin/env node
/*
  Fails if the literal string "mysql" appears in non-generated sources.
  Excludes: node_modules, .next, coverage, src/generated, public, .agent-os, docs, prisma/migrations (history), .git, .roo, .claude
*/
const fs = require('fs');
const path = require('path');

const IGNORE_DIRS = new Set([
  'node_modules',
  '.next',
  'coverage',
  'src/generated',
  'public',
  '.agent-os',
  'docs',
  'prisma/migrations',
  '.git',
  '.roo',
  '.claude'
]);

const ROOT = process.cwd();
let failures = [];

function shouldIgnore(relPath) {
  // If relPath starts with any ignored dir
  const parts = relPath.split(path.sep);
  for (let i = 0; i < parts.length; i++) {
    const prefix = parts.slice(0, i + 1).join('/');
    if (IGNORE_DIRS.has(prefix)) return true;
  }
  // Ignore the check script itself
  if (relPath === 'scripts/checkNoMysql.js') return true;
  return false;
}

function walk(dir) {
  for (const entry of fs.readdirSync(dir, { withFileTypes: true })) {
    const abs = path.join(dir, entry.name);
    const rel = path.relative(ROOT, abs);
    if (shouldIgnore(rel)) continue;
    if (entry.isDirectory()) {
      walk(abs);
    } else if (entry.isFile()) {
      // Only check text-like files by extension
      const ext = path.extname(entry.name).toLowerCase();
      const exts = new Set(['.ts', '.tsx', '.js', '.jsx', '.json', '.md', '.yml', '.yaml', '.toml', '.sh', '.mjs', '.tsconfig', '.prisma']);
      if (!exts.has(ext) && ext !== '') continue;
      try {
        const content = fs.readFileSync(abs, 'utf8');
        if (/mysql/i.test(content)) {
          failures.push(rel);
        }
      } catch (_) {}
    }
  }
}

walk(ROOT);

if (failures.length > 0) {
  console.error('Found disallowed "mysql" references in non-generated sources:');
  failures.forEach(f => console.error(' -', f));
  process.exit(1);
} else {
  console.log('No disallowed "mysql" references found.');
}
