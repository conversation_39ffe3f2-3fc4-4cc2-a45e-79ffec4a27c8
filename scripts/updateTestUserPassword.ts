import { prisma } from '../src/lib/prisma.js';
import bcrypt from 'bcryptjs';

async function updateTestUserPassword() {
  try {
    const email = '<EMAIL>';
    const newPassword = 'password';
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      console.log('User not found.');
      return;
    }

    await prisma.user.update({
      where: { email },
      data: { password: hashedPassword },
    });

    console.log('Password updated successfully for:', email);
  } catch (error) {
    console.error('Error updating password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateTestUserPassword();