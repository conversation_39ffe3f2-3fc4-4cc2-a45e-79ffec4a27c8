import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function checkDatabase() {
  console.log('=== Database Tables Check ===');
  
  try {
    // Check if Video table exists and has data
    const videoCount = await prisma.video.count();
    console.log('Total videos in database:', videoCount);
    
    if (videoCount > 0) {
      const videos = await prisma.video.findMany({
        select: {
          id: true,
          title: true,
          url: true,
          youtubeUrl: true,
          tiktokUrl: true,
          rumbleUrl: true,
          status: true,
          user: {
            select: {
              role: true
            }
          }
        },
        take: 5
      });
      console.log('Sample videos:', JSON.stringify(videos, null, 2));
      
      // Check NWA videos specifically
      const nwaVideoCount = await prisma.video.count({
        where: {
          user: {
            role: { in: ['ADMIN', 'NWA_TEAM'] }
          }
        }
      });
      console.log('NWA videos count:', nwaVideoCount);
    }

    // Check if Media table exists for ebooks
    try {
      const mediaCount = await prisma.media.count();
      console.log('Total media (ebooks) in database:', mediaCount);
    } catch (err) {
      console.log('Media table check failed:', (err as Error).message);
    }
    
    // Check users with ADMIN role
    const adminCount = await prisma.user.count({
      where: {
        role: { in: ['ADMIN', 'NWA_TEAM'] }
      }
    });
    console.log('Admin/NWA_TEAM users count:', adminCount);
    
  } catch (error) {
    console.error('Database check failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
