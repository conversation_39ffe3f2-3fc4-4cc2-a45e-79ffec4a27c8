import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestData() {
  console.log('🔄 Creating test data for migration...');
  
  try {
    // Get the admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      throw new Error('Admin user not found. Please run seed first.');
    }

    // Create test videos with different platform URLs in the old format
    const testVideos = [
      {
        title: 'YouTube Test Video',
        description: 'A test video from YouTube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        platform: 'youtube',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'TikTok Test Video',
        description: 'A test video from TikTok',
        url: 'https://www.tiktok.com/@user/video/1234567890',
        platform: 'tiktok',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'Rumble Test Video',
        description: 'A test video from Rumble',
        url: 'https://rumble.com/v123456-test-video.html',
        platform: 'rumble',
        userId: adminUser.id,
        status: 'published'
      },
      {
        title: 'Unknown Platform Video',
        description: 'A video from an unknown platform',
        url: 'https://example.com/video/123',
        platform: 'other',
        userId: adminUser.id,
        status: 'published'
      }
    ];

    for (const videoData of testVideos) {
      const video = await prisma.video.create({
        data: videoData
      });

      console.log(`✅ Created test video: ${video.title}`);

      // Create some test engagement records without platform info
      await prisma.userEngagement.create({
        data: {
          userId: adminUser.id,
          videoId: video.id,
          platform: '', // Empty platform to test migration
          action: 'like'
        }
      });

      await prisma.userEngagement.create({
        data: {
          userId: adminUser.id,
          videoId: video.id,
          platform: '', // Empty platform to test migration
          action: 'share'
        }
      });

      console.log(`✅ Created test engagement records for: ${video.title}`);
    }

    // Create a test user with proper password hashing
    const testUserPassword = await bcrypt.hash('password', 12);
    
    // Check if test user already exists
    const existingTestUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingTestUser) {
      // Update existing test user with properly hashed password
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: testUserPassword,
          name: 'Test User',
          role: 'USER',
          nwaVideoNotifications: true,
          userVideoNotifications: true
        }
      });
      console.log('✅ Updated existing test user with proper password hash');
    } else {
      // Create new test user
      await prisma.user.create({
        data: {
          id: 'test-user-1',
          email: '<EMAIL>',
          name: 'Test User',
          password: testUserPassword,
          role: 'USER',
          // Use default values for notification preferences
          nwaVideoNotifications: true,
          userVideoNotifications: true
        }
      });
      console.log('✅ Created new test user with proper password hash');
    }

    console.log('✅ Created test user without notification preferences');
    console.log('🎉 Test data creation completed!');
  } catch (error) {
    console.error('❌ Error creating test data:', error);
    throw error;
  }
}

createTestData()
  .catch((e) => {
    console.error('❌ Test data creation failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });