// Assign all <NAME_EMAIL>

import { prisma } from '../src/lib/prisma';

async function assignVideosToTestUser() {
  try {
    const email = '<EMAIL>';
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      console.error('User not found:', email);
      return;
    }
    const updated = await prisma.video.updateMany({
      data: { userId: user.id },
    });
    console.log(`Assigned all videos to user ${email} (id: ${user.id}). Updated ${updated.count} videos.`);
  } catch (error) {
    console.error('Error assigning videos:', error);
  } finally {
    await prisma.$disconnect();
  }
}

assignVideosToTestUser();