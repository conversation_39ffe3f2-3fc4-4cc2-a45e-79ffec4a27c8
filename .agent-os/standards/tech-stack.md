# Tech Stack

> Version: 1.0.0
> Last Updated: 2025-08-31

## Context

This file is part of the Agent OS standards system. These global tech stack defaults are referenced by all product codebases when initializing new projects. Individual projects may override these choices in their `.agent-os/product/tech-stack.md` file.

## Core Technologies

### Application Framework
- **Framework:** Next.js 15
- **Version:** 15.4.5
- **Language:** TypeScript 5.9+
- **Runtime:** Node.js 20+

### Database
- **Primary:** PostgreSQL
- **Version:** 17+
- **ORM:** Prisma 6.13+

## Frontend Stack

### JavaScript Framework
- **Framework:** React 19
- **Version:** 19.1.0
- **Build Tool:** Next.js built-in (Turbopack)

### Import Strategy
- **Strategy:** ES6 modules
- **Package Manager:** npm
- **Node Version:** 20+

### CSS Framework
- **Framework:** TailwindCSS
- **Version:** 4.0+
- **PostCSS:** Yes

### UI Components
- **Library:** Custom component library
- **Icons:** Lucide React, Heroicons
- **Styling:** Tailwind CSS with custom design system

## Assets & Media

### Fonts
- **Provider:** Next.js Font Optimization
- **Loading Strategy:** Built-in font optimization

### Icons
- **Library:** Lucide React, Heroicons
- **Implementation:** React components

## Infrastructure

### Application Hosting
- **Platform:** Vercel (recommended) or self-hosted
- **Service:** Serverless functions / Docker containers
- **Region:** Global CDN

### Database Hosting
- **Provider:** Self-hosted or managed PostgreSQL
- **Service:** PostgreSQL with connection pooling
- **Backups:** Automated with Prisma

### Caching & Storage
- **Caching:** Redis for sessions and API responses
- **Asset Storage:** Next.js public folder or external CDN
- **Session Storage:** Redis with NextAuth.js

## Deployment

### CI/CD Pipeline
- **Platform:** GitHub Actions
- **Trigger:** Push to main/staging branches
- **Tests:** Jest test suite before deployment

### Environments
- **Production:** main branch
- **Staging:** staging branch
- **Development:** feature branches

### Build Process
- **Build Command:** npm run build
- **Start Command:** npm start
- **Containerization:** Docker with docker-compose
- **Health Checks:** Built-in Next.js health endpoints

## Additional Technologies

### Authentication & Security
- **Authentication:** NextAuth.js 4.24+
- **Security:** Custom middleware with rate limiting
- **Validation:** Zod for schema validation
- **Sanitization:** DOMPurify for content security

### Real-time Features
- **Server-Sent Events:** Built-in Next.js API routes
- **Web Push Notifications:** Web Push API
- **Caching:** Redis for performance optimization

### Development Tools
- **Testing:** Jest with React Testing Library
- **Code Quality:** ESLint, TypeScript compiler
- **Database Management:** Prisma Studio, database migrations
- **Performance:** Lighthouse for auditing

---

*This tech stack is specifically configured for the NWA Media Site project. Individual projects may extend or override these choices in their .agent-os/product/tech-stack.md file.*
