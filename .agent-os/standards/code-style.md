# Code Style Guide

> Version: 1.0.0
> Last Updated: 2025-04-24

## Context

This file is part of the Agent OS standards system. These global code style rules are referenced by all product codebases and provide default formatting guidelines. Individual projects may extend or override these rules in their `.agent-os/product/code-style.md` file.

## General Formatting

### Indentation
- Use 2 spaces for indentation (never tabs)
- Maintain consistent indentation throughout files
- Align nested structures for readability

### Naming Conventions
- **Variables and Functions**: Use camelCase (e.g., `userProfile`, `calculateTotal`)
- **Classes and Types**: Use PascalCase (e.g., `UserProfile`, `PaymentProcessor`)
- **Constants**: Use UPPER_SNAKE_CASE (e.g., `MAX_RETRY_COUNT`)
- **Files**: Use kebab-case for components, camelCase for utilities (e.g., `user-profile.tsx`, `calculateTotal.ts`)
- **Interfaces**: Use PascalCase with 'I' prefix (e.g., `IUserProfile`)
- **Enums**: Use PascalCase (e.g., `UserRole`)

### String Formatting
- Use single quotes for strings: `'Hello World'`
- Use double quotes for JSX attributes: `className="container"`
- Use template literals for interpolation: `Hello ${name}`
- Use template literals for multi-line strings
- Prefer template literals over string concatenation

## JSX/React Formatting

### Component Structure Rules
- Use 2 spaces for indentation
- Place nested elements on new lines with proper indentation
- Content between tags should be on its own line when multi-line
- Use self-closing tags when no children: `<Component />`

### Props Formatting
- Place each prop on its own line for complex components
- Align props vertically for readability
- Keep the closing `>` on the same line as the last prop
- Use camelCase for prop names

### Example JSX Structure

```tsx
<div className="container">
  <header className="flex flex-col space-y-2
                     md:flex-row md:space-y-0 md:space-x-4">
    <h1 className="text-primary dark:text-primary-300">
      Page Title
    </h1>
    <nav className="flex flex-col space-y-2
                    md:flex-row md:space-y-0 md:space-x-4">
      <Link href="/"
            className="btn-ghost">
        Home
      </Link>
      <Link href="/about"
            className="btn-ghost">
        About
      </Link>
    </nav>
  </header>
</div>
```

## TypeScript and React Best Practices

### TypeScript Guidelines
- Use strict type checking with `strict: true` in tsconfig.json
- Prefer interfaces over types for object definitions
- Use union types for restricted values: `type Status = 'pending' | 'completed' | 'failed'`
- Use `unknown` instead of `any` for truly unknown values
- Leverage utility types: `Partial<T>`, `Pick<T>`, `Omit<T>`

### React Component Patterns
- Use functional components with hooks
- Prefer custom hooks for reusable logic
- Use proper TypeScript generics for component props
- Implement error boundaries for robust error handling
- Use React.memo for performance optimization when appropriate

## Tailwind CSS Preferences

### Multi-line CSS classes in JSX

- Use multi-line formatting for complex Tailwind classes in JSX
- Group related classes logically (spacing, colors, typography, etc.)
- Align classes vertically for readability
- Use responsive prefixes in ascending order (sm, md, lg, xl, 2xl)
- Keep hover and focus states on separate lines
- Place custom CSS classes at the beginning

**Example of multi-line Tailwind CSS classes:**

```tsx
<div className="custom-cta bg-gray-50 dark:bg-gray-900 p-4 rounded cursor-pointer w-full
                hover:bg-gray-100 dark:hover:bg-gray-800
                sm:p-6 sm:font-medium
                md:p-8 md:text-lg
                lg:p-10 lg:text-xl lg:font-semibold
                xl:p-12 xl:text-2xl
                2xl:p-14 2xl:text-3xl 2xl:font-bold">
  I'm a call-to-action!
</div>
```

## Code Comments

### When to Comment
- Add brief comments above non-obvious business logic
- Document complex algorithms or calculations
- Explain the "why" behind implementation choices

### Comment Maintenance
- Never remove existing comments unless removing the associated code
- Update comments when modifying code to maintain accuracy
- Keep comments concise and relevant

### Comment Format
```typescript
/**
 * Calculate compound interest with monthly contributions
 * Uses the formula: A = P(1 + r/n)^(nt) + PMT × (((1 + r/n)^(nt) - 1) / (r/n))
 * @param principal - Initial principal amount
 * @param rate - Annual interest rate (decimal)
 * @param time - Time in years
 * @param monthlyPayment - Monthly payment amount
 * @returns Total amount after compound interest
 */
function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  monthlyPayment: number
): number {
  // Implementation here
  return 0;
}
```

### JSDoc Standards
- Use JSDoc comments for all public functions and classes
- Include @param and @returns tags for functions
- Use @example for complex usage patterns
- Document thrown errors with @throws

## Project-Specific Guidelines

### Next.js Conventions
- Use App Router for all new routes (`app/` directory)
- Implement Server Components by default, use Client Components only when necessary
- Use `use client` directive sparingly and only at component boundaries
- Leverage Next.js built-in optimizations (Image, Font, Script components)

### Database and API Guidelines
- Use Prisma for all database operations
- Implement proper error handling in API routes
- Use Zod for request/response validation
- Follow RESTful API conventions with proper HTTP status codes

### Testing Standards
- Write unit tests for utility functions and hooks
- Write integration tests for API routes
- Use React Testing Library for component testing
- Maintain test coverage above 80% for critical paths

---

*These code style guidelines are specifically tailored for the NWA Media Site project. Individual projects may extend or override these rules in their .agent-os/product/code-style.md file.*
