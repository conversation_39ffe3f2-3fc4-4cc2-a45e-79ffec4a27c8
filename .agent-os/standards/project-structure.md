# NWA Media Site Project Structure

> Version: 1.0.0
> Last Updated: 2025-08-25
> Project: NWA Media Site (nwapromote)

## Overview

This document provides a comprehensive overview of the NWA Media Site project structure, designed to help developers and AI agents understand the codebase organization and locate relevant files efficiently.

## Root Directory Structure

```
/home/<USER>/Websites/NWAPromote/
├── .agent-os/                 # Project management and standards
│   ├── product/              # Project-specific documentation
│   │   ├── mission.md        # Product vision and goals
│   │   ├── roadmap.md        # Development roadmap
│   │   ├── tech-stack.md     # Technology choices
│   │   └── decisions.md      # Architecture decisions
│   └── standards/            # Development standards
│       ├── code-of-conduct.md    # Community guidelines
│       ├── tech-stack.md         # Global tech stack defaults
│       ├── code-style.md         # Coding conventions
│       └── best-practices.md     # Development practices
├── docs/                     # Project documentation
│   ├── security/             # Security documentation
│   ├── project-management/   # PM and workflow docs
│   └── reflections/          # Development reflections
├── prisma/                   # Database schema and migrations
│   ├── migrations/           # Database migration files
│   └── schema.prisma         # Prisma schema definition
├── public/                   # Static assets
│   └── icons/                # PWA icons and assets
├── scripts/                  # Utility scripts
├── src/                      # Source code
│   ├── app/                  # Next.js App Router pages
│   ├── components/           # React components
│   ├── contexts/             # React contexts
│   ├── hooks/                # Custom React hooks
│   ├── lib/                  # Utility libraries
│   ├── middleware/           # Next.js middleware
│   ├── services/             # Business logic services
│   └── types/                # TypeScript type definitions
└── Configuration files      # package.json, next.config.ts, etc.
```

## Source Code Organization (`src/`)

### App Router Structure (`src/app/`)

```
src/app/
├── api/                     # API routes
│   ├── auth/                # Authentication endpoints
│   │   ├── [...nextauth]/   # NextAuth.js configuration
│   │   ├── callback/        # OAuth callbacks
│   │   └── signout/         # Sign out endpoint
│   ├── admin/               # Admin-only endpoints
│   │   ├── analytics/       # Analytics data
│   │   ├── users/           # User management
│   │   └── audit-logs/      # Audit logging
│   ├── media/               # Media management
│   ├── notifications/       # Notification system
│   ├── profile/             # User profile management
│   └── videos/              # Video content management
├── auth/                    # Authentication pages
│   ├── signin/              # Sign in page
│   └── error/               # Auth error page
├── dashboard/               # Main dashboard
├── leaderboard/             # Leaderboard page
├── notifications/           # Notifications page
├── settings/                # User settings
└── admin/                   # Admin dashboard
```

### Component Architecture (`src/components/`)

```
src/components/
├── ui/                      # Reusable UI components
│   ├── button.tsx          # Button component
│   ├── input.tsx           # Input component
│   └── ...                 # Other UI primitives
├── forms/                   # Form components
├── layouts/                 # Layout components
└── pages/                   # Page-specific components
```

### Services Layer (`src/services/`)

```
src/services/
├── JwtValidationService.ts  # JWT token validation
├── NotificationService.ts   # Notification handling
├── MediaService.ts         # Media content management
└── AnalyticsService.ts     # Analytics and tracking
```

### Utility Libraries (`src/lib/`)

```
src/lib/
├── auth.ts                 # Authentication utilities
├── auth-config.ts          # Auth configuration
├── api-middleware.ts       # API middleware
├── oauth-error-handler.ts  # OAuth error handling
└── validation/             # Validation utilities
```

## Key Architecture Patterns

### 1. Next.js App Router
- Uses the new App Router for file-based routing
- Server Components by default for better performance
- API routes for backend functionality
- Middleware for authentication and security

### 2. Database Layer
- Prisma ORM for type-safe database operations
- PostgreSQL as the primary database
- Redis for caching and session storage
- Database migrations for schema management

### 3. Authentication
- NextAuth.js for authentication
- JWT tokens for session management
- Role-based access control (RBAC)
- OAuth integration with external providers

### 4. State Management
- React Context for global state
- Custom hooks for business logic
- Server state managed via API calls
- Local component state with useState/useReducer

### 5. API Design
- RESTful API endpoints
- Zod schemas for validation
- Proper error handling and status codes
- Rate limiting and security middleware

## File Naming Conventions

### Components
- PascalCase for component files: `UserProfile.tsx`
- kebab-case for component directories: `user-profile/`
- Index files for clean imports: `index.ts`

### Utilities and Services
- PascalCase for classes: `JwtValidationService.ts`
- camelCase for functions and utilities: `calculateTotal.ts`
- Descriptive names that indicate purpose

### API Routes
- RESTful naming: `/api/users/[userId]/profile`
- HTTP methods handled in route files
- Proper status codes and error responses

## Development Workflow

### Local Development
- Use `npm run dev` for development server
- Turbopack enabled for faster builds
- Hot reload for instant feedback

### Testing
- Jest for unit and integration tests
- React Testing Library for component tests
- API route testing with custom utilities

### Deployment
- Docker containerization
- Environment-based configuration
- Database migrations on deploy
- Health checks and monitoring

## Important Files for AI Understanding

### Core Configuration
- `package.json` - Dependencies and scripts
- `prisma/schema.prisma` - Database schema
- `next.config.ts` - Next.js configuration
- `tailwind.config.js` - Styling configuration

### Business Logic
- `src/lib/auth.ts` - Authentication logic
- `src/services/` - Core business services
- `src/middleware.ts` - Request middleware

### API Documentation
- `docs/openapi.yaml` - API specification
- `docs/api-documentation.md` - API docs
- Individual route files with JSDoc comments

This structure provides a scalable, maintainable foundation for the NWA Media Site platform, enabling efficient content distribution and user engagement tracking.