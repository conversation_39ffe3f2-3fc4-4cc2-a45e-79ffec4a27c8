# Development Best Practices

> Version: 1.0.0
> Last updated: 2025-03-02
> Scope: Global development standards

## Context

This file contains development best practices specifically tailored for the NWA Media Site project. These guidelines are designed for a Next.js 15, TypeScript, and PostgreSQL stack focused on content distribution and user engagement. Individual projects may extend or override these practices in their `.agent-os/product/dev-best-practices.md` file.

## Core Principles

### Keep It Simple
- Implement code in the fewest lines possible
- Avoid over-engineering solutions
- Choose straightforward approaches over clever ones

### Optimize for Readability
- Prioritize code clarity over micro-optimizations
- Write self-documenting code with clear variable names
- Add comments for "why" not "what"

### DRY (Don't Repeat Yourself)
- Extract repeated business logic to custom hooks
- Extract repeated UI markup to reusable components
- Create utility functions for common operations

## Next.js Best Practices

### Server vs Client Components
- Use Server Components by default for better performance
- Use Client Components only when interactivity is required
- Mark component boundaries clearly with 'use client' directive
- Leverage Server Actions for form submissions and data mutations

### API Routes
- Use proper HTTP status codes and error handling
- Implement validation using Zod schemas
- Use middleware for authentication and rate limiting
- Follow RESTful conventions for resource endpoints

### Performance Optimization
- Use Next.js Image component for optimized images
- Implement proper caching strategies with Redis
- Use React.memo and useMemo for expensive computations
- Optimize bundle size by lazy loading components

## Database Best Practices

### Prisma Usage
- Use Prisma Client for all database operations
- Implement proper error handling for database queries
- Use transactions for multi-step operations
- Optimize queries to avoid N+1 problems
- Use Prisma Studio for development and debugging

### Data Validation
- Use Zod schemas for API input validation
- Implement proper TypeScript types for database models
- Validate data at API boundaries
- Sanitize user inputs to prevent security issues

## Content Distribution Practices

### Media Management
- Store media metadata in database, files in appropriate storage
- Implement proper content moderation workflows
- Use Redis for caching frequently accessed content
- Implement proper error handling for media operations

### User Engagement
- Track user interactions for analytics and optimization
- Implement proper notification preferences management
- Use Web Push API for real-time notifications
- Maintain user privacy and consent preferences

## Security Best Practices

### Authentication & Authorization
- Use NextAuth.js for authentication with proper session management
- Implement role-based access control (RBAC)
- Use proper password hashing and validation
- Implement rate limiting for sensitive endpoints

### Data Protection
- Encrypt sensitive data in the database
- Implement proper CORS policies
- Use HTTPS for all production deployments
- Regularly audit and update security dependencies

## Code Organization

### File Structure
- Keep files focused on a single responsibility
- Group related functionality together
- Use consistent naming conventions

### Testing
- Write unit tests for utility functions and custom hooks
- Write integration tests for API routes
- Use React Testing Library for component testing
- Test authentication and authorization flows
- Maintain test coverage above 80% for critical paths
- Use test databases for integration testing

## Development Workflow

### Code Quality
- Use ESLint and TypeScript for code quality
- Run tests before committing code
- Use pre-commit hooks for automated checks
- Review code for security vulnerabilities
- Document complex business logic

### Deployment
- Use Docker for containerization
- Implement proper environment configuration
- Use database migrations for schema changes
- Monitor application performance and errors
- Implement proper logging and error tracking

## Project-Specific Guidelines

### Content Sharing Workflow
- Implement proper validation for content links
- Track sharing activities for analytics
- Handle different media platforms (YouTube, TikTok, Rumble)
- Implement user preference management
- Ensure GDPR compliance for user data

### Analytics and Monitoring
- Track user engagement metrics
- Monitor content distribution effectiveness
- Implement proper error logging
- Use performance monitoring tools
- Regular security audits and updates

---

*These best practices are specifically tailored for the NWA Media Site project. They ensure consistent, secure, and maintainable code across the entire application.*
