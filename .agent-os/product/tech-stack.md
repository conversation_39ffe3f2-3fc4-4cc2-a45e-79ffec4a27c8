# Technical Stack

> Last Updated: 2025-08-16
> Version: 1.0.0

## Application Framework

- **Framework:** Next.js 14 (App Router)
- **Language:** TypeScript
- **Runtime:** Node.js

## Database System

- **ORM:** Prisma
- **Database:** PostgreSQL
- **Connection Pooling:** Prisma built-in pooling

## Frontend Framework

- **Framework:** React (via Next.js)
- **State Management:** React Context API
- **Styling:** Tailwind CSS
- **UI Components:** Custom component library

## Infrastructure

- **Caching:** Redis
- **Authentication:** NextAuth.js
- **Real-time:** Server-Sent Events (SSE)
- **Push Notifications:** Web Push API
- **Email Service:** Nodemailer
- **Containerization:** Docker
- **Deployment:** Docker Compose

## Development Tools

- **Testing:** Jest
- **Code Quality:** ESLint, Prettier
- **Version Control:** Git
- **CI/CD:** GitHub Actions

## Hosting

- **Application Hosting:** Vercel (planned) or self-hosted with Docker
- **Database Hosting:** Self-hosted PostgreSQL or managed service (e.g., Supabase)
- **Asset Hosting:** Integrated with application hosting
- **Caching Hosting:** Self-hosted Redis or managed service

## External Services

- **Media Platforms:** YouTube, TikTok, Rumble (content sources)
- **Email Service:** Nodemailer (for notifications)
- **Analytics:** Custom analytics implementation

## Security

- **Authentication:** NextAuth.js with JWT sessions
- **Authorization:** Role-based access control (RBAC)
- **Data Protection:** Prisma encryption for sensitive fields
- **CORS:** Configured for API routes
- **CSRF Protection:** Custom implementation
- **Rate Limiting:** Custom implementation for login attempts
- **Login Monitoring:** Custom implementation for tracking login attempts

## Performance

- **Caching Strategy:** Redis for session data and API responses
- **Database Optimization:** Prisma query optimization
- **Frontend Optimization:** Next.js built-in optimizations (Image Optimization, Font Optimization)
- **API Performance:** Server-side caching and efficient querying

## Specialized Features

- **Notification System:** Custom implementation for in-app and email notifications
- **Direct Media Platform Integration:** Share and like buttons that link directly to media platforms
- **User Preference Management:** Settings system for notification opt-outs
- **Content Link Tracking:** System for tracking content distribution and completion status
- **Theme Customization:** User interface theme settings (with option to remove theme change button)
- **Activity Logging:** Database logging of user interactions (shares, likes)
- **Analytics Dashboard:** Custom analytics implementation for admin users
- **User Management:** CRUD operations for user accounts with deactivation capability