# Database Schema

This is the database schema implementation for the spec detailed in @.agent-os/specs/2025-08-19-oauth2-integration/spec.md

> Created: 2025-08-19
> Version: 1.0.0

## Schema Changes

### New Tables

1. **OAuthClient** - Store registered OAuth2 clients (remote servers)
2. **OAuthAuthorizationCode** - Temporary storage for authorization codes
3. **OAuthAccessToken** - Store issued access tokens
4. **OAuthRefreshToken** - Store refresh tokens for token renewal

## Table Specifications

### OAuthClient
```prisma
model OAuthClient {
  id            String    @id
  name          String
  secret        String
  redirectUris  String[]  // JSON array stored as string array
  grants        String[]  // Supported grant types
  scope         String[]  // Supported scopes
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}
```

### OAuthAuthorizationCode
```prisma
model OAuthAuthorizationCode {
  code         String   @id
  clientId     String
  userId       String
  redirectUri  String
  scope        String?
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  client       OAuthClient @relation(fields: [clientId], references: [id])
  user         User     @relation(fields: [userId], references: [id])
}
```

### OAuthAccessToken
```prisma
model OAuthAccessToken {
  accessToken  String   @id
  clientId     String
  userId       String
  scope        String?
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  client       OAuthClient @relation(fields: [clientId], references: [id])
  user         User     @relation(fields: [userId], references: [id])
}
```

### OAuthRefreshToken
```prisma
model OAuthRefreshToken {
  refreshToken String   @id
  clientId     String
  userId       String
  scope        String?
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  client       OAuthClient @relation(fields: [clientId], references: [id])
  user         User     @relation(fields: [userId], references: [id])
}
```

## Rationale

- Separate models for each token type to optimize performance and security
- Relations to existing User model to maintain data integrity
- Foreign key constraints through Prisma relations to clean up related tokens when clients are deleted
- Automatic timestamps for audit trails
- Integration with existing NextAuth user model