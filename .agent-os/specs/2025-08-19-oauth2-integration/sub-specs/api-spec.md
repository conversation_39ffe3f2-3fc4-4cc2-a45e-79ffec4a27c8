# API Specification

This is the API specification for the spec detailed in @.agent-os/specs/2025-08-19-oauth2-integration/spec.md

> Created: 2025-08-19
> Version: 1.0.0

## Endpoints

### GET /api/oauth/authorize

**Purpose:** Initiates the OAuth2 authorization flow
**Parameters:** 
- response_type (string, required) - Must be "code"
- client_id (string, required) - Registered client identifier
- redirect_uri (string, required) - Must match registered URI
- scope (string, optional) - Requested permissions
- state (string, recommended) - Random string for CSRF protection

**Response:** HTTP 302 redirect to client's redirect_uri with authorization code
**Errors:** 
- 400 Bad Request - Missing or invalid parameters
- 401 Unauthorized - Invalid client_id
- 403 Forbidden - Redirect URI mismatch

### POST /api/oauth/token

**Purpose:** Exchanges authorization code for access token
**Parameters:** 
- grant_type (string, required) - Must be "authorization_code"
- code (string, required) - Authorization code from /oauth/authorize
- redirect_uri (string, required) - Must match the one used in authorization
- client_id (string, required) - Registered client identifier
- client_secret (string, required) - Client's secret

**Response:** JSON with access_token, token_type, expires_in, refresh_token
**Errors:** 
- 400 Bad Request - Missing or invalid parameters
- 401 Unauthorized - Invalid client credentials
- 403 Forbidden - Expired or invalid authorization code

### GET /api/oauth/userinfo

**Purpose:** Retrieves user information for the authenticated user
**Parameters:** 
- Authorization header with Bearer token

**Response:** JSON with user information (id, email, name, etc.)
**Errors:** 
- 401 Unauthorized - Missing or invalid token
- 403 Forbidden - Expired token

### POST /api/oauth/clients

**Purpose:** Registers a new OAuth2 client (remote server)
**Parameters:** 
- name (string, required) - Client name
- redirectUris (array, required) - Allowed redirect URIs

**Response:** JSON with client_id and client_secret
**Errors:** 
- 400 Bad Request - Missing or invalid parameters
- 409 Conflict - Client already registered

### GET /api/oauth/clients

**Purpose:** Lists all registered OAuth2 clients
**Parameters:** None

**Response:** JSON array of registered clients
**Errors:** 
- 401 Unauthorized - Missing authentication
- 403 Forbidden - Insufficient permissions

### GET /api/oauth/clients/{id}

**Purpose:** Retrieves details for a specific OAuth2 client
**Parameters:** 
- id (path parameter, required) - Client ID

**Response:** JSON with client details
**Errors:** 
- 401 Unauthorized - Missing authentication
- 403 Forbidden - Insufficient permissions
- 404 Not Found - Client not found

### PUT /api/oauth/clients/{id}

**Purpose:** Updates a registered OAuth2 client
**Parameters:** 
- id (path parameter, required) - Client ID
- name (string, optional) - Updated client name
- redirectUris (array, optional) - Updated redirect URIs

**Response:** JSON with updated client details
**Errors:** 
- 400 Bad Request - Missing or invalid parameters
- 401 Unauthorized - Missing authentication
- 403 Forbidden - Insufficient permissions
- 404 Not Found - Client not found

### DELETE /api/oauth/clients/{id}

**Purpose:** Deletes a registered OAuth2 client
**Parameters:** 
- id (path parameter, required) - Client ID

**Response:** 204 No Content
**Errors:** 
- 401 Unauthorized - Missing authentication
- 403 Forbidden - Insufficient permissions
- 404 Not Found - Client not found