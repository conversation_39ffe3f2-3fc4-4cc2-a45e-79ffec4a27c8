# Spec Tasks

These are the tasks to be completed for the spec detailed in @.agent-os/specs/2025-08-23-docker-redis-postgresql-migration/spec.md

> Created: 2025-08-23
> Status: Ready for Implementation

## Tasks

- [ ] 1. Add Docker Compose for app, PostgreSQL, and Redis
  - [ ] 1.1 Write/adjust tests to ensure the health endpoint used by the container is available and returns 200 (e.g., `/api/health`)
  - [ ] 1.2 Create `docker-compose.yml` with services: `app`, `postgres` (16), and `redis` (7) including volumes, networks, and health checks
  - [ ] 1.3 Ensure environment wiring for `DATABASE_URL` (Postgres) and `REDIS_URL` for the app service; include `.env` support
  - [ ] 1.4 Align Dockerfile healthcheck path with an existing route or add a simple `/api/health` route returning `{ ok: true }`
  - [ ] 1.5 Document `docker compose up` usage and common commands in README
  - [ ] 1.6 Verify `docker compose up` starts all services healthy and app is reachable
  - [ ] 1.7 Verify all tests pass

- [ ] 2. Migrate Prisma and env from MySQL to PostgreSQL
  - [ ] 2.1 Add/adjust tests that exercise minimal Prisma interactions (connect, simple CRUD) to catch dialect issues early
  - [ ] 2.2 Update `prisma/schema.prisma` datasource provider from `mysql` to `postgresql`
  - [ ] 2.3 Audit and update any `@db.*` types for Postgres compatibility (e.g., text fields)
  - [ ] 2.4 Update `prisma/migrations/migration_lock.toml` provider to `postgresql` (or recreate via migration commands)
  - [ ] 2.5 Regenerate Prisma client (`npx prisma generate`) and ensure no MySQL-specific references remain in generated outputs
  - [ ] 2.6 Update `.env` and `.env.example` to use a Postgres `DATABASE_URL` (and deprecate/remove `MYSQL_URL` or map it to Postgres)
  - [ ] 2.7 Create a baseline Postgres migration (dev) and apply migrations locally
  - [ ] 2.8 Run the application locally and smoke test core endpoints that hit the DB (e.g., register, videos, notifications)
  - [ ] 2.9 Verify all tests pass

- [ ] 3. Update CI (GitHub Actions) for Postgres + Redis
  - [ ] 3.1 Update workflow services: replace MySQL with `postgres:16` and add `redis:7`
  - [ ] 3.2 Update health checks (use `pg_isready` for Postgres) and environment setup (`DATABASE_URL` Postgres format)
  - [ ] 3.3 Add steps to run `prisma generate` and migrations before tests
  - [ ] 3.4 Ensure REDIS_URL is configured for tests if needed
  - [ ] 3.5 Confirm all MySQL-specific steps/variables are removed from CI
  - [ ] 3.6 Verify CI can run tests locally or via a dry-run and ensure parity with local compose
  - [ ] 3.7 Verify all tests pass

- [x] 4. Remove/replace MySQL-specific references across the codebase
  - [x] 4.1 Write a small script/check (or test) that fails if `mysql` appears in non-generated sources (excluding migrations history if intentionally archived)
  - [x] 4.2 Update any documentation files that mention MySQL connection strings
  - [x] 4.3 Update package scripts (e.g., `db:migrate`, `db:generate`, `db:reset`, `db:seed`) to the Postgres flow if necessary
  - [x] 4.4 Rebuild Prisma client and clean up generated artifacts referencing MySQL
  - [x] 4.5 Verify grep shows no remaining `mysql` references in env, CI, or source (excluding allowed generated caches)
  - [x] 4.6 Verify all tests pass

- [x] 5. Documentation and migration guidance
  - [x] 5.1 Update README with Docker/Compose instructions and Postgres transition notes
  - [x] 5.2 Provide a brief guide for migrating existing data from MySQL to Postgres (e.g., `pgloader` or export/import), including caveats
  - [x] 5.3 Document rollback steps and how to switch back temporarily if needed (development only)
  - [x] 5.4 Verify documentation accuracy by walking through steps end-to-end
  - [x] 5.5 Verify all tests pass
