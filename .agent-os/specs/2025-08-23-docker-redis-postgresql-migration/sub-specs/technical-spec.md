# Technical Specification

This is the technical specification for the spec detailed in @.agent-os/specs/2025-08-23-docker-redis-postgresql-migration/spec.md

> Created: 2025-08-23
> Version: 1.0.0

## Technical Requirements

- Containerization
  - Dockerfile for Next.js app using multi-stage build (builder + runtime) with Node 18+.
  - docker-compose.yml with services: app, postgres, redis.
  - Health checks for postgres and redis; app depends_on wait-for.
- PostgreSQL Migration
  - Prisma datasource provider changed to `postgresql`.
  - Update Prisma field annotations using @db types compatible with Postgres.
  - Regenerate Prisma client and migrations for Postgres.
  - Update DATABASE_URL format to Postgres connection string in `.env` and Compose.
  - Ensure any raw SQL or MySQL-specific constructs are replaced with Postgres-safe alternatives.
- Redis Integration
  - Ensure `src/lib/redis.ts` reads REDIS_URL from env and connects to compose redis.
  - Provide default REDIS_URL in docker-compose and `.env.example`.
- CI/CD
  - Update GitHub Actions to run tests against Postgres service container.
  - Cache Prisma client generation where feasible.
- Developer Workflow
  - Scripts for `npm run db:migrate`, `npm run db:generate`, `npm run db:seed` working in containers.
  - Document workflows in README: build, migrate, seed, run.

## Approach Options

Option A: Convert in-place from MySQL to Postgres with Prisma
- Pros: Minimal code churn, leverage Prisma portability
- Cons: Need to audit @db.* types and uniqueness/indexes specifics

Option B: Create a new Postgres schema and perform data transfer tool-assisted (e.g., pgloader)
- Pros: Clean slate for Postgres types, fewer hidden incompatibilities
- Cons: Requires data migration planning, downtime window

Selected: Option A (in-place conversion) for speed and reduced disruption.

Rationale: Prisma models are largely cross-dialect compatible; project tech stack already specifies Postgres; we can update provider and fix any MySQL-specific types.

## External Dependencies

- Docker and Docker Compose
- PostgreSQL 16 image (official)
- Redis 7 image (official)
- Node 18/20 Alpine for app container
- wait-for-it or dockerize for service readiness (or simple retry in app startup)
