# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-08-23-docker-redis-postgresql-migration/spec.md

> Created: 2025-08-23
> Version: 1.0.0

## Test Coverage

### Unit Tests

- Prisma models compatibility (via type generation and minimal query smoke tests)
- Redis connection utility in `src/lib/redis.ts` picks up REDIS_URL and reconnects on error

### Integration Tests

- App starts and connects to Postgres and Redis in docker-compose
- Prisma migrations apply successfully against Postgres
- Selected API routes that touch DB (e.g., register, videos, notifications) succeed with Postgres

### Mocking Requirements

- For CI environments without Docker services, use GitHub Actions service containers for Postgres and Redis; provide test env vars
- Mock or stub external HTTP calls if any during integration tests
