# Database Schema

This is the database schema implementation for the spec detailed in @.agent-os/specs/2025-08-23-docker-redis-postgresql-migration/spec.md

> Created: 2025-08-23
> Version: 1.0.0

## Changes

- Change Prisma datasource provider from `mysql` to `postgresql` in `prisma/schema.prisma`.
- Review and adjust any `@db.*` field annotations that are MySQL-specific to Postgres-friendly equivalents (e.g., long text fields can be left without @db annotation or use `@db.Text` which maps to Postgres `TEXT`).
- Recreate migrations for Postgres: `npx prisma migrate reset` (local dev) to generate baseline Postgres migration. Preserve production migration plan separately.
- Update unique indexes and index names where MySQL-specific naming existed (Prisma handles but we must verify collisions/length limits).

## Specifications

- Prisma datasource:
  ```prisma
  datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
  }
  ```

- Example @db mappings:
  - `String @db.Text` => Postgres TEXT
  - Remove MySQL-only types (e.g., `@db.LongText`) if present
  - DateTime defaults `@default(now())` are supported on Postgres
  - `@updatedAt` supported

- Indexes and Constraints:
  - Preserve model-level `@@unique` and `@@index` declarations; Prisma will translate them to Postgres.
  - Ensure foreign keys specify `onDelete: Cascade` as intended.

## Rationale

- Align with documented tech stack (PostgreSQL)
- Unified local and CI environment using Postgres
- Reduce dialect-specific bugs moving forward
