# API Specification

This is the API specification for the spec detailed in @.agent-os/specs/2025-08-19-oauth-member-portal-integration/spec.md

> Created: 2025-08-19
> Version: 1.0.0

## Endpoints

### Middleware Redirect Logic

**Purpose:** Automatically redirect unauthenticated users to the Member Portal OAuth flow
**Path:** All application routes (handled by Next.js middleware)
**Method:** GET
**Parameters:** 
- Original URL (preserved in callbackUrl parameter)

**Response:** HTTP 307 redirect to NextAuth signIn endpoint
**Errors:** 
- 500 Internal Server Error - Middleware configuration issues

### Authentication Callback

**Purpose:** Handle successful authentication and redirect to original destination
**Path:** /api/auth/callback/member-portal (handled by NextAuth)
**Method:** GET
**Parameters:** 
- code (string) - Authorization code from Member Portal
- state (string) - State parameter for CSRF protection
- callbackUrl (string) - Original destination URL

**Response:** HTTP 302 redirect to original destination or dashboard
**Errors:** 
- 400 Bad Request - Invalid authorization code
- 401 Unauthorized - Authentication failed
- 500 Internal Server Error - Server-side processing errors

### Error Handling

**Purpose:** Display authentication errors to users
**Path:** /auth/error (handled by NextAuth)
**Method:** GET
**Parameters:** 
- error (string) - Error code from authentication provider

**Response:** Error page with user-friendly message
**Errors:** 
- OAuthAccountNotLinked - Account already linked with another profile
- Default - Generic authentication error