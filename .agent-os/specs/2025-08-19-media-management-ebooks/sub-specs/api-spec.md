# API Specification

This is the API specification for the spec detailed in @.agent-os/specs/2025-08-19-media-management-ebooks/spec.md

> Created: 2025-08-19  
> Version: 1.0.0

## Endpoints

### POST /api/media

**Purpose:** Create a new media item (video or ebook).  
**Parameters:** type (video/ebook), title, description, fileUrl (for ebooks), videoUrl (for videos), releaseTime (for ebooks), published (boolean)  
**Response:** Media item object  
**Errors:** Validation errors, file upload errors

### GET /api/media

**Purpose:** List all media items (videos and ebooks).  
**Parameters:** type (optional: filter by video/ebook), published (optional: filter by publish status)  
**Response:** Array of media items  
**Errors:** None

### PATCH /api/media/:id

**Purpose:** Update a media item.  
**Parameters:** id, updated fields (including releaseTime, published)  
**Response:** Updated media item  
**Errors:** Not found, validation errors

### DELETE /api/media/:id

**Purpose:** Delete a media item.  
**Parameters:** id  
**Response:** Success/failure  
**Errors:** Not found

### POST /api/media/:id/like

**Purpose:** Like a media item.  
**Parameters:** id, userId  
**Response:** Success/failure  
**Errors:** Not found, already liked

### POST /api/media/:id/share

**Purpose:** Share a media item.  
**Parameters:** id, userId  
**Response:** Success/failure, leaderboard points awarded  
**Errors:** Not found, already shared

### POST /api/media/:id/completed

**Purpose:** Mark media item as completed.  
**Parameters:** id, userId  
**Response:** Success/failure  
**Errors:** Not found, already completed

### PATCH /api/media/:id/publish

**Purpose:** Toggle publish/unpublish status for a media item (admin only).  
**Parameters:** id, published (boolean)  
**Response:** Updated media item  
**Errors:** Not found, permission denied

### PATCH /api/media/:id/release-time

**Purpose:** Update scheduled release time for an ebook (admin only).  
**Parameters:** id, releaseTime (datetime)  
**Response:** Updated media item  
**Errors:** Not found, permission denied