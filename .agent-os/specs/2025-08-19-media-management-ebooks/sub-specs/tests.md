# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-08-19-media-management-ebooks/spec.md

> Created: 2025-08-19  
> Version: 1.0.0

## Test Coverage

### Unit Tests

**Media Model**
- Should create media items with type video or ebook
- Should validate PDF file input for ebooks
- Should set and update releaseTime for ebooks
- Should toggle published/unpublished status

**Leaderboard Logic**
- Should award points for sharing media items

### Integration Tests

**Media Management API**
- Should allow CRUD operations for videos and ebooks
- Should handle user interactions (like, share, completed) for both media types
- Should restrict visibility of unpublished ebooks to admins
- Should enforce scheduled release logic

### Feature Tests

**Ebooks Tab**
- Should display only published ebooks to regular users
- Should allow admins to see all ebooks
- Should allow admins to schedule release and toggle publish status
- Should update leaderboard on share

### Mocking Requirements

- **PDF Validation:** Mock file upload and validation
- **Leaderboard Service:** Mock points awarding logic
- **User Authentication:** Mock user sessions for interaction tests
- **Time-based Logic:** Mock current time for scheduled release tests