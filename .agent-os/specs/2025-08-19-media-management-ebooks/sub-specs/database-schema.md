# Database Schema

This is the database schema implementation for the spec detailed in @.agent-os/specs/2025-08-19-media-management-ebooks/spec.md

> Created: 2025-08-19  
> Version: 1.0.0

## Changes

- Add "Media" table to unify videos and ebooks.
- Add "type" column to distinguish between "video" and "ebook".
- Add "fileUrl" column for ebook PDF storage.
- Add "releaseTime" column for scheduled ebook release (datetime).
- Add "published" boolean column to control ebook visibility.
- Update relationships for user interactions (like, share, completed) to reference media items.

## Specifications

- Prisma migration to create/modify tables and columns.
- Indexes on "type", "fileUrl", "releaseTime", and "published" for efficient queries.
- Foreign key relationships for user interactions.

## Rationale

- Unifies media management for scalability.
- Supports new ebook functionality with scheduled release and visibility control.
- Ensures data integrity and performance for both media types.