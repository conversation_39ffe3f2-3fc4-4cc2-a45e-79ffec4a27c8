# Spec Requirements Document

> Spec: media-management-ebooks  
> Created: 2025-08-19  
> Status: Planning

## Overview

Add an "NWA Ebooks" tab to the media section, supporting PDF uploads and user interactions (like, share, mark as completed) with leaderboard points for sharing. Unify video and ebook management under "Media Management."  
Admins can schedule ebook release times and control publish/unpublish status, making ebooks visible only to admins until published.

## User Stories

### Media Admin: Manage Ebooks

As an admin, I want to upload and manage ebook files (PDF) alongside videos, schedule their release, and control publish status, so that I can prepare content in advance and control visibility.

Admins can add, edit, remove, schedule release, and publish/unpublish ebooks, validate file input, and send notifications to users about new ebooks.

### User: Interact with Ebooks

As a user, I want to view, like, share, and mark published ebooks as completed, so that I can contribute to NWA's outreach and earn leaderboard points.

Users see only published ebooks in a dedicated tab, interact with them as with videos, and earn points for sharing.

## Spec Scope

1. **Media Management Unification** – Replace "Video Management" with "Media Management" to support both videos and ebooks.
2. **Ebook Support** – Allow admins to upload, validate, and manage PDF ebook files.
3. **Scheduled Release** – Enable admins to set a release time for ebooks.
4. **Publish/Unpublish Toggle** – Allow admins to control ebook visibility; unpublished ebooks are visible only to admins.
5. **User Interactions** – Enable like, share, and completed actions for published ebooks, identical to videos.
6. **Leaderboard Integration** – Award points for sharing ebooks, tracked in the leaderboard.

## Out of Scope

- Support for ebook formats other than PDF (e.g., EPUB).
- Advanced ebook reader functionality (e.g., in-browser reading).
- Changes to existing video interaction logic.

## Expected Deliverable

1. Media Management interface supporting both videos and ebooks.
2. Ebooks tab with like, share, completed, and leaderboard integration.
3. Admin ability to upload/manage PDF ebooks with input validation.
4. Scheduled release and publish/unpublish controls for ebooks.
5. Only published ebooks visible to regular users; admins see all.
## Spec Documentation

- Tasks: @.agent-os/specs/2025-08-19-media-management-ebooks/tasks.md
- Technical Specification: @.agent-os/specs/2025-08-19-media-management-ebooks/sub-specs/technical-spec.md
- API Specification: @.agent-os/specs/2025-08-19-media-management-ebooks/sub-specs/api-spec.md
- Database Schema: @.agent-os/specs/2025-08-19-media-management-ebooks/sub-specs/database-schema.md
- Tests Specification: @.agent-os/specs/2025-08-19-media-management-ebooks/sub-specs/tests.md