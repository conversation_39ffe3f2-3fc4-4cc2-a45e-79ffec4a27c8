# Spec Tasks

These are the tasks to be completed for the spec detailed in @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/spec.md

> Created: 2025-08-19
> Status: Ready for Implementation

## Tasks

- [x] 5. Monitoring & Structured Logging for Permissions Endpoint
  - [x] 5.1 Add structured logging (route, status, request id) without secrets for /api/permissions
  - [x] 5.2 Integrate with ApplicationMonitor.recordApiResponseTime and recordError
  - [x] 5.3 (Optional) Apply a strict rate limiter to /api/permissions
  - [x] 5.4 Verify tests pass

- [x] 6. CI & Developer Experience
  - [x] 6.1 Add README snippet with cURL example for /api/permissions (Authorization: Bearer)
  - [x] 6.2 Ensure jest picks up new test path; run full suite
  - [x] 6.3 Provide smoke-test instructions in README



- [x] 1. Permissions Catalog Endpoint (GET /api/permissions)
  - [x] 1.1 Write tests for Authorization header validation (missing/invalid should return 401 with `{ error: "Invalid bearer token", code: "UNAUTHORIZED" }`)
  - [x] 1.2 Write tests for successful response (200) returning exact JSON shape:
        `{ roles: ["USER","ADMIN","NWA_TEAM"], permissions: ["view_document","send_documents","snooze_notifications","view_notifications"] }`
  - [x] 1.3 Implement route handler at `src/app/api/permissions/route.ts` with Authorization: Bearer token check using `process.env.PERMISSIONS_API_TOKEN`
  - [x] 1.4 Add minimal request logging (without secrets) and ensure correct content-type and cache headers
  - [x] 1.5 Verify all tests pass

- [x] 2. Sign In Flow Verification (no new code expected)
  - [x] 2.1 Ensure Sign In button invokes `signIn('member-portal', { callbackUrl })` in header and `/auth/signin` page (adjust tests if needed)
  - [x] 2.2 Verify middleware public route config includes `/auth/signin` and `/api/auth/signin/member-portal`, and redirects unauthenticated protected routes to Member Portal with `callbackUrl`
  - [x] 2.3 Run existing auth-related tests and adjust if they assert old behavior
  - [x] 2.4 Verify all tests pass

- [x] 3. Configuration & Documentation
  - [x] 3.1 Document required environment variable `PERMISSIONS_API_TOKEN` and example usage of Authorization: Bearer header
  - [x] 3.2 Confirm `.env.local` includes: `MEMBER_PORTAL_URL`, `CLIENT_ID`, `CLIENT_SECRET`, `MEMBER_PORTAL_API_KEY`, `NEXTAUTH_SECRET`, `NEXTAUTH_URL`, `PERMISSIONS_API_TOKEN`
  - [x] 3.3 Add a short section to existing OAuth docs to mention `/api/permissions` contract (roles list, permissions list)
  - [x] 3.4 Verify build and type checks

- [x] 4. Optional: CORS Restriction for `/api/permissions`
  - [x] 4.1 If required, restrict CORS to the Member Portal origin derived from `MEMBER_PORTAL_URL`
  - [x] 4.2 Add tests (if implemented) to ensure other origins are blocked or receive appropriate headers
  - [x] 4.3 Verify all tests pass
