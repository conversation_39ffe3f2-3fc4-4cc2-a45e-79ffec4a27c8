# Spec Requirements Document

> Spec: OAuth Permissions and Sign In
> Created: 2025-08-19
> Status: Planning

## Overview

Ensure the front-page Sign In button initiates OAuth2 authentication against the remote Member Portal using environment-configured values, and add a Permissions API that exposes the available roles and permissions (catalog only). The Member Portal will use this endpoint to understand our app's roles/permissions. No user-specific permissions or scope endpoints are included in this spec.

## User Stories

### Seamless Sign In via Member Portal

As a user, I want the Sign In button to authenticate me via the Member Portal OAuth flow so I can access the site with my assigned role and permissions.

- Sign In redirects to Member Portal authorize endpoint with proper client_id, scope, and redirect_uri.
- On success, the user is returned and logged in with role and scope applied.

### Permissions Catalog for Member Portal

As the Member Portal (OAuth provider), I want to fetch the available roles and permissions for this app so I can determine and mint appropriate scopes for users.

- A GET endpoint returns roles, permissions, and role-to-permission mappings.
- Protected by API key so only trusted clients (Member Portal) can use it.

<!-- User Scope Retrieval removed per clarification -->

## Spec Scope

1. Front-page Sign In must initiate OAuth flow with Member Portal via NextAuth provider (id: "member-portal").
2. Permissions Catalog API (GET /api/permissions): returns roles and permissions. Secured with Authorization: Bearer <token>.
3. Documentation of .env requirements and callback behavior.

## Out of Scope

- Changes to Member Portal provider behavior beyond using provided endpoints.
- OpenID Connect extensions or JWT signing changes.
- Database migrations for RBAC (static mapping only for this spec).

## Expected Deliverable

1. Front-page Sign In reliably redirects to Member Portal OAuth authorize and completes login.
2. GET /api/permissions returns roles, permissions, and rolePermissions with API key security.
3. Tests covering endpoints, middleware redirects, and sign-in flow behavior.

## Spec Documentation

- Technical Specification: @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/sub-specs/technical-spec.md
- API Specification: @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/sub-specs/api-spec.md
- Tests Specification: @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/sub-specs/tests.md
