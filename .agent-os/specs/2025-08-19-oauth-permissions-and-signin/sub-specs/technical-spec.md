# Technical Specification

This is the technical specification for the spec detailed in @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/spec.md

> Created: 2025-08-19
> Version: 1.0.0

## Current State Summary (validated from codebase)

- OAuth provider configured in NextAuth at `src/lib/auth.ts` with id `member-portal`:
  - authorization: `${process.env.MEMBER_PORTAL_URL}/oauth/authorize`
  - token: `${process.env.MEMBER_PORTAL_URL}/oauth/token` (custom request using MEMBER_PORTAL_API_KEY)
  - userinfo: `${process.env.MEMBER_PORTAL_URL}/oauth/userinfo`
  - clientId: `process.env.CLIENT_ID`, clientSecret: `process.env.CLIENT_SECRET`
  - profile maps `role` from `userinfo` to session; no explicit `scope` mapping yet
- Sign In triggers:
  - Header button in `src/components/layout-client.tsx` calls `signIn('member-portal', { callbackUrl })`.
  - Dedicated page in `src/app/auth/signin/page.tsx` calls `signIn('member-portal', ...)`.
- Middleware redirects unauthenticated traffic to `/api/auth/signin/member-portal` (NextAuth handles this built-in).

Conclusion: Sign In is already wired to the Member Portal OAuth provider using env vars. This spec will formalize behavior and add Permissions APIs and scope mapping.

## Environment Variables

- MEMBER_PORTAL_URL: Base URL of the Member Portal (provider)
- CLIENT_ID: OAuth client id issued by Member Portal
- CLIENT_SECRET: OAuth client secret
- MEMBER_PORTAL_API_KEY: API key for token exchange (if provider requires)
- NEXTAUTH_SECRET: NextAuth secret
- NEXTAUTH_URL: Base URL of this app (for callback uri construction)
- PERMISSIONS_API_TOKEN: Bearer token that Member Portal must present when calling GET /api/permissions

## OAuth Flow Behavior

- Clicking Sign In should call `signIn('member-portal', { callbackUrl })`.
- NextAuth constructs the authorize request to `${MEMBER_PORTAL_URL}/oauth/authorize` with `client_id`, `redirect_uri`, and `scope` from provider config.
- On callback, token request uses custom `request` to exchange code for tokens (Bearer MEMBER_PORTAL_API_KEY if required).
- userinfo fetch returns user details and should ideally include `role` and optionally `scope`.

## Roles

- Use existing enum in Prisma schema: Role = { USER, ADMIN, NWA_TEAM }
- Permissions catalog is independent of the enum but should reflect what the app requires to function.

## Permissions Catalog

- Permissions identifiers (snake_case, examples):
  - view_document
  - send_documents
  - snooze_notifications
  - view_notifications

- For this spec, we only expose the list of permissions and the list of roles. No per-user scope handling or mapping output is required, and no additional database tables are added.

## Security

- GET /api/permissions requires header `Authorization: Bearer ${PERMISSIONS_API_TOKEN}`. Return 401 if absent or mismatched.
- CORS for `/api/permissions` can be restricted if needed to the Member Portal origin.

## Front-end Integration

- Keep Sign In button visible on front page header (already present in `layout-client.tsx`).
- Ensure any buttons in `src/app/page.tsx` that link to `/auth/signin` still reach the Sign In page (which triggers `signIn('member-portal')`).
- No UI change is required beyond verifying this flow.

## Error Handling

- Redirect failures land on `/auth/error` with appropriate messaging.
- `/api/permissions` endpoints return structured JSON errors: { error: string, code: string } with appropriate HTTP status.

## Telemetry/Logging

- Log `/api/permissions` access with request id, caller IP, and result (2xx/4xx) without logging secrets.
- Log sign-in attempts and failures via existing error tracking utilities.
