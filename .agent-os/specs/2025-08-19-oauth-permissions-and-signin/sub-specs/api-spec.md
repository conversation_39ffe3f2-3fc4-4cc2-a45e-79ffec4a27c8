# API Specification

This is the API specification for the spec detailed in @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/spec.md

> Created: 2025-08-19
> Version: 1.0.0

## Endpoints

### GET /api/permissions

Purpose: Provide the Member Portal with available roles and permissions (catalog only). No user-specific data is returned.

Security:
- Requires header `Authorization: Bearer ${PERMISSIONS_API_TOKEN}`. Otherwise 401 Unauthorized.

Response 200:
```
{
  "roles": ["USER", "ADMIN", "NWA_TEAM"],
  "permissions": [
    "view_document",
    "send_documents",
    "snooze_notifications",
    "view_notifications"
  ]
}
```

Errors:
- 401 Unauthorized: { "error": "Invalid bearer token", "code": "UNAUTHORIZED" }

---

<!-- Removed /api/permissions/me per clarification -->

### OAuth Provider Touchpoints (for reference)

- Authorization: `${MEMBER_PORTAL_URL}/oauth/authorize`
- Token: `${MEMBER_PORTAL_URL}/oauth/token`
- Userinfo: `${MEMBER_PORTAL_URL}/oauth/userinfo`

Our app acts as OAuth client; NextAuth handles these routes under `/api/auth/*`.
