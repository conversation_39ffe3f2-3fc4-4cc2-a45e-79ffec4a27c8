# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-08-19-oauth-permissions-and-signin/spec.md

> Created: 2025-08-19
> Version: 1.0.0

## Test Coverage

### Unit Tests

- Permissions mapping utility returns correct permissions per role.
- `/api/permissions` rejects missing/invalid Bearer token (Authorization header) with 401.
- `/api/permissions` returns roles and permissions with 200 when API key valid.

### Integration Tests

- Sign In page calls `signIn('member-portal')` with callbackUrl from query or defaults to `/dashboard`.
- Header Sign In button triggers `signIn('member-portal', { callbackUrl })`.
- Middleware redirects unauthenticated protected route access to `/api/auth/signin/member-portal` with `callbackUrl` param.

### Feature/E2E Tests (if infra available)

- End-to-end OAuth flow succeeds from Sign In button to Member Portal and back.
- After login, `/api/permissions/me` returns the expected role and scope.

### Mocking Requirements

- Mock NextAuth `signIn` in Sign In page tests.
- Mock `getToken` for middleware tests.
- Mock session for `/api/permissions/me` tests.

