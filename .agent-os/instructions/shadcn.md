---
description: Shadcn UI Component Integration for NWA Media Site
globs:
alwaysApply: false
version: 1.0
encoding: UTF-8
---

# Shadcn UI Integration Guidelines for NWA Media Site

## Overview

<purpose>
  - Integrate Shadcn UI components into the NWA Media Site project
  - Ensure consistent design system aligned with NWA's brand and mission
  - Provide accessible, responsive components for content distribution features
</purpose>

<context>
  - NWA Media Site uses Next.js 15, TypeScript, and TailwindCSS
  - Components must support content sharing, analytics, and user management features
  - Design should be clean, professional, and accessible for educational content
</context>

## Component Integration Rules

### When to Use Shadcn Components

<usage_guidelines>
  - **Always check existing components first** - Use existing custom components before adding new Shadcn components
  - **Use shadcn MCP tool for latest information** - Query the shadcn MCP server to get current component versions and documentation
  - **Check color scheme compatibility** - Ensure new components match the existing NWA Media Site color palette
  - **Prioritize accessibility** - All components must meet WCAG 2.1 AA standards for NWA's educational content
  - **Test responsive behavior** - Components must work across all device sizes for global content distribution
</usage_guidelines>

### Component Categories for NWA Media Site

<component_categories>
  <form_components>
    - Input fields for user registration and content submission
    - Select dropdowns for content categorization
    - Textarea for content descriptions and user feedback
    - Checkbox and radio groups for user preferences
  </form_components>

  <navigation_components>
    - Breadcrumb navigation for content hierarchy
    - Pagination for content listings and analytics
    - Tabs for switching between different content views
    - Navigation menu for admin dashboard sections
  </navigation_components>

  <data_display>
    - Table components for user management and content analytics
    - Card components for content preview and sharing
    - Badge components for content status and user roles
    - Chart components for analytics dashboard (using recharts)
  </data_display>

  <feedback_components>
    - Alert components for success/error messages
    - Toast notifications for user actions
    - Progress indicators for content sharing workflows
    - Skeleton loaders for content loading states
  </feedback_components>

  <layout_components>
    - Dialog modals for content sharing and user interactions
    - Sheet components for mobile navigation and side panels
    - Popover components for user menus and quick actions
    - Collapsible components for expandable content sections
  </layout_components>
</component_categories>

## Implementation Workflow

<implementation_steps>
  1. **Check Existing Components**
     - Review src/components/ directory for existing implementations
     - Check if component already exists with NWA-specific styling

  2. **Query Shadcn MCP Tool**
     - Use shadcn MCP server to get latest component information
     - Check for TypeScript support and accessibility features
     - Review installation requirements and dependencies

  3. **Design System Integration**
     - Ensure component matches existing color scheme from .env and TailwindCSS
     - Apply NWA Media Site branding and styling conventions
     - Test component responsiveness across different screen sizes

  4. **Accessibility and Security**
     - Implement proper ARIA labels for screen readers
     - Ensure keyboard navigation support
     - Add proper error handling and validation
     - Follow security best practices for user data handling

  5. **Testing and Documentation**
     - Write unit tests for component functionality
     - Add integration tests for component interactions
     - Document component usage in project documentation
     - Update component library documentation
</implementation_steps>

## Color Scheme and Theming

<color_considerations>
  - **Primary Colors**: Use NWA's brand colors for primary actions and links
  - **Semantic Colors**: Maintain consistent success, warning, error, and info colors
  - **Dark Mode**: Support both light and dark themes for user preference
  - **Contrast**: Ensure minimum 4.5:1 contrast ratio for accessibility
  - **Brand Consistency**: All components should align with NWA's educational mission
</color_considerations>

## Performance Considerations

<performance_guidelines>
  - **Bundle Size**: Monitor impact on Next.js bundle size
  - **Lazy Loading**: Implement lazy loading for heavy components
  - **Tree Shaking**: Ensure unused component parts are eliminated
  - **Caching**: Leverage Next.js caching strategies for component assets
  - **Mobile Optimization**: Optimize for mobile content sharing workflows
</performance_guidelines>

## Security and Privacy

<security_requirements>
  - **Data Sanitization**: Sanitize all user inputs using DOMPurify
  - **XSS Protection**: Implement proper XSS prevention measures
  - **CSRF Protection**: Include CSRF tokens in forms and API calls
  - **Privacy Compliance**: Ensure GDPR compliance for user data handling
  - **Content Security**: Implement CSP headers for component security
</security_requirements>

## Component Documentation

<documentation_standards>
  - **JSDoc Comments**: Document all component props and usage
  - **Storybook**: Create Storybook stories for component development
  - **Usage Examples**: Provide practical examples for NWA Media Site use cases
  - **Migration Guide**: Document migration from existing components
  - **Changelog**: Track component updates and breaking changes
</documentation_standards>

## Integration with Existing Systems

<integration_points>
  - **Database**: Components should work with Prisma ORM and PostgreSQL
  - **Authentication**: Integrate with NextAuth.js for user management
  - **API Routes**: Connect to existing API endpoints for data operations
  - **Real-time**: Support Server-Sent Events for live content updates
  - **Analytics**: Integrate with content sharing analytics tracking
</integration_points>

## Quality Assurance

<testing_requirements>
  - **Unit Tests**: Test component logic and interactions
  - **Integration Tests**: Test component integration with NWA features
  - **Visual Tests**: Ensure consistent appearance across browsers
  - **Accessibility Tests**: Verify WCAG compliance
  - **Performance Tests**: Monitor component rendering performance
</testing_requirements>

## Maintenance and Updates

<maintenance_guidelines>
  - **Version Management**: Keep Shadcn components updated with latest versions
  - **Breaking Changes**: Plan for breaking changes in major updates
  - **Deprecation**: Gradually migrate from deprecated components
  - **Community Contributions**: Monitor Shadcn community for improvements
  - **Security Updates**: Apply security patches promptly
</maintenance_guidelines>

## Usage Instructions

<usage_instructions>
  When implementing Shadcn UI components in NWA Media Site:

  1. **First**, check existing components in `src/components/` directory
  2. **Then**, use the shadcn MCP tool to get latest component information:
     ```
     @use_mcp_tool(shadcn-ui, get_component, {componentName: "component-name"})
     ```
  3. **Next**, verify color scheme compatibility with existing NWA design
  4. **Then**, implement component with proper TypeScript types and accessibility
  5. **Finally**, test component integration with NWA Media Site features

  Always prioritize existing custom components over new Shadcn additions unless significant improvements are gained.
</usage_instructions>