'use client';

import { useState, useEffect, useCallback } from 'react';
import { useApiRetry } from './useRetry';
import { useRequestCache, generateCacheKey } from './useRequestCache';

interface UserInteractions {
  [videoId: string]: {
    [platform: string]: {
      liked: boolean;
      shared: boolean;
    };
  };
}

interface ApiErrorResponse {
  error?: {
    message?: string;
  };
  message?: string;
}

interface InteractionsApiResponse {
  interactions?: UserInteractions;
  success?: boolean;
}

interface UseVideoInteractionsReturn {
  userInteractions: UserInteractions;
  isLoading: boolean;
  error: string | null;
  handlePlatformClick: (videoId: string, platform: string, action: 'like' | 'share') => Promise<void>;
  refreshInteractions: (videoIds: string[]) => Promise<void>;
}

export function useVideoInteractions(videoIds: string[]): UseVideoInteractionsReturn {
  const [userInteractions, setUserInteractions] = useState<UserInteractions>({});
  
  // Initialize request cache with shorter TTL for interactions (2 minutes)
  const cache = useRequestCache<UserInteractions>({
    ttl: 2 * 60 * 1000, // 2 minutes
    maxSize: 50,
  });

  // Fetch user interactions API call with caching
  const fetchInteractionsApi = useCallback(async (...args: unknown[]) => {
    const ids = args[0] as string[];
    if (ids.length === 0) return {};

    // Sort IDs to ensure consistent cache keys
    const sortedIds = [...ids].sort();
    const cacheKey = generateCacheKey('/api/engagement/user-interactions', { videoIds: sortedIds.join(',') });
    
    return cache.executeWithCache(cacheKey, async () => {
      const response = await fetch(`/api/engagement/user-interactions?videoIds=${sortedIds.join(',')}`);
      
      if (!response.ok) {
        let errorMessage = 'Failed to fetch user interactions';
        try {
          const errorData: ApiErrorResponse = await response.json();
          errorMessage = errorData.error?.message || errorData.message || errorMessage;
        } catch {
          // If response.json() fails, use status text as fallback
          errorMessage = `HTTP ${response.status}: ${response.statusText || errorMessage}`;
        }
        throw new Error(errorMessage);
      }

      let data: InteractionsApiResponse;
      try {
        data = await response.json();
      } catch {
        throw new Error('Invalid response format from interactions API');
      }
      
      const interactions = data.interactions || {};
      setUserInteractions(prev => ({
        ...prev,
        ...interactions,
      }));
      
      return interactions;
    });
  }, [cache]);

  // Use retry mechanism for fetching interactions
  const {
    execute: fetchInteractions,
    isLoading,
    error
  } = useApiRetry(fetchInteractionsApi, {
    maxRetries: 2,
    retryDelay: 500,
    onRetry: (attempt, error) => {
      console.log(`Retrying interactions fetch (${attempt}/2):`, (error as Error).message);
    }
  });

  // Refresh interactions for specific video IDs
  const refreshInteractions = useCallback(async (ids: string[]) => {
    await fetchInteractions(ids);
  }, [fetchInteractions]);

  // Handle platform click (like/share) with retry
  const trackEngagementApi = useCallback(async (...args: unknown[]) => {
    const [videoId, platform, action] = args as [string, string, 'like' | 'share'];
    const response = await fetch('/api/engagement/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoId,
        platform,
        action,
      }),
    });

    if (!response.ok) {
      let errorMessage = 'Failed to track engagement';
      try {
        const errorData: ApiErrorResponse = await response.json();
        errorMessage = errorData.error?.message || errorData.message || errorMessage;
      } catch {
        // If response.json() fails, use status text as fallback
        errorMessage = `HTTP ${response.status}: ${response.statusText || errorMessage}`;
      }
      throw new Error(errorMessage);
    }

    try {
      return await response.json();
    } catch {
      throw new Error('Invalid response format from engagement tracking API');
    }
  }, []);

  const {
    execute: trackEngagement
  } = useApiRetry(trackEngagementApi, {
    maxRetries: 2,
    retryDelay: 300,
    onRetry: (attempt, error) => {
      console.log(`Retrying engagement tracking (${attempt}/2):`, error instanceof Error ? error.message : String(error));
    }
  });

  const handlePlatformClick = useCallback(async (
    videoId: string, 
    platform: string, 
    action: 'like' | 'share'
  ) => {
    try {
      // Optimistically update the UI
      setUserInteractions(prev => {
        const currentVideoInteractions = prev[videoId] || {};
        const currentPlatformInteractions = currentVideoInteractions[platform] || { liked: false, shared: false };
        
        return {
          ...prev,
          [videoId]: {
            ...currentVideoInteractions,
            [platform]: {
              ...currentPlatformInteractions,
              [action === 'like' ? 'liked' : 'shared']: true,
            },
          },
        };
      });

      // Track the engagement with retry
      await trackEngagement(videoId, platform, action);
    } catch (err) {
      console.error('Error tracking platform click:', err);
      
      // Revert optimistic update on error
      setUserInteractions(prev => {
        const currentVideoInteractions = prev[videoId] || {};
        const currentPlatformInteractions = currentVideoInteractions[platform] || { liked: false, shared: false };
        
        return {
          ...prev,
          [videoId]: {
            ...currentVideoInteractions,
            [platform]: {
              ...currentPlatformInteractions,
              [action === 'like' ? 'liked' : 'shared']: false,
            },
          },
        };
      });
      
      throw err; // Re-throw so the component can handle it
    }
  }, [trackEngagement]);

  // Fetch interactions when video IDs change
  useEffect(() => {
    if (videoIds.length > 0) {
      fetchInteractions(videoIds);
    }
  }, [videoIds, fetchInteractions]);

  return {
    userInteractions,
    isLoading,
    error: error?.message || null,
    handlePlatformClick,
    refreshInteractions,
  };
}