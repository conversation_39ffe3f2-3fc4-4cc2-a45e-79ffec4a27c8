import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  initialData: T[];
  fetchMore: (offset: number, limit: number) => Promise<{ data: T[]; hasMore: boolean; total?: number }>;
  limit?: number;
  threshold?: number;
  enabled?: boolean;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  refresh: () => Promise<void>;
  isRefreshing: boolean;
  getSentinelProps: () => { ref: React.RefObject<HTMLDivElement | null>; style: { height: string; width: string } };
}

export function useInfiniteScroll<T>({
  initialData,
  fetchMore,
  limit = 20,
  threshold = 100,
  enabled = true
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(initialData.length);
  
  const loadingRef = useRef(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const sentinelRef = useRef<HTMLDivElement | null>(null);

  // Load more data
  const loadMore = useCallback(async () => {
    if (!enabled || loadingRef.current || !hasMore) return;
    
    loadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const result = await fetchMore(offset, limit);
      
      setData(prevData => [...prevData, ...result.data]);
      setHasMore(result.hasMore);
      setOffset(prevOffset => prevOffset + result.data.length);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more data');
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, [enabled, hasMore, offset, limit, fetchMore]);

  // Refresh data (pull-to-refresh)
  const refresh = useCallback(async () => {
    if (!enabled || loadingRef.current) return;
    
    loadingRef.current = true;
    setIsRefreshing(true);
    setError(null);

    try {
      const result = await fetchMore(0, limit);
      
      setData(result.data);
      setHasMore(result.hasMore);
      setOffset(result.data.length);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh data');
    } finally {
      setIsRefreshing(false);
      loadingRef.current = false;
    }
  }, [enabled, limit, fetchMore]);

  // Set up intersection observer for infinite scroll
  useEffect(() => {
    if (!enabled || !sentinelRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingRef.current) {
          loadMore();
        }
      },
      {
        threshold: 0.1,
        rootMargin: `${threshold}px`
      }
    );

    observerRef.current.observe(sentinelRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enabled, hasMore, loading, loadMore, threshold]);

  // Update data when initialData changes
  useEffect(() => {
    setData(initialData);
    setOffset(initialData.length);
    setHasMore(true);
    setError(null);
  }, [initialData]);

  // Provide sentinel ref for the component to use
  const getSentinelProps = useCallback(() => ({
    ref: sentinelRef,
    style: { height: '1px', width: '100%' }
  }), []);

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    isRefreshing,
    getSentinelProps
  };
}