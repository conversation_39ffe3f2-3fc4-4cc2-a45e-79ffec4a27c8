'use client';

import { useCallback, useRef, useEffect } from 'react';

export interface ScreenReaderOptions {
  politeness?: 'polite' | 'assertive';
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
  delay?: number;
}

/**
 * Hook for managing screen reader announcements
 */
export function useScreenReader() {
  const announcementRegionRef = useRef<HTMLDivElement | null>(null);

  // Create the announcement region on mount
  useEffect(() => {
    if (!announcementRegionRef.current) {
      const region = document.createElement('div');
      region.setAttribute('aria-live', 'polite');
      region.setAttribute('aria-atomic', 'true');
      region.className = 'sr-only';
      region.style.cssText = `
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
      `;
      
      document.body.appendChild(region);
      announcementRegionRef.current = region;
    }

    return () => {
      if (announcementRegionRef.current && document.body.contains(announcementRegionRef.current)) {
        document.body.removeChild(announcementRegionRef.current);
      }
    };
  }, []);

  const announce = useCallback((
    message: string,
    options: ScreenReaderOptions = {}
  ) => {
    const {
      politeness = 'polite',
      atomic = true,
      relevant = 'additions',
      delay = 100
    } = options;

    if (!announcementRegionRef.current || !message.trim()) return;

    const region = announcementRegionRef.current;

    // Update attributes
    region.setAttribute('aria-live', politeness);
    region.setAttribute('aria-atomic', atomic.toString());
    region.setAttribute('aria-relevant', relevant);

    // Clear previous content and set new message with a slight delay
    // This ensures screen readers pick up the change
    region.textContent = '';
    
    setTimeout(() => {
      if (region) {
        region.textContent = message;
      }
    }, delay);

    // Clear the message after a reasonable time to prevent clutter
    setTimeout(() => {
      if (region && region.textContent === message) {
        region.textContent = '';
      }
    }, 5000);
  }, []);

  const announceNavigation = useCallback((pageName: string, additionalInfo?: string) => {
    const message = additionalInfo 
      ? `Navigated to ${pageName}. ${additionalInfo}`
      : `Navigated to ${pageName}`;
    
    announce(message, { politeness: 'assertive' });
  }, [announce]);

  const announceAction = useCallback((action: string, result?: string) => {
    const message = result 
      ? `${action}. ${result}`
      : action;
    
    announce(message, { politeness: 'polite' });
  }, [announce]);

  const announceError = useCallback((error: string, context?: string) => {
    const message = context 
      ? `Error in ${context}: ${error}`
      : `Error: ${error}`;
    
    announce(message, { politeness: 'assertive' });
  }, [announce]);

  const announceSuccess = useCallback((success: string, context?: string) => {
    const message = context 
      ? `Success in ${context}: ${success}`
      : `Success: ${success}`;
    
    announce(message, { politeness: 'polite' });
  }, [announce]);

  const announceLoading = useCallback((loadingMessage: string) => {
    announce(`Loading: ${loadingMessage}`, { politeness: 'polite' });
  }, [announce]);

  const announceLoadingComplete = useCallback((completionMessage: string) => {
    announce(`Loading complete: ${completionMessage}`, { politeness: 'polite' });
  }, [announce]);

  return {
    announce,
    announceNavigation,
    announceAction,
    announceError,
    announceSuccess,
    announceLoading,
    announceLoadingComplete
  };
}

/**
 * Hook for managing dynamic content announcements
 */
export function useDynamicAnnouncements() {
  const { announce } = useScreenReader();
  const previousValuesRef = useRef<Map<string, unknown>>(new Map());

  const announceChange = useCallback((
    key: string,
    newValue: unknown,
    formatter?: (value: unknown) => string,
    options?: ScreenReaderOptions
  ) => {
    const previousValue = previousValuesRef.current.get(key);
    
    if (previousValue !== newValue) {
      previousValuesRef.current.set(key, newValue);
      
      const message = formatter ? formatter(newValue) : String(newValue);
      announce(message, options);
    }
  }, [announce]);

  const announceListChange = useCallback((
    key: string,
    newList: unknown[],
    formatter?: (list: unknown[], change: 'added' | 'removed' | 'updated') => string,
    options?: ScreenReaderOptions
  ) => {
    const previousList = (previousValuesRef.current.get(key) as unknown[]) || [];
    
    if (JSON.stringify(previousList) !== JSON.stringify(newList)) {
      previousValuesRef.current.set(key, newList);
      
      let changeType: 'added' | 'removed' | 'updated' = 'updated';
      if (newList.length > previousList.length) {
        changeType = 'added';
      } else if (newList.length < previousList.length) {
        changeType = 'removed';
      }
      
      const message = formatter 
        ? formatter(newList, changeType)
        : `List ${changeType}. ${newList.length} items total.`;
      
      announce(message, options);
    }
  }, [announce]);

  const announceCountChange = useCallback((
    key: string,
    newCount: number,
    label: string,
    options?: ScreenReaderOptions
  ) => {
    announceChange(
      key,
      newCount,
      (count) => `${label}: ${count}`,
      options
    );
  }, [announceChange]);

  return {
    announceChange,
    announceListChange,
    announceCountChange
  };
}

/**
 * Hook for managing form accessibility announcements
 */
export function useFormAnnouncements() {
  const { announce } = useScreenReader();

  const announceFieldError = useCallback((fieldName: string, error: string) => {
    announce(`Error in ${fieldName}: ${error}`, { politeness: 'assertive' });
  }, [announce]);

  const announceFieldSuccess = useCallback((fieldName: string, success?: string) => {
    const message = success 
      ? `${fieldName}: ${success}`
      : `${fieldName} is valid`;
    
    announce(message, { politeness: 'polite' });
  }, [announce]);

  const announceFormSubmission = useCallback((isSubmitting: boolean, formName?: string) => {
    const formLabel = formName || 'form';
    const message = isSubmitting 
      ? `Submitting ${formLabel}...`
      : `${formLabel} submitted successfully`;
    
    announce(message, { politeness: 'assertive' });
  }, [announce]);

  const announceValidationSummary = useCallback((
    errors: string[],
    formName?: string
  ) => {
    const formLabel = formName || 'form';
    
    if (errors.length === 0) {
      announce(`${formLabel} is valid and ready to submit`, { politeness: 'polite' });
    } else {
      const errorCount = errors.length;
      const message = `${formLabel} has ${errorCount} error${errorCount === 1 ? '' : 's'}: ${errors.join(', ')}`;
      announce(message, { politeness: 'assertive' });
    }
  }, [announce]);

  return {
    announceFieldError,
    announceFieldSuccess,
    announceFormSubmission,
    announceValidationSummary
  };
}

/**
 * Hook for managing modal accessibility announcements
 */
export function useModalAnnouncements() {
  const { announce } = useScreenReader();

  const announceModalOpen = useCallback((modalTitle: string, description?: string) => {
    const message = description 
      ? `${modalTitle} dialog opened. ${description}`
      : `${modalTitle} dialog opened`;
    
    announce(message, { politeness: 'assertive' });
  }, [announce]);

  const announceModalClose = useCallback((modalTitle: string) => {
    announce(`${modalTitle} dialog closed`, { politeness: 'assertive' });
  }, [announce]);

  const announceModalAction = useCallback((action: string, result?: string) => {
    const message = result 
      ? `${action}. ${result}`
      : action;
    
    announce(message, { politeness: 'polite' });
  }, [announce]);

  return {
    announceModalOpen,
    announceModalClose,
    announceModalAction
  };
}