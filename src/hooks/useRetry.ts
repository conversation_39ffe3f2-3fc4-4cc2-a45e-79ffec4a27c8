'use client';

import { useState, useCallback, useRef, useMemo } from 'react';

interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  backoffMultiplier?: number;
  maxDelay?: number;
  retryCondition?: (error: unknown) => boolean;
  onRetry?: (attempt: number, error: unknown) => void;
}

interface UseRetryReturn<T> {
  execute: (...args: unknown[]) => Promise<T>;
  isLoading: boolean;
  error: Error | null;
  retryCount: number;
  canRetry: boolean;
  reset: () => void;
}

const defaultOptions: Required<RetryOptions> = {
  maxRetries: 3,
  retryDelay: 1000,
  backoffMultiplier: 2,
  maxDelay: 10000,
  retryCondition: () => true,
  onRetry: () => {},
};

export function useRetry<T>(
  asyncFunction: (...args: unknown[]) => Promise<T>,
  options: RetryOptions = {}
): UseRetryReturn<T> {
  const opts = useMemo(() => ({ ...defaultOptions, ...options }), [options]);
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  
  const abortControllerRef = useRef<AbortController | null>(null);

  const calculateDelay = useCallback((attempt: number): number => {
    const delay = opts.retryDelay * Math.pow(opts.backoffMultiplier, attempt - 1);
    return Math.min(delay, opts.maxDelay);
  }, [opts.retryDelay, opts.backoffMultiplier, opts.maxDelay]);

  const sleep = useCallback((ms: number): Promise<void> => {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(resolve, ms);
      
      // Allow cancellation
      if (abortControllerRef.current) {
        abortControllerRef.current.signal.addEventListener('abort', () => {
          clearTimeout(timeoutId);
          reject(new Error('Aborted'));
        });
      }
    });
  }, []);

  const execute = useCallback(async (...args: unknown[]): Promise<T> => {
    // Cancel any previous execution
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    
    setIsLoading(true);
    setError(null);
    setRetryCount(0);

    let lastError: Error;
    
    for (let attempt = 1; attempt <= opts.maxRetries + 1; attempt++) {
      try {
        // Check if aborted
        if (abortControllerRef.current.signal.aborted) {
          throw new Error('Aborted');
        }

        const result = await asyncFunction(...args);
        
        // Success - reset state and return result
        setIsLoading(false);
        setError(null);
        setRetryCount(0);
        return result;
        
      } catch (err) {
        lastError = err instanceof Error ? err : new Error(String(err));
        
        // Check if aborted
        if (abortControllerRef.current.signal.aborted || lastError.message === 'Aborted') {
          setIsLoading(false);
          setError(new Error('Operation was cancelled'));
          return Promise.reject(new Error('Operation was cancelled'));
        }

        // Check if we should retry this error
        if (!opts.retryCondition(lastError)) {
          break;
        }

        // If this is not the last attempt, wait and retry
        if (attempt <= opts.maxRetries) {
          setRetryCount(attempt);
          opts.onRetry(attempt, lastError);
          
          const delay = calculateDelay(attempt);
          
          try {
            await sleep(delay);
          } catch {
              // Sleep was aborted
              setIsLoading(false);
              setError(new Error('Operation was cancelled'));
              return Promise.reject(new Error('Operation was cancelled'));
          }
        }
      }
    }

    // All retries exhausted
    setIsLoading(false);
    setError(lastError!);
    throw lastError!;
  }, [asyncFunction, opts, calculateDelay, sleep]);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsLoading(false);
    setError(null);
    setRetryCount(0);
  }, []);

  const canRetry = !isLoading && error !== null && retryCount < opts.maxRetries;

  return {
    execute,
    isLoading,
    error,
    retryCount,
    canRetry,
    reset,
  };
}

// Specialized retry hook for API calls
export function useApiRetry<T>(
  apiCall: (...args: unknown[]) => Promise<T>,
  options: RetryOptions = {}
): UseRetryReturn<T> {
  const apiRetryOptions: RetryOptions = {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 1.5,
    maxDelay: 5000,
    retryCondition: (error: unknown) => {
      // Don't retry if the request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return false;
      }
      
      // Don't retry if the error message indicates abortion
      if (error instanceof Error && (error.message === 'Aborted' || error.message.includes('aborted'))) {
        return false;
      }
      
      // Extract status code from various error formats
      let statusCode: number | undefined;
      
      // Check if it's a fetch response error
      if (
        typeof error === 'object' &&
        error !== null &&
        'response' in error &&
        typeof (error as { response?: { status?: number } }).response === 'object' &&
        (error as { response?: { status?: number } }).response !== null &&
        (error as { response?: { status?: number } }).response !== undefined
      ) {
        statusCode = (error as { response?: { status?: number } }).response?.status;
      }
      
      // Check if it's a direct HTTP status error (like from fetch)
      else if (
        typeof error === 'object' &&
        error !== null &&
        'status' in error &&
        typeof (error as { status?: number }).status === 'number'
      ) {
        statusCode = (error as { status: number }).status;
      }
      
      // Check if it's an API error with status code
      else if (
        typeof error === 'object' &&
        error !== null &&
        'statusCode' in error &&
        typeof (error as { statusCode?: number }).statusCode === 'number'
      ) {
        statusCode = (error as { statusCode: number }).statusCode;
      }
      
      // Parse status code from error message if available
      else if (error instanceof Error && error.message) {
        const statusMatch = error.message.match(/HTTP (\d{3})/i);
        if (statusMatch) {
          statusCode = parseInt(statusMatch[1], 10);
        }
      }
      
      // If we have a status code, handle specific cases
      if (statusCode !== undefined) {
        // Always retry on server errors (5xx) or network issues (0)
        if (statusCode >= 500 || statusCode === 0) {
          return true;
        }
        
        // Retry on 429 (Too Many Requests) with exponential backoff
        if (statusCode === 429) {
          return true;
        }
        
        // Don't retry on client errors (4xx) except 429
        if (statusCode >= 400 && statusCode < 500) {
          return false;
        }
      }
      
      // Retry on network errors
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as { code?: string }).code === 'NETWORK_ERROR'
      ) {
        return true;
      }

      if (error instanceof Error && (error.message.includes('fetch') || error.message.includes('network'))) {
        return true;
      }
      
      return false;
    },
    onRetry: (attempt: number, error: unknown) => {
      console.warn(`API call failed, retrying (${attempt}/${options.maxRetries || 3}):`, error instanceof Error ? error.message : String(error));
    },
    ...options,
  };

  return useRetry(apiCall, apiRetryOptions);
}

// Hook for retrying with exponential backoff and jitter
export function useRetryWithJitter<T>(
  asyncFunction: (...args: unknown[]) => Promise<T>,
  options: RetryOptions = {}
): UseRetryReturn<T> {
  const jitterOptions: RetryOptions = {
    ...options,
    retryDelay: options.retryDelay || 1000,
    backoffMultiplier: options.backoffMultiplier || 2,
  };

  // Override the delay calculation to add jitter
  /* Removed unused originalCalculateDelay */

  return useRetry(asyncFunction, jitterOptions);
}