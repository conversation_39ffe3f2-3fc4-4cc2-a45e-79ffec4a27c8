import { useState, useEffect, useCallback, useMemo } from 'react';
import { useServerSentEvents } from './useServerSentEvents';

// Disable realtime by default in development to prevent runaway connections.
// Can be re-enabled by setting NEXT_PUBLIC_DISABLE_REALTIME=false explicitly.
const REALTIME_DISABLED =
  (process.env.NODE_ENV !== 'production') ||
  (process.env.NEXT_PUBLIC_DISABLE_REALTIME === 'true');

interface StatsData {
  totalVideos: number;
  totalUsers: number;
  totalViews: number;
  nwaVideosCount: number;
  userVideoLinksCount: number;
  timestamp: string;
}

interface UserStatsData {
  points: number;
  level: number;
  totalPoints: number;
  leaderboardPosition: number;
  timestamp: string;
}

interface LeaderboardEntry {
  id: string;
  name: string;
  points: number;
  level: number;
  rank: number;
  isCurrentUser: boolean;
}

interface LeaderboardData {
  leaderboard: LeaderboardEntry[];
  timestamp: string;
}

interface RealTimeStatsReturn {
  stats: StatsData | null;
  userStats: UserStatsData | null;
  leaderboard: LeaderboardEntry[];
  isStatsConnected: boolean;
  isLeaderboardConnected: boolean;
  statsError: string | null;
  leaderboardError: string | null;
  lastStatsUpdate: Date | null;
  lastLeaderboardUpdate: Date | null;
  reconnectStats: () => void;
  reconnectLeaderboard: () => void;
}

export function useRealTimeStats(enabled = true): RealTimeStatsReturn {
  const [userStats, setUserStats] = useState<UserStatsData | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [lastStatsUpdate, setLastStatsUpdate] = useState<Date | null>(null);
  const [lastLeaderboardUpdate, setLastLeaderboardUpdate] = useState<Date | null>(null);

  // Effective runtime toggle (env + caller control)
  const effectiveEnabled = enabled && !REALTIME_DISABLED;

  

  // Stable callbacks and options to prevent reconnect loops on re-render
  const onStatsError = useCallback((error: Event) => {
    console.error('Stats SSE error:', error);
  }, []);
  const statsOptions = useMemo(() => ({
    reconnectInterval: 3000,
    maxReconnectAttempts: 10,
    onError: onStatsError,
    enabled: effectiveEnabled,
  }), [onStatsError, effectiveEnabled]);

  // Hook for system statistics
  const {
    data: stats,
    isConnected: isStatsConnected,
    error: statsError,
    connect: reconnectStats
  } = useServerSentEvents<StatsData>('/api/realtime/stats', 'stats', statsOptions);

  const onUserStatsError = useCallback((error: Event) => {
    console.error('User stats SSE error:', error);
  }, []);
  const userStatsOptions = useMemo(() => ({
    reconnectInterval: 3000,
    maxReconnectAttempts: 10,
    onError: onUserStatsError,
    enabled: effectiveEnabled,
  }), [onUserStatsError, effectiveEnabled]);

  // Hook for user statistics
  const {
    data: userStatsData,
    isConnected: isUserStatsConnected,
    error: userStatsError
  } = useServerSentEvents<UserStatsData>('/api/realtime/stats', 'userStats', userStatsOptions);

  const onLeaderboardError = useCallback((error: Event) => {
    console.error('Leaderboard SSE error:', error);
  }, []);
  const leaderboardOptions = useMemo(() => ({
    reconnectInterval: 3000,
    maxReconnectAttempts: 10,
    onError: onLeaderboardError,
    enabled: effectiveEnabled,
  }), [onLeaderboardError, effectiveEnabled]);

  // Hook for leaderboard
  const {
    data: leaderboardData,
    isConnected: isLeaderboardConnected,
    error: leaderboardError,
    connect: reconnectLeaderboard
  } = useServerSentEvents<LeaderboardData>('/api/realtime/leaderboard', 'leaderboard', leaderboardOptions);

  // Update user stats when received
  useEffect(() => {
    if (userStatsData) {
      setUserStats(userStatsData);
    }
  }, [userStatsData]);

  // Update leaderboard when received
  useEffect(() => {
    if (leaderboardData?.leaderboard) {
      setLeaderboard(leaderboardData.leaderboard);
      setLastLeaderboardUpdate(new Date());
    }
  }, [leaderboardData]);

  // Update last stats update time
  useEffect(() => {
    if (stats) {
      setLastStatsUpdate(new Date());
    }
  }, [stats]);

  return {
    stats,
    userStats,
    leaderboard,
    isStatsConnected: isStatsConnected && isUserStatsConnected,
    isLeaderboardConnected,
    statsError: statsError || userStatsError,
    leaderboardError,
    lastStatsUpdate,
    lastLeaderboardUpdate,
    reconnectStats,
    reconnectLeaderboard
  };
}