'use client';

import { useEffect, useState } from 'react';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOffline: boolean;
  notificationPermission: NotificationPermission;
  serviceWorkerRegistration: ServiceWorkerRegistration | null;
}

interface PWAActions {
  installApp: () => Promise<void>;
  requestNotificationPermission: () => Promise<NotificationPermission>;
  subscribeToPushNotifications: () => Promise<PushSubscription | null>;
  unsubscribeFromPushNotifications: () => Promise<boolean>;
  showNotification: (title: string, options?: NotificationOptions) => Promise<void>;
}

export function usePWA(): PWAState & PWAActions {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOffline, setIsOffline] = useState(() => typeof window !== 'undefined' ? !navigator.onLine : false);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>(() => 
    typeof window !== 'undefined' && 'Notification' in window ? Notification.permission : 'default'
  );
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Prevent execution on server side
    if (typeof window === 'undefined') return;

    let isMounted = true;

    // Check if app is installed (running in standalone mode)
    const checkInstalled = () => {
      if (!isMounted) return;
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as unknown as { standalone?: boolean }).standalone === true;
      setIsInstalled(isStandalone || isInWebAppiOS);
    };

    // Check online/offline status
    const updateOnlineStatus = () => {
      if (!isMounted) return;
      setIsOffline(!navigator.onLine);
    };

    // Check notification permission
    const checkNotificationPermission = () => {
      if (!isMounted) return;
      if ('Notification' in window) {
        setNotificationPermission(Notification.permission);
      }
    };

    // Register service worker
    const registerServiceWorker = async () => {
      // Skip service worker registration in insecure contexts to prevent DOMException
      if (!isMounted || typeof window === 'undefined' || !window.isSecureContext) {
        console.log('Skipping service worker registration in insecure context');
        return;
      }
      
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          if (!isMounted) return;
          setServiceWorkerRegistration(registration);
          console.log('Service Worker registered successfully:', registration);

          // Listen for updates
          const handleUpdateFound = () => {
            const newWorker = registration.installing;
            if (newWorker) {
              const handleStateChange = () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available, prompt user to refresh
                  if (confirm('New version available! Refresh to update?')) {
                    window.location.reload();
                  }
                }
              };
              newWorker.addEventListener('statechange', handleStateChange);
            }
          };
          registration.addEventListener('updatefound', handleUpdateFound);
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      }
    };

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      if (!isMounted) return;
      e.preventDefault();
      setInstallPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      if (!isMounted) return;
      setIsInstalled(true);
      setIsInstallable(false);
      setInstallPrompt(null);
      console.log('PWA was installed');
    };

    // Initialize with slight delay to prevent hydration issues
    const initTimer = setTimeout(() => {
      if (!isMounted) return;
      checkInstalled();
      updateOnlineStatus();
      checkNotificationPermission();
      registerServiceWorker();
    }, 100);

    // Event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      isMounted = false;
      clearTimeout(initTimer);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const installApp = async (): Promise<void> => {
    if (!installPrompt) {
      throw new Error('Install prompt not available');
    }

    const result = await installPrompt.prompt();
    console.log('Install prompt result:', result);

    if (result.outcome === 'accepted') {
      setIsInstallable(false);
      setInstallPrompt(null);
    }
  };

  const requestNotificationPermission = async (): Promise<NotificationPermission> => {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    const permission = await Notification.requestPermission();
    setNotificationPermission(permission);
    return permission;
  };

  const subscribeToPushNotifications = async (): Promise<PushSubscription | null> => {
    if (!serviceWorkerRegistration) {
      throw new Error('Service Worker not registered');
    }

    if (!('PushManager' in window)) {
      throw new Error('Push notifications not supported');
    }

    try {
      // Check if already subscribed
      const existingSubscription = await serviceWorkerRegistration.pushManager.getSubscription();
      if (existingSubscription) {
        return existingSubscription;
      }

      // Subscribe to push notifications
      const subscription = await serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || '') as BufferSource
      });

      // Send subscription to server
      await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  };

  const unsubscribeFromPushNotifications = async (): Promise<boolean> => {
    if (!serviceWorkerRegistration) {
      return false;
    }

    try {
      const subscription = await serviceWorkerRegistration.pushManager.getSubscription();
      if (subscription) {
        const successful = await subscription.unsubscribe();
        
        if (successful) {
          // Remove subscription from server
          await fetch('/api/notifications/unsubscribe', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ endpoint: subscription.endpoint }),
          });
        }
        
        return successful;
      }
      return true;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  };

  const showNotification = async (title: string, options?: NotificationOptions): Promise<void> => {
    if (notificationPermission !== 'granted') {
      throw new Error('Notification permission not granted');
    }

    if (serviceWorkerRegistration) {
      await serviceWorkerRegistration.showNotification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        ...options,
      });
    } else {
      new Notification(title, {
        icon: '/icons/icon-192x192.png',
        ...options,
      });
    }
  };

  return {
    // State
    isInstallable,
    isInstalled,
    isOffline,
    notificationPermission,
    serviceWorkerRegistration,
    // Actions
    installApp,
    requestNotificationPermission,
    subscribeToPushNotifications,
    unsubscribeFromPushNotifications,
    showNotification,
  };
}

// Helper function to convert VAPID key
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}