'use client';

import { useEffect, useCallback, useRef } from 'react';
import { focusManagement } from '@/lib/accessibility';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  category?: string;
  preventDefault?: boolean;
}

export interface UseKeyboardNavigationOptions {
  shortcuts?: KeyboardShortcut[];
  enableGlobalShortcuts?: boolean;
  announceShortcuts?: boolean;
  disabled?: boolean;
}

/**
 * Hook for managing keyboard navigation and shortcuts
 */
export function useKeyboardNavigation(options: UseKeyboardNavigationOptions = {}) {
  const {
    shortcuts = [],
    enableGlobalShortcuts = true,
    announceShortcuts = false,
    disabled = false
  } = options;

  const shortcutsRef = useRef<KeyboardShortcut[]>(shortcuts);
  const isDisabledRef = useRef(disabled);

  // Update refs when props change
  useEffect(() => {
    shortcutsRef.current = shortcuts;
    isDisabledRef.current = disabled;
  }, [shortcuts, disabled]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (isDisabledRef.current) return;

    const activeShortcuts = shortcutsRef.current;
    
    for (const shortcut of activeShortcuts) {
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
      const ctrlMatches = !!event.ctrlKey === !!shortcut.ctrlKey;
      const altMatches = !!event.altKey === !!shortcut.altKey;
      const shiftMatches = !!event.shiftKey === !!shortcut.shiftKey;
      const metaMatches = !!event.metaKey === !!shortcut.metaKey;

      if (keyMatches && ctrlMatches && altMatches && shiftMatches && metaMatches) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault();
        }
        
        shortcut.action();
        
        if (announceShortcuts) {
          focusManagement.announceToScreenReader(
            `Executed shortcut: ${shortcut.description}`,
            'polite'
          );
        }
        
        break;
      }
    }
  }, [announceShortcuts]);

  useEffect(() => {
    if (!enableGlobalShortcuts || disabled) return;

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, enableGlobalShortcuts, disabled]);

  return {
    handleKeyDown,
    shortcuts: shortcutsRef.current
  };
}

/**
 * Hook for managing focus within a container (like a modal or dropdown)
 */
export function useFocusTrap<T extends HTMLElement>(
  containerRef: React.RefObject<T | null>,
  isActive: boolean = true,
  options: {
    initialFocus?: React.RefObject<HTMLElement | null>;
    returnFocus?: React.RefObject<HTMLElement | null>;
    escapeDeactivates?: boolean;
    onEscape?: () => void;
  } = {}
) {
  const {
    initialFocus,
    returnFocus,
    escapeDeactivates = true,
    onEscape
  } = options;

  const previousFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    // Store the previously focused element
    previousFocusRef.current = document.activeElement as HTMLElement;

    // Set initial focus
    if (initialFocus?.current) {
      initialFocus.current.focus();
    } else {
      const firstFocusable = containerRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement;
      firstFocusable?.focus();
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && escapeDeactivates && onEscape) {
        event.preventDefault();
        onEscape();
        return;
      }

      if (event.key === 'Tab') {
        const focusableElements = containerRef.current?.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (!focusableElements || focusableElements.length === 0) return;

        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (event.shiftKey) {
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    const returnFocusElement = returnFocus?.current;

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      
      // Return focus to the previously focused element
      if (returnFocusElement) {
        returnFocusElement.focus();
      } else if (previousFocusRef.current && document.contains(previousFocusRef.current)) {
        previousFocusRef.current.focus();
      }
    };
  }, [isActive, containerRef, initialFocus, returnFocus, escapeDeactivates, onEscape]);
}

/**
 * Hook for managing arrow key navigation in lists
 */
export function useArrowNavigation<T extends HTMLElement>(
  items: React.RefObject<T>[],
  options: {
    orientation?: 'horizontal' | 'vertical' | 'both';
    loop?: boolean;
    onActivate?: (index: number) => void;
    disabled?: boolean;
  } = {}
) {
  const {
    orientation = 'vertical',
    loop = true,
    onActivate,
    disabled = false
  } = options;

  const currentIndexRef = useRef(0);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled) return;

    const validItems = items.filter(ref => ref.current);
    if (validItems.length === 0) return;

    let newIndex = currentIndexRef.current;
    let shouldPreventDefault = false;

    switch (event.key) {
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          newIndex = loop 
            ? (currentIndexRef.current + 1) % validItems.length
            : Math.min(currentIndexRef.current + 1, validItems.length - 1);
          shouldPreventDefault = true;
        }
        break;
      
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          newIndex = loop
            ? currentIndexRef.current === 0 ? validItems.length - 1 : currentIndexRef.current - 1
            : Math.max(currentIndexRef.current - 1, 0);
          shouldPreventDefault = true;
        }
        break;
      
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          newIndex = loop 
            ? (currentIndexRef.current + 1) % validItems.length
            : Math.min(currentIndexRef.current + 1, validItems.length - 1);
          shouldPreventDefault = true;
        }
        break;
      
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          newIndex = loop
            ? currentIndexRef.current === 0 ? validItems.length - 1 : currentIndexRef.current - 1
            : Math.max(currentIndexRef.current - 1, 0);
          shouldPreventDefault = true;
        }
        break;
      
      case 'Home':
        newIndex = 0;
        shouldPreventDefault = true;
        break;
      
      case 'End':
        newIndex = validItems.length - 1;
        shouldPreventDefault = true;
        break;
      
      case 'Enter':
      case ' ':
        if (onActivate) {
          event.preventDefault();
          onActivate(currentIndexRef.current);
        }
        break;
    }

    if (shouldPreventDefault) {
      event.preventDefault();
      currentIndexRef.current = newIndex;
      validItems[newIndex].current?.focus();
    }
  }, [items, orientation, loop, onActivate, disabled]);

  const setCurrentIndex = useCallback((index: number) => {
    const validItems = items.filter(ref => ref.current);
    if (index >= 0 && index < validItems.length) {
      currentIndexRef.current = index;
      validItems[index].current?.focus();
    }
  }, [items]);

  return {
    handleKeyDown,
    currentIndex: currentIndexRef.current,
    setCurrentIndex
  };
}

/**
 * Global keyboard shortcuts for the application
 */
export const GLOBAL_SHORTCUTS: KeyboardShortcut[] = [
  {
    key: '/',
    action: () => {
      const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
        searchInput.select();
      }
    },
    description: 'Focus search',
    category: 'Navigation',
    preventDefault: true
  },
  {
    key: 'h',
    action: () => {
      const homeLink = document.querySelector('a[href="/"], a[href="/dashboard"]') as HTMLAnchorElement;
      if (homeLink) {
        homeLink.click();
      }
    },
    description: 'Go to home/dashboard',
    category: 'Navigation'
  },
  {
    key: 'l',
    action: () => {
      const leaderboardLink = document.querySelector('a[href="/leaderboard"]') as HTMLAnchorElement;
      if (leaderboardLink) {
        leaderboardLink.click();
      }
    },
    description: 'Go to leaderboard',
    category: 'Navigation'
  },
  {
    key: 's',
    action: () => {
      const settingsLink = document.querySelector('a[href="/settings"]') as HTMLAnchorElement;
      if (settingsLink) {
        settingsLink.click();
      }
    },
    description: 'Go to settings',
    category: 'Navigation'
  },
  {
    key: 'n',
    action: () => {
      const notificationBell = document.querySelector('[aria-label*="notification" i]') as HTMLButtonElement;
      if (notificationBell) {
        notificationBell.click();
      }
    },
    description: 'Open notifications',
    category: 'Actions'
  },
  {
    key: '?',
    shiftKey: true,
    action: () => {
      focusManagement.announceToScreenReader(
        'Keyboard shortcuts: Press / for search, h for home, l for leaderboard, s for settings, n for notifications, ? for help',
        'assertive'
      );
    },
    description: 'Show keyboard shortcuts help',
    category: 'Help'
  }
];