import { useEffect, useRef, useState, useCallback } from 'react';

interface SSEOptions {
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onError?: (error: Event) => void;
  onOpen?: (event: Event) => void;
  onClose?: (event: Event) => void;
  enabled?: boolean;
}

interface SSEHookReturn<T> {
  data: T | null;
  isConnected: boolean;
  error: string | null;
  reconnectCount: number;
  connect: () => void;
  disconnect: () => void;
}

export function useServerSentEvents<T = unknown>(
  url: string,
  eventType: string,
  options: SSEOptions = {}
): SSEHookReturn<T> {
  const {
    onError,
    onOpen,
    onClose,
    enabled = true,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reconnectCount, setReconnectCount] = useState(0);

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const shouldReconnectRef = useRef(true);
  const reconnectCountRef = useRef(0);
  const lastConnectAtRef = useRef<number>(0);

  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false;
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    setIsConnected(false);
    // Call onClose callback when explicitly disconnecting
    onClose?.(new Event('close'));
  }, [onClose]);

  // Keep a ref in sync with state to avoid recreating connect() on changes
  useEffect(() => {
    reconnectCountRef.current = reconnectCount;
  }, [reconnectCount]);

  const connect = useCallback(() => {
    if (!enabled) return;
    // Rate-limit connection attempts to at most 1 every 5s
    const now = Date.now();
    if (now - lastConnectAtRef.current < 5000) {
      return;
    }
    lastConnectAtRef.current = now;

    if (eventSourceRef.current) {
      disconnect();
    }

    shouldReconnectRef.current = true;
    setError(null);

    try {
      // Use relative URL to avoid accidental cross-origin and CORS churn
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      eventSource.onopen = (event) => {
        setIsConnected(true);
        setError(null);
        setReconnectCount(0);
        onOpen?.(event);
      };

      eventSource.addEventListener(eventType, (event) => {
        try {
          const parsedData = JSON.parse(event.data);
          setData(parsedData);
        } catch (parseError) {
          console.error('Error parsing SSE data:', parseError);
          setError('Failed to parse server data');
        }
      });

      eventSource.addEventListener('error', (event) => {
        try {
          const errorData = JSON.parse((event as MessageEvent).data);
          setError(errorData.error || 'Server error occurred');
        } catch {
          // Not a JSON error message, ignore
        }
      });

      eventSource.addEventListener('heartbeat', () => {
        // Heartbeat received, connection is alive
      });

      eventSource.onerror = (event) => {
        setIsConnected(false);
        onError?.(event);
        // Rely on native EventSource automatic reconnection to avoid duplicate reconnect storms.
        // Do not manually call connect() here.
      };

      // No native onclose for EventSource; closure is handled via onerror or error event.

    } catch (err) {
      setError('Failed to establish connection');
      console.error('SSE connection error:', err);
    }
  }, [url, eventType, onError, onOpen, disconnect, enabled]);

  useEffect(() => {
    if (!enabled) return;

    connect();

    return () => {
      disconnect();
    };
  }, [enabled, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      shouldReconnectRef.current = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  return {
    data,
    isConnected,
    error,
    reconnectCount,
    connect,
    disconnect
  };
}