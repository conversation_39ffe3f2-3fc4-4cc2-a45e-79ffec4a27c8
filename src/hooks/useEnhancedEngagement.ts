'use client';

import { useState, useCallback, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface PendingConfirmation {
  id: string;
  videoId: string;
  videoTitle: string;
  platform: string;
  videoUrl: string;
  timestamp: Date;
}

interface EngagementStats {
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  totalConfirmed: number;
  currentStreak: number;
  leaderboardPosition: number;
  platformsUsed: string[];
  newAchievements: string[];
}

export const useEnhancedEngagement = () => {
  const { data: session } = useSession();
  const [pendingConfirmations, setPendingConfirmations] = useState<PendingConfirmation[]>([]);
  const [currentConfirmation, setCurrentConfirmation] = useState<PendingConfirmation | null>(null);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track engagement with multi-action confirmation system
  const trackEngagementWithConfirmation = useCallback(async (
    videoId: string,
    videoTitle: string,
    platform: string,
    url?: string
  ) => {
    if (!session?.user?.id) {
      setError('Please log in to engage with content');
      return { success: false };
    }

    setIsLoading(true);
    setError(null);

    try {
      // 1. First track the initial engagement intent (without specifying action)
      const response = await fetch('/api/engagement/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoId,
          platform: platform.toLowerCase(),
          action: 'engage' // Use a generic action since we'll specify actual actions in confirmation
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to track engagement');
      }

      // 2. Open the platform URL if provided
      if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
      }

      // 3. Schedule confirmation modal after 5 seconds
      const confirmationId = `${videoId}-${platform}-${Date.now()}`;
      const pendingConfirmation: PendingConfirmation = {
        id: confirmationId,
        videoId,
        videoTitle,
        platform,
        videoUrl: url || '',
        timestamp: new Date()
      };

      setPendingConfirmations(prev => [...prev, pendingConfirmation]);

      // Show confirmation modal after 5 seconds
      setTimeout(() => {
        setCurrentConfirmation(pendingConfirmation);
        setIsConfirmationModalOpen(true);
      }, 5000);

      return { success: true };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to track engagement';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id]);

  // Handle multi-action confirmation modal response
  const handleConfirmation = useCallback(async (
    actions: { liked: boolean; shared: boolean; completed: boolean } | 'skip'
  ) => {
    if (!currentConfirmation) return;

    setIsLoading(true);

    try {
      // For 'skip', just close the modal without sending confirmation
      if (actions === 'skip') {
        // Remove from pending confirmations without recording API action
        setPendingConfirmations(prev => 
          prev.filter(p => p.id !== currentConfirmation.id)
        );
        setIsConfirmationModalOpen(false);
        setCurrentConfirmation(null);
        setIsLoading(false);
        return;
      }

      // Process each action that was selected
      const actionsToProcess = [];
      
      if (actions.liked) {
        actionsToProcess.push({ action: 'like', bonusPoints: 2 });
      }
      
      if (actions.shared) {
        actionsToProcess.push({ action: 'share', bonusPoints: 3 });
      }
      
      if (actions.completed) {
        actionsToProcess.push({ action: 'complete', bonusPoints: 1 });
      }
      
      // If no actions were selected but the user clicked 'I didn't do anything'
      if (actionsToProcess.length === 0) {
        // Record a 'no' confirmation for the generic 'engage' action
        const response = await fetch('/api/engagement/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            videoId: currentConfirmation.videoId,
            platform: currentConfirmation.platform,
            action: 'engage',
            confirmed: 'no',
            timestamp: currentConfirmation.timestamp.toISOString()
          }),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Failed to track confirmation:', errorData);
        }
      } else {
        // Process each selected action
        for (const { action, bonusPoints } of actionsToProcess) {
          const response = await fetch('/api/engagement/confirm', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              videoId: currentConfirmation.videoId,
              platform: currentConfirmation.platform,
              action,
              confirmed: 'yes',
              timestamp: currentConfirmation.timestamp.toISOString(),
              bonusPoints
            }),
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            console.error('Failed to track confirmation:', errorData);
          }
        }
      }

      // Remove from pending confirmations
      setPendingConfirmations(prev => 
        prev.filter(p => p.id !== currentConfirmation.id)
      );

    } catch (err) {
      console.error('Error handling confirmation:', err);
    } finally {
      // Close modal and clear current confirmation
      setIsConfirmationModalOpen(false);
      setCurrentConfirmation(null);
      setIsLoading(false);
    }
  }, [currentConfirmation]);

  // Get user engagement stats
  const getEngagementStats = useCallback(async (): Promise<EngagementStats | null> => {
    if (!session?.user?.id) return null;

    try {
      const response = await fetch('/api/engagement/user-stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch engagement stats');
      }

      const data = await response.json();
      
      // Transform the data to match our interface
      const stats: EngagementStats = {
        totalLikes: data.totalLikes || 0,
        totalShares: data.totalShares || 0,
        totalCompletions: data.totalVideosCompleted || 0,
        totalConfirmed: data.totalConfirmed || 0,
        currentStreak: data.currentStreak || 0,
        leaderboardPosition: data.leaderboardPosition || 0,
        platformsUsed: Object.keys(data.platformBreakdown || {}),
        newAchievements: data.newAchievements || []
      };

      return stats;

    } catch (err) {
      console.error('Error fetching engagement stats:', err);
      return null;
    }
  }, [session?.user?.id]);

  // Clean up old pending confirmations (older than 1 hour)
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      setPendingConfirmations(prev => 
        prev.filter(p => p.timestamp > oneHourAgo)
      );
    }, 60000); // Run every minute

    return () => clearInterval(cleanupInterval);
  }, []);

  // Auto-skip confirmations that have been pending too long
  useEffect(() => {
    if (currentConfirmation && isConfirmationModalOpen) {
      // Auto-skip after 60 seconds if no response
      const autoSkipTimer = setTimeout(() => {
        handleConfirmation('skip');
      }, 60000);

      return () => clearTimeout(autoSkipTimer);
    }
  }, [currentConfirmation, isConfirmationModalOpen, handleConfirmation]);

  return {
    // Main engagement function
    trackEngagementWithConfirmation,
    
    // Confirmation handling
    handleConfirmation,
    currentConfirmation,
    isConfirmationModalOpen,
    setIsConfirmationModalOpen,
    
    // State
    pendingConfirmations,
    isLoading,
    error,
    
    // Stats
    getEngagementStats,
    
    // Utility
    clearError: () => setError(null)
  };
};
