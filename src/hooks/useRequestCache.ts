'use client';

import { useCallback, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface PendingRequest<T> {
  promise: Promise<T>;
  resolve: (value: T) => void;
  reject: (reason: unknown) => void;
}

interface RequestCacheOptions {
  ttl?: number; // Time to live in milliseconds (default: 5 minutes)
  maxSize?: number; // Maximum cache size (default: 100)
  // deduplicationWindow?: number; // Time window for deduplicating requests in ms (default: 1000ms)
}

export function useRequestCache<T>(options: RequestCacheOptions = {}) {
  const {
    ttl = 5 * 60 * 1000, // 5 minutes
    maxSize = 100,
    // deduplicationWindow = 1000, // 1 second
  } = options;

  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  const pendingRequests = useRef<Map<string, PendingRequest<T>>>(new Map());

  const cleanupExpiredEntries = useCallback(() => {
    const now = Date.now();
    const entries = Array.from(cache.current.entries());
    
    entries.forEach(([key, entry]) => {
      if (entry.expiresAt < now) {
        cache.current.delete(key);
      }
    });
  }, []);

  const enforceMaxSize = useCallback(() => {
    const cacheMap = cache.current;
    
    if (cacheMap.size <= maxSize) {
      return;
    }

    // Remove oldest entries (LRU)
    const entries = Array.from(cacheMap.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const entriesToRemove = entries.slice(0, cacheMap.size - maxSize);
    entriesToRemove.forEach(([key]) => {
      cacheMap.delete(key);
    });
  }, [maxSize]);

  const get = useCallback((key: string): T | null => {
    cleanupExpiredEntries();
    
    const entry = cache.current.get(key);
    if (!entry) {
      return null;
    }

    const now = Date.now();
    if (entry.expiresAt < now) {
      cache.current.delete(key);
      return null;
    }

    // Update timestamp for LRU
    entry.timestamp = now;
    return entry.data;
  }, [cleanupExpiredEntries]);

  const set = useCallback((key: string, data: T) => {
    const now = Date.now();
    
    cache.current.set(key, {
      data,
      timestamp: now,
      expiresAt: now + ttl,
    });

    enforceMaxSize();
  }, [ttl, enforceMaxSize]);

  const remove = useCallback((key: string) => {
    cache.current.delete(key);
  }, []);

  const clear = useCallback(() => {
    cache.current.clear();
    pendingRequests.current.clear();
  }, []);

  const executeWithCache = useCallback(async (
    key: string,
    fetcher: () => Promise<T>
  ): Promise<T> => {
    // Check if we have cached data
    const cachedData = get(key);
    if (cachedData !== null) {
      return cachedData;
    }

    // Check if there's already a pending request for this key
    const pendingRequest = pendingRequests.current.get(key);
    if (pendingRequest) {
      return pendingRequest.promise;
    }

    // Create a new request
    let resolve: (value: T) => void;
    let reject: (reason: unknown) => void;

    const promise = new Promise<T>((res, rej) => {
      resolve = res;
      reject = rej;
    });

    const pendingReq: PendingRequest<T> = {
      promise,
      resolve: resolve!,
      reject: reject!,
    };

    pendingRequests.current.set(key, pendingReq);

    try {
      const result = await fetcher();
      
      // Cache the result
      set(key, result);
      
      // Resolve all waiting promises
      pendingReq.resolve(result);
      
      return result;
    } catch (error) {
      // Reject all waiting promises
      pendingReq.reject(error);
      throw error;
    } finally {
      // Clean up pending request
      pendingRequests.current.delete(key);
    }
  }, [get, set]);

  const isInCache = useCallback((key: string): boolean => {
    return get(key) !== null;
  }, [get]);

  const isPending = useCallback((key: string): boolean => {
    return pendingRequests.current.has(key);
  }, []);

  return {
    get,
    set,
    remove,
    clear,
    executeWithCache,
    isInCache,
    isPending,
  };
}

// Helper function to generate cache keys
export function generateCacheKey(endpoint: string, params: Record<string, unknown> = {}): string {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((sorted, key) => {
      sorted[key] = params[key];
      return sorted;
    }, {} as Record<string, unknown>);
  
  return `${endpoint}:${JSON.stringify(sortedParams)}`;
}
