'use client';

import { useState, useEffect, useCallback } from 'react';
import { Search, Filter, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface SearchFilters {
  query?: string;
  type?: 'nwa' | 'user' | 'all';
  platform?: 'youtube' | 'tiktok' | 'rumble' | '';
  featured?: boolean | null;
}

interface VideoSearchAndFilterProps {
  onSearch: (filters: SearchFilters) => void;
  initialFilters?: SearchFilters;
  showTypeFilter?: boolean;
  showPlatformFilter?: boolean;
  showFeaturedFilter?: boolean;
  placeholder?: string;
  className?: string;
}

export default function VideoSearchAndFilter({
  onSearch,
  initialFilters = {},
  showTypeFilter = true,
  showPlatformFilter = true,
  showFeaturedFilter = true,
  placeholder = "Search videos by title or description...",
  className
}: VideoSearchAndFilterProps) {
  const [searchQuery, setSearchQuery] = useState(initialFilters.query || '');
  const [filters, setFilters] = useState<SearchFilters>(initialFilters);
  const [showFilters, setShowFilters] = useState(false);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Debounced search function
  const debouncedSearch = useCallback((query: string, currentFilters: SearchFilters) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    const timer = setTimeout(() => {
      const searchFilters = {
        ...currentFilters,
        query: query.trim() || undefined
      };
      onSearch(searchFilters);
    }, 300); // 300ms debounce delay

    setDebounceTimer(timer);
  }, [debounceTimer, onSearch]);

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    debouncedSearch(value, filters);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof SearchFilters, value: string | boolean | null) => {
    const newFilters = {
      ...filters,
      [key]: value === '' ? undefined : value
    };
    setFilters(newFilters);
    debouncedSearch(searchQuery, newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('');
    setFilters({});
    onSearch({});
  };

  // Check if any filters are active
  const hasActiveFilters = searchQuery || filters.type !== 'all' || filters.platform || filters.featured !== null;

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder={placeholder}
          className="block w-full pl-10 pr-12 py-3 border border-gray-700 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
        />
        {searchQuery && (
          <button
            onClick={() => handleSearchChange('')}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Filter Toggle and Clear Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
            <ChevronDown className={cn("h-4 w-4 transition-transform", showFilters && "rotate-180")} />
          </Button>
          
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-gray-400 hover:text-white"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        {/* Active Filter Count */}
        {hasActiveFilters && (
          <span className="text-sm text-gray-400">
            {Object.values({ query: searchQuery, ...filters }).filter(v => v && v !== 'all').length} filter(s) active
          </span>
        )}
      </div>

      {/* Filter Options */}
      {showFilters && (
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            
            {/* Video Type Filter */}
            {showTypeFilter && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Video Type
                </label>
                <select
                  value={filters.type || 'all'}
                  onChange={(e) => handleFilterChange('type', e.target.value as 'nwa' | 'user' | 'all')}
                  className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="all">All Videos</option>
                  <option value="nwa">NWA Videos</option>
                  <option value="user">User Videos</option>
                </select>
              </div>
            )}

            {/* Platform Filter */}
            {showPlatformFilter && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Platform
                </label>
                <select
                  value={filters.platform || ''}
                  onChange={(e) => handleFilterChange('platform', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">All Platforms</option>
                  <option value="youtube">YouTube</option>
                  <option value="tiktok">TikTok</option>
                  <option value="rumble">Rumble</option>
                </select>
              </div>
            )}

            {/* Featured Filter */}
            {showFeaturedFilter && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Featured Status
                </label>
                <select
                  value={filters.featured === null ? '' : filters.featured ? 'true' : 'false'}
                  onChange={(e) => {
                    const value = e.target.value === '' ? null : e.target.value === 'true';
                    handleFilterChange('featured', value);
                  }}
                  className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">All Videos</option>
                  <option value="true">Featured Only</option>
                  <option value="false">Non-Featured Only</option>
                </select>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}