'use client';

import React from 'react';

interface GamingCardProps {
  children: React.ReactNode;
  className?: string;
  glowing?: boolean;
  floating?: boolean;
}

export default function GamingCard({ 
  children, 
  className = '', 
  glowing = false, 
  floating = false 
}: GamingCardProps) {
  // Build class string in a deterministic way to prevent hydration mismatches
  const classes = React.useMemo(() => {
    const baseClasses = 'glass-card p-6 transition-all duration-300 hover:scale-105';
    const glowClasses = glowing ? 'neon-glow pulse-animation' : '';
    const floatClasses = floating ? 'float-animation' : '';
    return `${baseClasses} ${glowClasses} ${floatClasses} ${className}`.trim();
  }, [className, glowing, floating]);
  
  return (
    <div className={classes}>
      {children}
    </div>
  );
}