'use client';

import React from 'react';
import GamingCard from './GamingCard';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  gradient: 'purple-cyan' | 'rose-amber' | 'emerald-lime';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  rank?: number;
}

export default function StatCard({ 
  title, 
  value, 
  icon, 
  gradient, 
  trend,
  rank 
}: StatCardProps) {
  const gradientClasses = {
    'purple-cyan': 'gaming-gradient',
    'rose-amber': 'gaming-gradient-alt',
    'emerald-lime': 'bg-gradient-to-br from-emerald-500 to-lime-400'
  };

  return (
    <GamingCard glowing={rank === 1} floating={rank === 1}>
      <div className="relative overflow-hidden">
        {/* Background gradient */}
        <div className={`absolute inset-0 ${gradientClasses[gradient]} opacity-10 rounded-lg`} />
        
        {/* Rank badge */}
        {rank && rank <= 3 && (
          <div className="absolute top-2 right-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white ${
              rank === 1 ? 'bg-yellow-500' : rank === 2 ? 'bg-gray-400' : 'bg-amber-600'
            }`}>
              #{rank}
            </div>
          </div>
        )}
        
        <div className="relative z-10 flex items-center space-x-4">
          {/* Icon */}
          <div className={`p-3 rounded-xl ${gradientClasses[gradient]} text-white`}>
            {icon}
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-400 uppercase tracking-wide">
              {title}
            </p>
            <div className="flex items-baseline space-x-2">
              <p className="text-2xl font-bold text-white font-display">
                {value}
              </p>
              {trend && (
                <span className={`text-sm font-medium ${
                  trend.isPositive ? 'text-emerald-400' : 'text-red-400'
                }`}>
                  {trend.isPositive ? '+' : ''}{trend.value}%
                </span>
              )}
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute -bottom-2 -right-2 w-16 h-16 bg-gradient-to-br from-white/5 to-transparent rounded-full" />
        <div className="absolute -top-1 -left-1 w-8 h-8 bg-gradient-to-br from-white/10 to-transparent rounded-full" />
      </div>
    </GamingCard>
  );
}