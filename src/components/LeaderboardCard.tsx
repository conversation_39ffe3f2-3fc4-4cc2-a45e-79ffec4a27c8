'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { TrophyIcon, FireIcon } from '@heroicons/react/24/outline';
import { generateAriaLabel } from '@/lib/accessibility';

interface LeaderboardEntry {
  id: string;
  name: string;
  points: number;
  level: number;
  rank: number;
  avatar?: string;
  isCurrentUser?: boolean;
}

interface LeaderboardCardProps {
  entries: LeaderboardEntry[];
  currentUserRank?: number;
}

export default function LeaderboardCard({ entries, currentUserRank }: LeaderboardCardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full shadow-lg">
            <span className="text-lg">🥇</span>
          </div>
        );
      case 2:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-gray-300 to-gray-500 rounded-full shadow-lg">
            <span className="text-lg">🥈</span>
          </div>
        );
      case 3:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-amber-500 to-amber-700 rounded-full shadow-lg">
            <span className="text-lg">🥉</span>
          </div>
        );
      case 4:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-orange-600 to-red-700 rounded-full shadow-lg">
            <span className="text-lg">🏅</span>
          </div>
        );
      default:
        return <div className="w-8 h-8 text-center text-sm font-bold text-gray-400 flex items-center justify-center">#{rank}</div>;
    }
  };

  const getRankGlow = (rank: number) => {
    if (rank === 1) return 'shadow-lg shadow-yellow-500/30';
    if (rank === 2) return 'shadow-lg shadow-gray-400/30';
    if (rank === 3) return 'shadow-lg shadow-amber-600/30';
    if (rank === 4) return 'shadow-lg shadow-orange-500/30';
    return '';
  };

  return (
    <section 
      className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl overflow-hidden shadow-2xl"
      aria-labelledby="leaderboard-title"
    >
      <div className="p-6">
        <header className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <TrophyIcon 
              className="h-8 w-8 text-yellow-400" 
              aria-hidden="true"
            />
            <h2 
              id="leaderboard-title"
              className="text-2xl font-bold text-white font-display"
            >
              Top Creators
            </h2>
          </div>
          {currentUserRank && (
            <div 
              className="text-sm bg-white/10 px-3 py-1 rounded-full text-cyan-400 font-medium"
              aria-label={`Your current rank is ${currentUserRank}`}
            >
              <span className="text-gray-300">Your rank:</span> #{currentUserRank}
            </div>
          )}
        </header>
        
        {/* Time Filters */}
        <nav 
          className="flex space-x-2 mb-6 overflow-x-auto pb-2"
          role="tablist"
          aria-label="Leaderboard time period filters"
        >
          {['All Time', 'This Month', 'This Week'].map((period) => (
            <button 
              key={period}
              role="tab"
              aria-selected={period === 'All Time'}
              aria-controls="leaderboard-content"
              aria-label={`Show leaderboard for ${period.toLowerCase()}`}
              className={`px-4 py-2 text-sm font-medium rounded-full whitespace-nowrap focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 ${
                period === 'All Time' 
                  ? 'bg-cyan-600 text-white' 
                  : 'bg-white/5 text-gray-300 hover:bg-white/10'
              }`}
            >
              {period}
            </button>
          ))}
        </nav>
        
        <div 
          id="leaderboard-content"
          className="space-y-3 max-h-[600px] overflow-y-auto pr-2 -mr-2"
          role="tabpanel"
          aria-labelledby="leaderboard-title"
        >
          {entries.map((entry) => (
            <article
              key={entry.id}
              className={`leaderboard-item relative flex items-center space-x-4 p-4 rounded-xl transition-all duration-300 ${
                entry.isCurrentUser 
                  ? 'bg-gradient-to-r from-purple-500/20 to-cyan-500/20 border border-purple-500/30' 
                  : 'bg-white/5 hover:bg-white/10'
              } ${getRankGlow(entry.rank)} ${entry.rank <= 4 ? 'scale-[1.02]' : ''}`}
              aria-label={`${entry.name}, rank ${entry.rank}, ${entry.points.toLocaleString()} points, level ${entry.level}${entry.isCurrentUser ? ' (your position)' : ''}`}
            >
              {/* Rank */}
              <div className="flex-shrink-0">
                {getRankIcon(entry.rank)}
              </div>
              
              {/* Avatar */}
              <div className="flex-shrink-0">
                <Image
                  src={entry.avatar || `https://i.pravatar.cc/40?img=${entry.rank}`}
                  alt={`${entry.name} avatar`}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-full border-2 border-white/20"
                />
              </div>
              
              {/* User info */}
              <div className="flex-1 min-w-0">
                <p className={`font-semibold truncate ${
                  entry.isCurrentUser ? 'text-cyan-300' : 'text-white'
                }`}>
                  {entry.name}
                </p>
                <p className="text-sm text-gray-400">
                  Level {entry.level}
                </p>
              </div>
              
              {/* Points */}
              <div className="flex-shrink-0 text-right">
                <div className="flex items-center justify-end space-x-1">
                  <FireIcon className="h-4 w-4 text-orange-400" />
                  <span className="font-bold text-white">
                    {entry.points.toLocaleString()}
                  </span>
                </div>
                <p className="text-xs text-gray-400">engagement</p>
              </div>
              
              {/* Decorative glow for top 4 */}
              {entry.rank <= 4 && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-50" />
              )}
              
              {/* Enhanced current user highlighting */}
              {entry.isCurrentUser && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/10 via-cyan-500/10 to-purple-500/10 animate-pulse" />
              )}
            </article>
          ))}
        </div>
        
        {/* View All Button */}
        <footer className="p-4 bg-white/5 mt-4">
          <Link 
            href="/leaderboard" 
            aria-label={generateAriaLabel.link('full leaderboard page', 'View complete leaderboard with all users')}
            className="w-full flex items-center justify-center space-x-2 text-cyan-400 hover:text-cyan-300 font-medium transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 rounded-md p-2"
          >
            <span>View Full Leaderboard</span>
            <svg 
              className="h-4 w-4" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
              aria-hidden="true"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </footer>
      </div>
    </section>
  );
}