'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { X, Check, Clock, SkipForward } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EngagementActions {
  liked: boolean;
  shared: boolean;
  completed: boolean;
}

interface EngagementConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (actions: 'yes' | 'no' | 'skip' | EngagementActions) => void;
  platform: string;
  videoTitle: string;
  action: string;
  bonusPoints?: number;
}

const PLATFORM_CONFIG = {
  youtube: {
    name: 'YouTube',
    icon: '📺',
    color: 'from-red-500 to-red-600'
  },
  tiktok: {
    name: 'TikTok',
    icon: '🎵',
    color: 'from-pink-500 to-purple-600'
  },
  rumble: {
    name: 'Rumble',
    icon: '⚡',
    color: 'from-green-500 to-green-600'
  }
};

export default function EngagementConfirmationModal({
  isOpen,
  onClose: _onClose,
  onConfirm,
  platform,
  action,
  videoTitle,
  bonusPoints = 2
}: EngagementConfirmationModalProps) {
  const [mounted, setMounted] = useState(false);
  const [timeLeft, setTimeLeft] = useState(15); // 15 second timer

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!isOpen) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          // Auto-skip when time runs out
          onConfirm('skip');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Reset timer when modal opens
    setTimeLeft(15);

    return () => clearInterval(timer);
  }, [isOpen, onConfirm]);

  // Handle keyboard events
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          onConfirm('skip');
          break;
        case '1':
        case 'y':
        case 'Y':
          onConfirm('yes');
          break;
        case '2':
        case 'n':
        case 'N':
          onConfirm('no');
          break;
        case '3':
        case 's':
        case 'S':
          onConfirm('skip');
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onConfirm]);

  if (!mounted || !isOpen) return null;

  const platformConfig = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG] || {
    name: platform,
    icon: '📺',
    color: 'from-blue-500 to-blue-600'
  };

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
         role="dialog"
         aria-modal="true"
         aria-labelledby="confirmation-title"
         aria-describedby="confirmation-description">
      <div className="relative w-full max-w-md">
        {/* Background glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-cyan-600/20 to-purple-600/20 rounded-2xl blur-xl" />
        
        {/* Main modal */}
        <div className="relative bg-gray-900/95 backdrop-blur-xl border-2 border-purple-500/30 rounded-2xl p-6 shadow-2xl">
          {/* Close button */}
          <button
            onClick={() => onConfirm('skip')}
            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white transition-colors"
            aria-label="Close confirmation"
          >
            <X className="h-4 w-4" />
          </button>

          {/* Timer */}
          <div className="absolute top-4 left-4 flex items-center space-x-1 text-xs text-gray-400">
            <Clock className="h-3 w-3" />
            <span>{timeLeft}s</span>
          </div>

          {/* Content */}
          <div className="text-center space-y-4 mt-8">
            {/* Platform icon */}
            <div className={cn(
              "inline-flex items-center justify-center w-16 h-16 rounded-full text-2xl",
              `bg-gradient-to-r ${platformConfig.color}`
            )}>
              {platformConfig.icon}
            </div>

            {/* Title */}
            <div>
              <h2 id="confirmation-title" className="text-xl font-bold text-white mb-2">
                Did you {action} this video?
              </h2>
              <p id="confirmation-description" className="text-gray-300 text-sm leading-relaxed">
                We opened <strong>{platformConfig.name}</strong> for you to {action} <br />
                {videoTitle.length > 50 ? `${videoTitle.slice(0, 50)}...` : videoTitle}
              </p>
            </div>

            {/* Honest question message */}
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
              <p className="text-blue-200 text-sm">
                🤝 We&apos;re asking honestly - this helps us track community engagement!
              </p>
            </div>

            {/* Action buttons */}
            <div className="space-y-3 pt-2">
              {/* Yes button */}
              <button
                onClick={() => onConfirm('yes')}
                className={cn(
                  "w-full flex items-center justify-center space-x-3 py-3 px-4 rounded-xl text-white font-medium transition-all duration-200",
                  "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500",
                  "focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-900",
                  "hover:scale-105 hover:shadow-lg active:scale-95"
                )}
              >
                <Check className="h-5 w-5" />
                <span>Yes, I did! 🎉</span>
                <span className="text-green-200 text-sm">(+{bonusPoints} bonus)</span>
              </button>

              {/* Not yet button */}
              <button
                onClick={() => onConfirm('no')}
                className={cn(
                  "w-full flex items-center justify-center space-x-3 py-3 px-4 rounded-xl text-white font-medium transition-all duration-200",
                  "bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400",
                  "focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 focus:ring-offset-gray-900",
                  "hover:scale-105 hover:shadow-lg active:scale-95"
                )}
              >
                <Clock className="h-5 w-5" />
                <span>Not yet ⏱️</span>
                <span className="text-yellow-200 text-sm">(no bonus)</span>
              </button>

              {/* Skip button */}
              <button
                onClick={() => onConfirm('skip')}
                className={cn(
                  "w-full flex items-center justify-center space-x-3 py-2 px-4 rounded-xl text-gray-400 font-medium transition-all duration-200",
                  "bg-gray-800 hover:bg-gray-700",
                  "focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-900",
                  "hover:text-gray-300"
                )}
              >
                <SkipForward className="h-4 w-4" />
                <span>Skip</span>
              </button>
            </div>

            {/* Keyboard shortcuts */}
            <div className="text-xs text-gray-500 mt-4 space-y-1">
              <div>Keyboard shortcuts: <kbd className="px-1 py-0.5 bg-gray-700 rounded">1</kbd> Yes, <kbd className="px-1 py-0.5 bg-gray-700 rounded">2</kbd> Not yet, <kbd className="px-1 py-0.5 bg-gray-700 rounded">3</kbd> Skip</div>
            </div>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
}
