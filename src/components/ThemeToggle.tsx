'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

export default function ThemeToggle() {
  const { theme, toggleHighContrast } = useTheme();
  const isHighContrast = theme === 'high-contrast';

  return (
    <div className="flex items-center justify-between p-4 sm:p-6 bg-gray-900 rounded-lg border border-gray-800">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          {isHighContrast ? (
            <EyeSlashIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
          ) : (
            <EyeIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-medium text-white">High Contrast Mode</h3>
          <p className="text-sm text-gray-400">
            {isHighContrast 
              ? 'High contrast mode is enabled for better visibility' 
              : 'Enable high contrast mode for improved accessibility'
            }
          </p>
        </div>
      </div>
      <div className="ml-4 flex-shrink-0">
        <button
          type="button"
          onClick={toggleHighContrast}
          className={`
            relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent 
            transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
            ${isHighContrast ? 'bg-purple-600' : 'bg-gray-600'}
          `}
          role="switch"
          aria-checked={isHighContrast}
          aria-labelledby="high-contrast-label"
          aria-describedby="high-contrast-description"
        >
          <span className="sr-only">
            {isHighContrast ? 'Disable high contrast mode' : 'Enable high contrast mode'}
          </span>
          <span
            className={`
              pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 
              transition duration-200 ease-in-out
              ${isHighContrast ? 'translate-x-5' : 'translate-x-0'}
            `}
          />
        </button>
      </div>
    </div>
  );
}