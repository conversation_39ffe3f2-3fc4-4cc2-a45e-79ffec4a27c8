'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Plus, X, Video, Youtube, Music, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  generateAriaLabel, 
  formAccessibility, 
  buttonAccessibility
} from '@/lib/accessibility';

interface PlatformUrls {
  youtubeUrl?: string;
  tiktokUrl?: string;
  rumbleUrl?: string;
}

interface UserVideoCreationFormProps {
  onVideoCreated?: () => void;
  className?: string;
}

export default function UserVideoCreationForm({ onVideoCreated, className }: UserVideoCreationFormProps) {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [thumbnailUrl, setThumbnailUrl] = useState('');
  const [platforms, setPlatforms] = useState<PlatformUrls>({});

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setThumbnailUrl('');
    setPlatforms({});
    setError(null);
    setSuccess(null);
  };

  const handleClose = () => {
    setIsOpen(false);
    resetForm();
  };

  const handlePlatformUrlChange = (platform: keyof PlatformUrls, value: string) => {
    setPlatforms(prev => ({
      ...prev,
      [platform]: value.trim() || undefined
    }));
  };

  const validateForm = () => {
    if (!title.trim()) {
      setError('Title is required');
      return false;
    }

    // Check if at least one platform URL is provided
    const hasValidUrl = Object.values(platforms).some(url => url && url.trim());
    if (!hasValidUrl) {
      setError('At least one platform URL is required');
      return false;
    }

    // Basic URL validation
    const urlPattern = /^https?:\/\/.+/;
    for (const [platform, url] of Object.entries(platforms)) {
      if (url && !urlPattern.test(url)) {
        setError(`Invalid ${platform} URL format`);
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setError(null);

    try {
      // Create the video
      const videoResponse = await fetch('/api/videos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          description: description.trim() || undefined,
          thumbnailUrl: thumbnailUrl.trim() || undefined,
          platforms,
          type: 'user'
        }),
      });

      if (!videoResponse.ok) {
        const errorData = await videoResponse.json();
        // Extract the error message from the API error response format
        const errorMessage = errorData.error?.message || errorData.message || 'Failed to create video';
        throw new Error(errorMessage);
      }

      const newVideo = await videoResponse.json();

      // Send notifications to other users
      try {
        await fetch('/api/notifications/send-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            videoIds: [newVideo.id],
            type: 'user'
          }),
        });
      } catch (notificationError) {
        console.error('Failed to send notifications:', notificationError);
        // Don't fail the whole operation if notifications fail
      }

      setSuccess('Video created successfully! Other users have been notified.');
      resetForm();
      setIsOpen(false);
      
      // Call the callback to refresh the parent component
      if (onVideoCreated) {
        onVideoCreated();
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create video');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!session) {
    return null;
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'youtubeUrl':
        return <Youtube className="h-4 w-4 text-red-500" />;
      case 'tiktokUrl':
        return <Music className="h-4 w-4 text-pink-500" />;
      case 'rumbleUrl':
        return <Zap className="h-4 w-4 text-green-500" />;
      default:
        return <Video className="h-4 w-4" />;
    }
  };

  const getPlatformLabel = (platform: string) => {
    switch (platform) {
      case 'youtubeUrl':
        return 'YouTube URL';
      case 'tiktokUrl':
        return 'TikTok URL';
      case 'rumbleUrl':
        return 'Rumble URL';
      default:
        return platform;
    }
  };

  const fieldIds = {
    title: formAccessibility.generateFieldIds('video-title'),
    description: formAccessibility.generateFieldIds('video-description'),
    thumbnailUrl: formAccessibility.generateFieldIds('video-thumbnail'),
    youtubeUrl: formAccessibility.generateFieldIds('youtube-url'),
    tiktokUrl: formAccessibility.generateFieldIds('tiktok-url'),
    rumbleUrl: formAccessibility.generateFieldIds('rumble-url')
  };

  return (
    <section className={className} aria-labelledby="video-creation-section">
      {/* Success Message */}
      {success && (
        <div 
          className="mb-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg"
          role="alert"
          aria-live="polite"
        >
          <p className="text-green-400 text-sm">{success}</p>
        </div>
      )}

      {/* Create Video Button */}
      {!isOpen && (
        <Button
          onClick={() => setIsOpen(true)}
          aria-label={generateAriaLabel.button('Open video creation form', 'Share your video with the community')}
          className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
        >
          <Plus className="h-5 w-5 mr-2" aria-hidden="true" />
          Share Your Video
        </Button>
      )}

      {/* Creation Form */}
      {isOpen && (
        <div 
          className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          role="dialog"
          aria-labelledby="video-creation-title"
          aria-describedby="video-creation-description"
        >
          <header className="flex justify-between items-center mb-6">
            <h3 
              id="video-creation-title"
              className="text-xl font-bold text-white flex items-center"
            >
              <Video className="h-6 w-6 mr-2 text-purple-400" aria-hidden="true" />
              Share Your Video
            </h3>
            <Button
              onClick={handleClose}
              variant="outline"
              size="sm"
              aria-label={generateAriaLabel.button('Close video creation form')}
              className="text-gray-400 hover:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
            >
              <X className="h-4 w-4" aria-hidden="true" />
            </Button>
          </header>

          <p 
            id="video-creation-description" 
            className="sr-only"
          >
            Fill out this form to share your video with the community. All fields marked with an asterisk are required.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6" noValidate>
            {/* Error Message */}
            {error && (
              <div 
                className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg"
                role="alert"
                aria-live="assertive"
              >
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            {/* Title */}
            <div>
              <label 
                htmlFor={fieldIds.title.field} 
                className="block text-sm font-medium text-gray-300 mb-2"
              >
                Video Title *
              </label>
              <input
                type="text"
                {...formAccessibility.getFieldAttributes(fieldIds.title.field, {
                  required: true,
                  invalid: !!error && error.includes('Title')
                })}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500 focus-visible:border-transparent"
                placeholder="Enter a catchy title for your video"
              />
            </div>

            {/* Description */}
            <div>
              <label 
                htmlFor={fieldIds.description.field} 
                className="block text-sm font-medium text-gray-300 mb-2"
              >
                Description
              </label>
              <textarea
                {...formAccessibility.getFieldAttributes(fieldIds.description.field, {
                  required: false
                })}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500 focus-visible:border-transparent resize-none"
                placeholder="Describe your video (optional)"
              />
            </div>

            {/* Platform URLs */}
            <fieldset>
              <legend className="block text-sm font-medium text-gray-300 mb-3">
                Platform URLs * (at least one required)
              </legend>
              <div className="space-y-4">
                {(['youtubeUrl', 'tiktokUrl', 'rumbleUrl'] as const).map((platform) => (
                  <div key={platform}>
                    <label 
                      htmlFor={fieldIds[platform].field} 
                      className="block text-sm font-medium text-gray-400 mb-2 flex items-center"
                    >
                      {getPlatformIcon(platform)}
                      <span className="ml-2">{getPlatformLabel(platform)}</span>
                    </label>
                    <input
                      type="url"
                      {...formAccessibility.getFieldAttributes(fieldIds[platform].field, {
                        required: false,
                        invalid: !!error && error.includes(platform)
                      })}
                      value={platforms[platform] || ''}
                      onChange={(e) => handlePlatformUrlChange(platform, e.target.value)}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500 focus-visible:border-transparent"
                      placeholder={`https://${platform === 'youtubeUrl' ? 'youtube.com' : platform === 'tiktokUrl' ? 'tiktok.com' : 'rumble.com'}/...`}
                    />
                  </div>
                ))}
              </div>
            </fieldset>

            {/* Thumbnail URL */}
            <div>
              <label 
                htmlFor={fieldIds.thumbnailUrl.field} 
                className="block text-sm font-medium text-gray-300 mb-2"
              >
                Thumbnail URL
              </label>
              <input
                type="url"
                {...formAccessibility.getFieldAttributes(fieldIds.thumbnailUrl.field, {
                  required: false,
                  invalid: !!error && error.includes('thumbnail')
                })}
                value={thumbnailUrl}
                onChange={(e) => setThumbnailUrl(e.target.value)}
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500 focus-visible:border-transparent"
                placeholder="https://example.com/thumbnail.jpg (optional)"
              />
            </div>

            {/* Form Actions */}
            <div className="flex space-x-4 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                aria-label={generateAriaLabel.button(
                  isSubmitting ? 'Creating video' : 'Create and share video',
                  'Submit form to create video and notify other users'
                )}
                {...buttonAccessibility.getButtonAttributes({
                  disabled: isSubmitting,
                  loading: isSubmitting
                })}
                className={cn(
                  "flex-1 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
                  isSubmitting && "opacity-50 cursor-not-allowed"
                )}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" aria-hidden="true" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" aria-hidden="true" />
                    Create & Share Video
                  </>
                )}
              </Button>
              
              <Button
                type="button"
                onClick={handleClose}
                variant="outline"
                aria-label={generateAriaLabel.button('Cancel video creation', 'Close form without saving')}
                className="px-6 py-3 border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}
    </section>
  );
}