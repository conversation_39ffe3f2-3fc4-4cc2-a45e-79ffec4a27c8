'use client';

import { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FeatureToggleButtonProps {
  videoId: string;
  isFeatured: boolean;
}

export default function FeatureToggleButton({ videoId, isFeatured: initialIsFeatured }: FeatureToggleButtonProps) {
  const [isFeatured, setIsFeatured] = useState(initialIsFeatured);
  const [isLoading, setIsLoading] = useState(false);

  const handleToggle = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/videos/${videoId}/feature`, {
        method: 'PATCH',
      });

      if (response.ok) {
        const updatedVideo = await response.json();
        setIsFeatured(updatedVideo.isFeatured);
      } else {
        console.error('Failed to toggle feature status');
      }
    } catch (error) {
      console.error('Error toggling feature status:', error);
    }
    setIsLoading(false);
  };

  return (
    <button
      onClick={handleToggle}
      disabled={isLoading}
      className={cn(
        'p-2 rounded-full transition-colors',
        isFeatured ? 'text-yellow-400 bg-yellow-400/20' : 'text-gray-500 hover:bg-gray-700',
        isLoading && 'animate-pulse'
      )}
    >
      <Star className="h-5 w-5" />
    </button>
  );
}
