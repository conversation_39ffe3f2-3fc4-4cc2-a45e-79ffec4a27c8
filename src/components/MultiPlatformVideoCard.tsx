'use client';

import React, { useState } from 'react';
import GamingCard from './GamingCard';
import { VideoErrorBoundary } from './ErrorBoundary';
import { HighlightedText } from '@/lib/search-utils';
import { useSwipeableContainer } from '@/hooks/useSwipeGestures';
import VideoHoverPreview from './VideoHoverPreview';
import VideoPreviewModal from './VideoPreviewModal';
import { VideoThumbnail } from './OptimizedImage';
import { 
  generateAriaLabel, 
  buttonAccessibility
} from '@/lib/accessibility';
import { useScreenReader } from '@/hooks/useScreenReader';


interface Video {
  id: string;
  title: string;
  description: string | null;
  platforms: {
    youtube?: string;
    tiktok?: string;
    rumble?: string;
  };
  thumbnailUrl?: string;
  createdAt: string;
}

interface UserInteractions {
  [platform: string]: {
    liked: boolean;
    shared: boolean;
  };
}

interface MultiPlatformVideoCardProps {
  video: Video;
  onPlatformClick?: (platform: string, action: 'like' | 'share') => void; // Legacy - deprecated
  onEngageClick?: (platform: string, url: string) => void; // New engagement handler
  userInteractions: UserInteractions;
  searchQuery?: string;
  onCompleteVideo?: (videoId: string) => void;
  showCompleteButton?: boolean;
  useEnhancedEngagement?: boolean; // Toggle between old and new system
}

// Platform configuration with icons and colors
const PLATFORMS = {
  youtube: {
    name: 'YouTube',
    icon: '▶️',
    color: 'from-red-600 to-red-700',
    hoverColor: 'hover:from-red-700 hover:to-red-800',
  },
  tiktok: {
    name: 'TikTok',
    icon: '🎵',
    color: 'from-black to-gray-800',
    hoverColor: 'hover:from-gray-800 hover:to-gray-900',
  },
  rumble: {
    name: 'Rumble',
    icon: '🎬',
    color: 'from-green-600 to-green-700',
    hoverColor: 'hover:from-green-700 hover:to-green-800',
  },
};

export default function MultiPlatformVideoCard({ 
  video, 
  onPlatformClick, 
  onEngageClick,
  userInteractions,
  searchQuery,
  onCompleteVideo,
  showCompleteButton = false,
  useEnhancedEngagement = false
}: MultiPlatformVideoCardProps) {
  const [isLoading, setIsLoading] = useState<{[key: string]: boolean}>({});
  const [currentPlatformIndex, setCurrentPlatformIndex] = useState(0);
  const [isCompleting, setIsCompleting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Screen reader support
  const { announceAction, announceError, announceSuccess } = useScreenReader();

  // Get available platforms for this video
  const availablePlatforms = Object.entries(video.platforms)
    .filter(([_, url]) => url)
    .map(([platform, url]) => ({ platform, url: url! }));

  // Touch gesture handlers for platform switching
  const handleSwipeLeft = () => {
    if (availablePlatforms.length > 1) {
      setCurrentPlatformIndex((prev) => 
        prev === availablePlatforms.length - 1 ? 0 : prev + 1
      );
    }
  };

  const handleSwipeRight = () => {
    if (availablePlatforms.length > 1) {
      setCurrentPlatformIndex((prev) => 
        prev === 0 ? availablePlatforms.length - 1 : prev - 1
      );
    }
  };

  // Set up swipe gestures for the card
  const swipeContainerRef = useSwipeableContainer({
    onSwipeLeft: handleSwipeLeft,
    onSwipeRight: handleSwipeRight,
    threshold: 50
  });

  const handlePlatformAction = async (platform: string, action: 'like' | 'share', url: string) => {
    const key = `${platform}-${action}`;
    const platformName = PLATFORMS[platform as keyof typeof PLATFORMS]?.name || platform;
    
    setIsLoading(prev => ({ ...prev, [key]: true }));
    announceAction(`${action === 'like' ? 'Liking' : 'Sharing'} video on ${platformName}`);

    try {
      // Track the interaction (only if callback exists)
      if (onPlatformClick) {
        await onPlatformClick(platform, action);
      }
      
      // Open the platform URL in a new tab
      if (action === 'like') {
        // For likes, open the video directly
        window.open(url, '_blank', 'noopener,noreferrer');
        announceSuccess(`Video liked on ${platformName}. Opening ${platformName} in new tab.`);
      } else {
        // For shares, try to open platform-specific share URLs
        const shareUrl = getShareUrl(platform, url);
        window.open(shareUrl, '_blank', 'noopener,noreferrer');
        announceSuccess(`Video shared on ${platformName}. Opening share dialog in new tab.`);
      }
    } catch (error) {
      console.error(`Error tracking ${action} on ${platform}:`, error);
      announceError(`Failed to track ${action} on ${platformName}, but opening video anyway`);
      
      // Still open the URL even if tracking fails
      const targetUrl = action === 'share' ? getShareUrl(platform, url) : url;
      window.open(targetUrl, '_blank', 'noopener,noreferrer');
    } finally {
      setIsLoading(prev => ({ ...prev, [key]: false }));
    }
  };

  const getShareUrl = (platform: string, videoUrl: string): string => {
    switch (platform) {
      case 'youtube':
        // Extract video ID from YouTube URL and create share URL
        const youtubeMatch = videoUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        const videoId = youtubeMatch?.[1];
        return videoId 
          ? `https://www.youtube.com/share?v=${videoId}`
          : videoUrl;
      
      case 'tiktok':
        // TikTok share URL format
        return `https://www.tiktok.com/share?url=${encodeURIComponent(videoUrl)}`;
      
      case 'rumble':
        // Rumble doesn't have a specific share API, so use generic share
        return `https://rumble.com/share?url=${encodeURIComponent(videoUrl)}`;
      
      default:
        return videoUrl;
    }
  };

  const isButtonDisabled = (platform: string, action: 'like' | 'share'): boolean => {
    const interaction = userInteractions[platform];
    if (!interaction) return false;
    
    return action === 'like' ? interaction.liked : interaction.shared;
  };

  const isButtonLoading = (platform: string, action: 'like' | 'share'): boolean => {
    return isLoading[`${platform}-${action}`] || false;
  };

  const handleCompleteVideo = async () => {
    if (!onCompleteVideo) return;
    
    setIsCompleting(true);
    announceAction(`Marking video "${video.title}" as complete`);
    
    try {
      await onCompleteVideo(video.id);
      announceSuccess(`Video "${video.title}" marked as complete and removed from your list`);
    } catch (error) {
      console.error('Error completing video:', error);
      announceError(`Failed to mark video "${video.title}" as complete`);
    } finally {
      setIsCompleting(false);
    }
  };

  return (
    <VideoErrorBoundary>
      <div ref={swipeContainerRef} className="touch-pan-y">
        <GamingCard className="group overflow-hidden">
          {/* Thumbnail with Hover Preview */}
          <VideoHoverPreview 
            video={video} 
            onPreviewClick={() => setIsModalOpen(true)}
            delay={300}
          >
            <div className="relative mb-3 sm:mb-4">
              <VideoThumbnail
                video={video}
                width={400}
                height={225}
                className="w-full aspect-video"
                onClick={() => setIsModalOpen(true)}
                showPlayOverlay={true}
              />

              {/* Platform indicator for mobile swipe */}
              {availablePlatforms.length > 1 && (
                <div className="absolute top-2 right-2 sm:hidden">
                  <div className="bg-black/60 backdrop-blur-sm rounded-full px-2 py-1 text-xs text-white">
                    {currentPlatformIndex + 1}/{availablePlatforms.length}
                  </div>
                </div>
              )}
            </div>
          </VideoHoverPreview>
          
          {/* Content */}
          <div className="space-y-3 sm:space-y-4">
            <header>
              <h3 
                id={`video-title-${video.id}`}
                className="font-semibold text-white line-clamp-2 group-hover:text-cyan-300 transition-colors mb-2 text-sm sm:text-base"
              >
                {searchQuery ? (
                  <HighlightedText 
                    text={video.title} 
                    searchQuery={searchQuery}
                    className="bg-yellow-400/20 text-yellow-300 px-1 rounded"
                  />
                ) : (
                  video.title
                )}
              </h3>
              
              {video.description && (
                <p className="text-xs sm:text-sm text-gray-400 line-clamp-2 mb-2 sm:mb-3">
                  {searchQuery ? (
                    <HighlightedText 
                      text={video.description} 
                      searchQuery={searchQuery}
                      className="bg-yellow-400/20 text-yellow-300 px-1 rounded"
                    />
                  ) : (
                    video.description
                  )}
                </p>
              )}
              
              <span className="text-xs text-gray-500">
                {new Date(video.createdAt).toLocaleDateString()}
              </span>
            </header>

            {/* Platform-specific buttons */}
            {availablePlatforms.length > 0 && (
              <div className="space-y-2 sm:space-y-3">
                <div className="text-xs text-gray-400 font-medium">
                  Available on:
                  {availablePlatforms.length > 1 && (
                    <span className="hidden sm:inline ml-2 text-gray-500">
                      (Swipe to switch platforms on mobile)
                    </span>
                  )}
                </div>
                
                {/* Mobile: Show current platform only */}
                <div className="sm:hidden">
                  {availablePlatforms.length > 0 && (() => {
                    const { platform, url } = availablePlatforms[currentPlatformIndex];
                    const platformConfig = PLATFORMS[platform as keyof typeof PLATFORMS];
                    if (!platformConfig) return null;

                    const likeDisabled = isButtonDisabled(platform, 'like');
                    const shareDisabled = isButtonDisabled(platform, 'share');
                    const likeLoading = isButtonLoading(platform, 'like');
                    const shareLoading = isButtonLoading(platform, 'share');

                    return (
                      <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <span className="text-xl">{platformConfig.icon}</span>
                          <span className="text-sm font-medium text-gray-300">
                            {platformConfig.name}
                          </span>
                        </div>
                        
                        <div className="flex space-x-2">
                          {useEnhancedEngagement ? (
                            // Enhanced Engagement: Single "Engage" button
                            <button
                              onClick={() => onEngageClick?.(platform, url)}
                              aria-label={generateAriaLabel.button(
                                'Engage with video',
                                `${video.title} on ${platformConfig.name}`
                              )}
                              aria-describedby={`video-title-${video.id}`}
                              {...buttonAccessibility.getButtonAttributes({})}
                              className={`
                                flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
                                flex items-center justify-center space-x-2 min-h-[44px]
                                focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                                bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white active:scale-95 hover:shadow-lg
                              `}
                            >
                              <span>🚀</span>
                              <span>Engage</span>
                            </button>
                          ) : (
                            // Legacy: Separate Like and Share buttons
                            <>
                              {/* Like Button */}
                              <button
                                onClick={() => handlePlatformAction(platform, 'like', url)}
                                disabled={likeDisabled || likeLoading}
                                aria-label={generateAriaLabel.button(
                                  likeDisabled ? 'Already liked' : 'Like video',
                                  `${video.title} on ${platformConfig.name}`
                                )}
                                aria-describedby={`video-title-${video.id}`}
                                {...buttonAccessibility.getButtonAttributes({
                                  disabled: likeDisabled || likeLoading,
                                  loading: likeLoading
                                })}
                                className={`
                                  px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
                                  flex items-center space-x-2 min-h-[44px] min-w-[44px]
                                  focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                                  ${likeDisabled 
                                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                                    : `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white active:scale-95`
                                  }
                                  ${likeLoading ? 'opacity-70' : ''}
                                `}
                              >
                                {likeLoading ? (
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <>
                                    <span>👍</span>
                                    <span>{likeDisabled ? 'Liked' : 'Like'}</span>
                                  </>
                                )}
                              </button>

                              {/* Share Button */}
                              <button
                                onClick={() => handlePlatformAction(platform, 'share', url)}
                                disabled={shareDisabled || shareLoading}
                                aria-label={generateAriaLabel.button(
                                  shareDisabled ? 'Already shared' : 'Share video',
                                  `${video.title} on ${platformConfig.name}`
                                )}
                                aria-describedby={`video-title-${video.id}`}
                                {...buttonAccessibility.getButtonAttributes({
                                  disabled: shareDisabled || shareLoading,
                                  loading: shareLoading
                                })}
                                className={`
                                  px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
                                  flex items-center space-x-2 min-h-[44px] min-w-[44px]
                                  focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                                  ${shareDisabled 
                                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                                    : `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white active:scale-95`
                                  }
                                  ${shareLoading ? 'opacity-70' : ''}
                                `}
                              >
                                {shareLoading ? (
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <>
                                    <span>🔗</span>
                                    <span>{shareDisabled ? 'Shared' : 'Share'}</span>
                                  </>
                                )}
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>

                {/* Desktop: Show all platforms */}
                <div className="hidden sm:block space-y-2">
                  {availablePlatforms.map(({ platform, url }) => {
                    const platformConfig = PLATFORMS[platform as keyof typeof PLATFORMS];
                    if (!platformConfig) return null;

                    const likeDisabled = isButtonDisabled(platform, 'like');
                    const shareDisabled = isButtonDisabled(platform, 'share');
                    const likeLoading = isButtonLoading(platform, 'like');
                    const shareLoading = isButtonLoading(platform, 'share');

                    return (
                      <div key={platform} className="flex items-center justify-between p-2 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{platformConfig.icon}</span>
                          <span className="text-sm font-medium text-gray-300">
                            {platformConfig.name}
                          </span>
                        </div>
                        
                        <div className="flex space-x-2">
                          {useEnhancedEngagement ? (
                            // Enhanced Engagement: Single "Engage" button
                            <button
                              onClick={() => onEngageClick?.(platform, url)}
                              aria-label={generateAriaLabel.button(
                                'Engage with video',
                                `${video.title} on ${platformConfig.name}`
                              )}
                              aria-describedby={`video-title-${video.id}`}
                              {...buttonAccessibility.getButtonAttributes({})}
                              className={`
                                flex-1 px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200
                                flex items-center justify-center space-x-1 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                                bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white hover:scale-105 hover:shadow-lg
                              `}
                            >
                              <span>🚀</span>
                              <span>Engage</span>
                            </button>
                          ) : (
                            // Legacy: Separate Like and Share buttons
                            <>
                              {/* Like Button */}
                              <button
                                onClick={() => handlePlatformAction(platform, 'like', url)}
                                disabled={likeDisabled || likeLoading}
                                aria-label={generateAriaLabel.button(
                                  likeDisabled ? 'Already liked' : 'Like video',
                                  `${video.title} on ${platformConfig.name}`
                                )}
                                aria-describedby={`video-title-${video.id}`}
                                {...buttonAccessibility.getButtonAttributes({
                                  disabled: likeDisabled || likeLoading,
                                  loading: likeLoading
                                })}
                                className={`
                                  px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200
                                  flex items-center space-x-1 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                                  ${likeDisabled 
                                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                                    : `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white hover:scale-105 hover:shadow-lg`
                                  }
                                  ${likeLoading ? 'opacity-70' : ''}
                                `}
                              >
                                {likeLoading ? (
                                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <>
                                    <span>👍</span>
                                    <span>{likeDisabled ? 'Liked' : 'Like'}</span>
                                  </>
                                )}
                              </button>

                              {/* Share Button */}
                              <button
                                onClick={() => handlePlatformAction(platform, 'share', url)}
                                disabled={shareDisabled || shareLoading}
                                aria-label={generateAriaLabel.button(
                                  shareDisabled ? 'Already shared' : 'Share video',
                                  `${video.title} on ${platformConfig.name}`
                                )}
                                aria-describedby={`video-title-${video.id}`}
                                {...buttonAccessibility.getButtonAttributes({
                                  disabled: shareDisabled || shareLoading,
                                  loading: shareLoading
                                })}
                                className={`
                                  px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200
                                  flex items-center space-x-1 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                                  ${shareDisabled 
                                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                                    : `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white hover:scale-105 hover:shadow-lg`
                                  }
                                  ${shareLoading ? 'opacity-70' : ''}
                                `}
                              >
                                {shareLoading ? (
                                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <>
                                    <span>🔗</span>
                                    <span>{shareDisabled ? 'Shared' : 'Share'}</span>
                                  </>
                                )}
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Fallback if no platforms available */}
            {availablePlatforms.length === 0 && (
              <div className="text-center py-4 text-gray-500 text-sm">
                No platform links available
              </div>
            )}

            {/* Complete Video Button */}
            {showCompleteButton && (
              <div className="pt-3 sm:pt-4 border-t border-gray-700">
                <button
                  onClick={handleCompleteVideo}
                  disabled={isCompleting}
                  aria-label={generateAriaLabel.button(
                    isCompleting ? 'Completing video' : 'Mark video as complete',
                    video.title
                  )}
                  aria-describedby={`video-title-${video.id}`}
                  {...buttonAccessibility.getButtonAttributes({
                    disabled: isCompleting,
                    loading: isCompleting,
                    describedBy: `video-title-${video.id}`
                  })}
                  className={`
                    w-full px-4 py-2 sm:py-3 rounded-lg text-sm sm:text-base font-medium transition-all duration-200
                    flex items-center justify-center space-x-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500
                    ${isCompleting 
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                      : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white hover:scale-105 hover:shadow-lg active:scale-95'
                    }
                  `}
                >
                  {isCompleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Completing...</span>
                    </>
                  ) : (
                    <>
                      <span>✅</span>
                      <span>Complete Video</span>
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </GamingCard>
      </div>

      {/* Video Preview Modal */}
      <VideoPreviewModal
        video={video}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onPlatformClick={onPlatformClick}
        userInteractions={userInteractions}
      />
    </VideoErrorBoundary>
  );
}