'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import { X, ExternalLink, ThumbsUp, Share2, Calendar, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFocusTrap } from '@/hooks/useKeyboardNavigation';
import { useModalAnnouncements } from '@/hooks/useScreenReader';
import { modalAccessibility, generateAriaLabel, buttonAccessibility } from '@/lib/accessibility';

interface Video {
  id: string;
  title: string;
  description: string | null;
  platforms: {
    youtube?: string;
    tiktok?: string;
    rumble?: string;
  };
  thumbnailUrl?: string;
  createdAt: string;
  user?: {
    name: string | null;
  };
  _count?: {
    likes: number;
    shares: number;
  };
}

interface VideoPreviewModalProps {
  video: Video;
  isOpen: boolean;
  onClose: () => void;
  onPlatformClick?: (platform: string, action: 'like' | 'share') => void;
  userInteractions?: {
    [platform: string]: {
      liked: boolean;
      shared: boolean;
    };
  };
}

// Platform configuration with icons and colors
const PLATFORMS = {
  youtube: {
    name: 'YouTube',
    icon: '▶️',
    color: 'from-red-600 to-red-700',
    hoverColor: 'hover:from-red-700 hover:to-red-800',
  },
  tiktok: {
    name: 'TikTok',
    icon: '🎵',
    color: 'from-black to-gray-800',
    hoverColor: 'hover:from-gray-800 hover:to-gray-900',
  },
  rumble: {
    name: 'Rumble',
    icon: '🎬',
    color: 'from-green-600 to-green-700',
    hoverColor: 'hover:from-green-700 hover:to-green-800',
  },
};

export default function VideoPreviewModal({
  video,
  isOpen,
  onClose,
  onPlatformClick,
  userInteractions = {}
}: VideoPreviewModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const { announceModalOpen, announceModalClose, announceModalAction } = useModalAnnouncements();

  // Get available platforms for this video
  const availablePlatforms = Object.entries(video.platforms)
    .filter(([_, url]) => url)
    .map(([platform, url]) => ({ platform, url: url! }));

  // Set up focus trap with enhanced keyboard navigation
  useFocusTrap(modalRef, isOpen, {
    initialFocus: closeButtonRef,
    escapeDeactivates: true,
    onEscape: onClose
  });

  const handlePlatformAction = useCallback((platform: string, action: 'like' | 'share', url: string) => {
    if (onPlatformClick) {
      onPlatformClick(platform, action);
    }
    
    // Open the platform URL
    if (action === 'like') {
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      const shareUrl = getShareUrl(platform, url);
      window.open(shareUrl, '_blank', 'noopener,noreferrer');
    }
  }, [onPlatformClick]);

  const getShareUrl = (platform: string, videoUrl: string): string => {
    switch (platform) {
      case 'youtube':
        const youtubeMatch = videoUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        const videoId = youtubeMatch?.[1];
        return videoId
          ? `https://www.youtube.com/share?v=${videoId}`
          : videoUrl;
      
      case 'tiktok':
        return `https://www.tiktok.com/share?url=${encodeURIComponent(videoUrl)}`;
      
      case 'rumble':
        return `https://rumble.com/share?url=${encodeURIComponent(videoUrl)}`;
      
      default:
        return videoUrl;
    }
  };

  const isButtonDisabled = useCallback((platform: string, action: 'like' | 'share'): boolean => {
    const interaction = userInteractions[platform];
    if (!interaction) return false;
    return action === 'like' ? interaction.liked : interaction.shared;
  }, [userInteractions]);

  // Enhanced keyboard navigation with platform shortcuts
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
          // Quick access to platforms (1-5)
          const platformIndex = parseInt(event.key) - 1;
          if (platformIndex < availablePlatforms.length) {
            const { platform, url } = availablePlatforms[platformIndex];
            const platformName = PLATFORMS[platform as keyof typeof PLATFORMS]?.name || platform;
            window.open(url, '_blank', 'noopener,noreferrer');
            announceModalAction(`Opening ${platformName} video`);
          }
          break;
        case 'l':
          // Quick like action for first platform
          if (availablePlatforms.length > 0 && onPlatformClick) {
            const { platform, url } = availablePlatforms[0];
            const platformName = PLATFORMS[platform as keyof typeof PLATFORMS]?.name || platform;
            if (!isButtonDisabled(platform, 'like')) {
              handlePlatformAction(platform, 'like', url);
              announceModalAction(`Liked video on ${platformName}`);
            }
          }
          break;
        case 's':
          // Quick share action for first platform
          if (availablePlatforms.length > 0 && onPlatformClick) {
            const { platform, url } = availablePlatforms[0];
            const platformName = PLATFORMS[platform as keyof typeof PLATFORMS]?.name || platform;
            if (!isButtonDisabled(platform, 'share')) {
              handlePlatformAction(platform, 'share', url);
              announceModalAction(`Shared video on ${platformName}`);
            }
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, availablePlatforms, onPlatformClick, announceModalAction, handlePlatformAction, isButtonDisabled]);

  // Announce modal state changes
  useEffect(() => {
    if (isOpen) {
      const platformCount = availablePlatforms.length;
      const description = `Video preview for ${video.title}. ${platformCount} platform${platformCount === 1 ? '' : 's'} available. Use number keys 1-${platformCount} to open platforms directly.`;
      announceModalOpen('Video Preview', description);
    } else {
      announceModalClose('Video Preview');
    }
  }, [isOpen, video.title, availablePlatforms.length, announceModalOpen, announceModalClose]);

  // Handle backdrop click
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };


  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
      onClick={handleBackdropClick}
      {...modalAccessibility.getModalAttributes('video-preview-modal', 'modal-title', 'modal-description')}
    >
      <div 
        ref={modalRef}
        className="relative w-full max-w-4xl max-h-[90vh] bg-gray-900 rounded-lg border border-gray-700 overflow-hidden shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 id="modal-title" className="text-xl font-bold text-white truncate pr-4">
            {video.title}
          </h2>
          <button
            ref={closeButtonRef}
            onClick={onClose}
            className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
            aria-label="Close preview"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          <div 
            id="modal-description" 
            className="sr-only"
          >
            Video preview for {video.title}. {availablePlatforms.length} platform{availablePlatforms.length === 1 ? '' : 's'} available. Use number keys to open platforms directly, L to like, S to share.
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
            {/* Left Column - Video Thumbnail */}
            <div className="space-y-4">
              <div className="relative rounded-lg overflow-hidden bg-gray-800">
                {video.thumbnailUrl ? (
                  <Image 
                    src={video.thumbnailUrl} 
                    alt={video.title}
                    width={600}
                    height={338}
                    className="w-full aspect-video object-cover"
                    priority
                  />
                ) : (
                  <div className="aspect-video bg-gradient-to-br from-purple-900/50 to-cyan-900/50 flex items-center justify-center">
                    <div className="text-8xl opacity-50">🎬</div>
                  </div>
                )}
                
                {/* Play overlay */}
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <div className="w-0 h-0 border-l-[12px] border-l-white border-y-[8px] border-y-transparent ml-1" />
                  </div>
                </div>
              </div>

              {/* Video Stats */}
              {video._count && (
                <div className="flex items-center space-x-6 text-sm text-gray-400">
                  <div className="flex items-center space-x-2">
                    <ThumbsUp className="h-4 w-4" />
                    <span>{video._count.likes} likes</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Share2 className="h-4 w-4" />
                    <span>{video._count.shares} shares</span>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Video Details */}
            <div className="space-y-6">
              {/* Description */}
              {video.description && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Description</h3>
                  <p className="text-gray-300 leading-relaxed">{video.description}</p>
                </div>
              )}

              {/* Metadata */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Calendar className="h-4 w-4" />
                  <span>Published {new Date(video.createdAt).toLocaleDateString()}</span>
                </div>
                
                {video.user?.name && (
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <User className="h-4 w-4" />
                    <span>Shared by {video.user.name}</span>
                  </div>
                )}
              </div>

              {/* Platform Actions */}
              {availablePlatforms.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Available Platforms</h3>
                  <div className="space-y-3">
                    {availablePlatforms.map(({ platform, url }, index) => {
                      const platformConfig = PLATFORMS[platform as keyof typeof PLATFORMS];
                      if (!platformConfig) return null;

                      const likeDisabled = isButtonDisabled(platform, 'like');
                      const shareDisabled = isButtonDisabled(platform, 'share');

                      return (
                        <div key={platform} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{platformConfig.icon}</span>
                            <div>
                              <span className="font-medium text-white">{platformConfig.name}</span>
                              <div className="text-xs text-gray-400">
                                Press {index + 1} to open directly
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex space-x-3">
                            {/* View Button */}
                            <button
                              onClick={() => {
                                window.open(url, '_blank', 'noopener,noreferrer');
                                announceModalAction(`Opening ${platformConfig.name} video`);
                              }}
                              aria-label={generateAriaLabel.button(
                                'View video',
                                `${video.title} on ${platformConfig.name}`
                              )}
                              className={cn(
                                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                                "flex items-center space-x-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500",
                                `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white hover:scale-105 hover:shadow-lg`
                              )}
                            >
                              <ExternalLink className="h-4 w-4" />
                              <span>View</span>
                            </button>

                            {/* Like Button */}
                            {onPlatformClick && (
                              <button
                                onClick={() => {
                                  handlePlatformAction(platform, 'like', url);
                                  announceModalAction(
                                    likeDisabled 
                                      ? `Already liked on ${platformConfig.name}` 
                                      : `Liked video on ${platformConfig.name}`
                                  );
                                }}
                                disabled={likeDisabled}
                                aria-label={generateAriaLabel.button(
                                  likeDisabled ? 'Already liked' : 'Like video',
                                  `${video.title} on ${platformConfig.name}`
                                )}
                                {...buttonAccessibility.getButtonAttributes({
                                  disabled: likeDisabled
                                })}
                                className={cn(
                                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                                  "flex items-center space-x-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500",
                                  likeDisabled 
                                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                                    : `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white hover:scale-105 hover:shadow-lg`
                                )}
                              >
                                <ThumbsUp className="h-4 w-4" />
                                <span>{likeDisabled ? 'Liked' : 'Like'}</span>
                              </button>
                            )}

                            {/* Share Button */}
                            {onPlatformClick && (
                              <button
                                onClick={() => {
                                  handlePlatformAction(platform, 'share', url);
                                  announceModalAction(
                                    shareDisabled 
                                      ? `Already shared on ${platformConfig.name}` 
                                      : `Shared video on ${platformConfig.name}`
                                  );
                                }}
                                disabled={shareDisabled}
                                aria-label={generateAriaLabel.button(
                                  shareDisabled ? 'Already shared' : 'Share video',
                                  `${video.title} on ${platformConfig.name}`
                                )}
                                {...buttonAccessibility.getButtonAttributes({
                                  disabled: shareDisabled
                                })}
                                className={cn(
                                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                                  "flex items-center space-x-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500",
                                  shareDisabled 
                                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                                    : `bg-gradient-to-r ${platformConfig.color} ${platformConfig.hoverColor} text-white hover:scale-105 hover:shadow-lg`
                                )}
                              >
                                <Share2 className="h-4 w-4" />
                                <span>{shareDisabled ? 'Shared' : 'Share'}</span>
                              </button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Keyboard shortcuts help */}
              <div className="p-4 bg-gray-800/50 rounded-lg">
                <h4 className="text-sm font-semibold text-white mb-2">Keyboard Shortcuts</h4>
                <div className="space-y-1 text-xs text-gray-400">
                  <div>• <kbd className="px-1 py-0.5 bg-gray-700 rounded text-xs">Esc</kbd> - Close preview</div>
                  <div>• <kbd className="px-1 py-0.5 bg-gray-700 rounded text-xs">Tab</kbd> - Navigate elements</div>
                  {onPlatformClick && (
                    <>
                      <div>• <kbd className="px-1 py-0.5 bg-gray-700 rounded text-xs">L</kbd> - Like on first platform</div>
                      <div>• <kbd className="px-1 py-0.5 bg-gray-700 rounded text-xs">S</kbd> - Share on first platform</div>
                    </>
                  )}
                  {availablePlatforms.map(({ platform }, index) => (
                    <div key={platform}>
                      • <kbd className="px-1 py-0.5 bg-gray-700 rounded text-xs">{index + 1}</kbd> - Open {PLATFORMS[platform as keyof typeof PLATFORMS]?.name}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}