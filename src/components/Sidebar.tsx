'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Home, Users, Settings, ListVideo, BarChart3 } from 'lucide-react';
import { Session } from 'next-auth';

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  adminOnly?: boolean;
}

export default function Sidebar({ session }: { session: Session | null }) {
  const pathname = usePathname();

  const navigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard', icon: <Home className="h-5 w-5" /> },
    { name: 'User Management', href: '/admin/users', icon: <Users className="h-5 w-5" />, adminOnly: true },
    { name: 'Manage Videos', href: '/admin/videos', icon: <ListVideo className="h-5 w-5" />, adminOnly: true },
    { name: 'Analytics', href: '/admin/analytics', icon: <BarChart3 className="h-5 w-5" />, adminOnly: true },
    { name: 'Settings', href: '/settings', icon: <Settings className="h-5 w-5" /> },
  ];

  return (
    <div className="hidden md:flex md:flex-shrink-0">
      <div className="flex flex-col w-64 border-r border-gray-700 bg-gray-900">
        <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
          <div className="flex items-center flex-shrink-0 px-4 mb-6">
            <h1 className="text-2xl font-bold text-white">NWA Promote</h1>
          </div>
          <div className="flex-1 flex flex-col">
            <nav className="flex-1 px-2 space-y-1">
              {navigation.map((item) => {
                if (item.name === 'Dashboard' && !session) {
                  return null;
                }
                const isActive = pathname === item.href;
                if (item.adminOnly && session?.user?.role !== 'ADMIN') {
                  return null;
                }
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      isActive
                        ? 'bg-gray-800 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white',
                      'group flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors duration-200'
                    )}
                  >
                    <span className="mr-3">
                      {item.icon}
                    </span>
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
