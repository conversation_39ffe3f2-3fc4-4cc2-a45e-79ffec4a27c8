'use client';

import { useState } from 'react';
import { Bell, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NotificationButtonProps {
  videoId: string;
  notificationSentAt: Date | null;
}

export default function NotificationButton({ videoId, notificationSentAt: initialNotificationSentAt }: NotificationButtonProps) {
  const [notificationSentAt, setNotificationSentAt] = useState(initialNotificationSentAt);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendNotification = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/videos/${videoId}/notify`, {
        method: 'POST',
      });

      if (response.ok) {
        setNotificationSentAt(new Date());
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to send notification');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      alert('An unexpected error occurred.');
    }
    setIsLoading(false);
  };

  return (
    <button
      onClick={handleSendNotification}
      disabled={isLoading || !!notificationSentAt}
      className={cn(
        'px-4 py-2 rounded-lg font-semibold flex items-center transition-colors',
        notificationSentAt
          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
          : 'bg-blue-600 text-white hover:bg-blue-700',
        isLoading && 'animate-pulse'
      )}
    >
      {notificationSentAt ? (
        <>
          <Check className="mr-2 h-5 w-5" />
          Sent
        </>
      ) : (
        <>
          <Bell className="mr-2 h-5 w-5" />
          Send Notification
        </>
      )}
    </button>
  );
}
