'use client';

import React, { useRef } from 'react';
import { X, Keyboard, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFocusTrap } from '@/hooks/useKeyboardNavigation';
import { useModalAnnouncements } from '@/hooks/useScreenReader';
import { modalAccessibility } from '@/lib/accessibility';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  description: string;
  category?: string;
}

interface KeyboardShortcutsHelpProps {
  shortcuts?: KeyboardShortcut[];
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const DEFAULT_SHORTCUTS: KeyboardShortcut[] = [
  // Navigation shortcuts
  {
    key: '/',
    description: 'Focus search input',
    category: 'Navigation'
  },
  {
    key: 'h',
    description: 'Go to home/dashboard',
    category: 'Navigation'
  },
  {
    key: 'l',
    description: 'Go to leaderboard',
    category: 'Navigation'
  },
  {
    key: 's',
    description: 'Go to settings',
    category: 'Navigation'
  },
  {
    key: 'n',
    description: 'Open notifications',
    category: 'Navigation'
  },
  
  // General shortcuts
  {
    key: 'Escape',
    description: 'Close modal or cancel action',
    category: 'General'
  },
  {
    key: 'Tab',
    description: 'Navigate to next element',
    category: 'General'
  },
  {
    key: 'Tab',
    shiftKey: true,
    description: 'Navigate to previous element',
    category: 'General'
  },
  {
    key: 'Enter',
    description: 'Activate focused element',
    category: 'General'
  },
  {
    key: 'Space',
    description: 'Activate button or checkbox',
    category: 'General'
  },
  
  // Video shortcuts
  {
    key: '1',
    description: 'Open first platform (in video preview)',
    category: 'Video Actions'
  },
  {
    key: '2',
    description: 'Open second platform (in video preview)',
    category: 'Video Actions'
  },
  {
    key: '3',
    description: 'Open third platform (in video preview)',
    category: 'Video Actions'
  },
  
  // List navigation
  {
    key: 'ArrowUp',
    description: 'Navigate up in lists',
    category: 'List Navigation'
  },
  {
    key: 'ArrowDown',
    description: 'Navigate down in lists',
    category: 'List Navigation'
  },
  {
    key: 'Home',
    description: 'Go to first item in list',
    category: 'List Navigation'
  },
  {
    key: 'End',
    description: 'Go to last item in list',
    category: 'List Navigation'
  },
  
  // Help
  {
    key: '?',
    shiftKey: true,
    description: 'Show this help dialog',
    category: 'Help'
  }
];

function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.altKey) parts.push('Alt');
  if (shortcut.shiftKey) parts.push('Shift');
  if (shortcut.metaKey) parts.push('Cmd');
  
  parts.push(shortcut.key);
  
  return parts.join(' + ');
}

export default function KeyboardShortcutsHelp({
  shortcuts = DEFAULT_SHORTCUTS,
  isOpen,
  onClose,
  className
}: KeyboardShortcutsHelpProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const { announceModalOpen, announceModalClose } = useModalAnnouncements();

  // Set up focus trap
  useFocusTrap(modalRef, isOpen, {
    initialFocus: closeButtonRef,
    escapeDeactivates: true,
    onEscape: onClose
  });

  // Announce modal state changes
  React.useEffect(() => {
    if (isOpen) {
      announceModalOpen('Keyboard Shortcuts Help', 'Use Tab to navigate through shortcuts, Escape to close');
    } else {
      announceModalClose('Keyboard Shortcuts Help');
    }
  }, [isOpen, announceModalOpen, announceModalClose]);

  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce((groups, shortcut) => {
    const category = shortcut.category || 'Other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(shortcut);
    return groups;
  }, {} as Record<string, KeyboardShortcut[]>);

  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
      onClick={handleBackdropClick}
      {...modalAccessibility.getModalAttributes('keyboard-shortcuts-modal')}
    >
      <div 
        ref={modalRef}
        className={cn(
          "relative w-full max-w-4xl max-h-[90vh] bg-gray-900 rounded-lg border border-gray-700 overflow-hidden shadow-2xl",
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <Keyboard className="h-6 w-6 text-purple-400" />
            <h2 
              id="keyboard-shortcuts-modal-title"
              className="text-xl sm:text-2xl font-bold text-white"
            >
              Keyboard Shortcuts
            </h2>
          </div>
          <button
            ref={closeButtonRef}
            onClick={onClose}
            className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500"
            aria-label="Close keyboard shortcuts help"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div 
          id="keyboard-shortcuts-modal-description"
          className="overflow-y-auto max-h-[calc(90vh-120px)] p-4 sm:p-6"
        >
          <p className="text-gray-300 mb-6">
            Use these keyboard shortcuts to navigate and interact with the application more efficiently.
            Press <kbd className="px-2 py-1 bg-gray-700 rounded text-sm">Tab</kbd> to navigate between elements,
            and <kbd className="px-2 py-1 bg-gray-700 rounded text-sm">Escape</kbd> to close dialogs.
          </p>

          <div className="grid gap-6 md:grid-cols-2">
            {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
              <div key={category} className="space-y-3">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2 text-purple-400" />
                  {category}
                </h3>
                
                <div className="space-y-2">
                  {categoryShortcuts.map((shortcut, index) => (
                    <div 
                      key={`${category}-${index}`}
                      className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg"
                    >
                      <span className="text-gray-300 text-sm">
                        {shortcut.description}
                      </span>
                      <kbd className="px-3 py-1.5 bg-gray-700 text-gray-200 rounded-md text-sm font-mono border border-gray-600">
                        {formatShortcut(shortcut)}
                      </kbd>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Additional tips */}
          <div className="mt-8 p-4 bg-purple-900/20 border border-purple-500/30 rounded-lg">
            <h4 className="text-lg font-semibold text-purple-300 mb-2">Tips</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>• Most shortcuts work globally throughout the application</li>
              <li>• Some shortcuts are context-specific (like video platform shortcuts)</li>
              <li>• Screen readers will announce important actions and navigation changes</li>
              <li>• Use Tab and Shift+Tab to navigate between interactive elements</li>
              <li>• Press Enter or Space to activate buttons and links</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-700 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-400">
              Press <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">?</kbd> anytime to show this help
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500"
            >
              Got it
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Floating help button component
 */
export function KeyboardShortcutsButton({ 
  onClick,
  className 
}: { 
  onClick: () => void;
  className?: string;
}) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "fixed bottom-4 right-4 z-40 p-3 bg-purple-600 hover:bg-purple-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-110 focus-visible:outline focus-visible:outline-2 focus-visible:outline-purple-500",
        className
      )}
      aria-label="Show keyboard shortcuts help"
      title="Keyboard shortcuts (Press ? for help)"
    >
      <Keyboard className="h-5 w-5" />
    </button>
  );
}