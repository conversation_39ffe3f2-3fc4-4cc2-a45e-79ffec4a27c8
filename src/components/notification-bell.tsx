'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
  userNotificationId: string; // This is what we need to send to mark as read
}

interface NotificationsApiResponse {
  notifications: Notification[];
  totalCount: number;
  hasMore: boolean;
}

interface CountApiResponse {
  count: number;
}

interface ApiError {
  error: string;
}

export default function NotificationBell() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const { data: session } = useSession();

  // Only render on client to prevent hydration issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch unread count
  useEffect(() => {
    // Don't fetch if session is not loaded yet or user is not authenticated
    if (!session || !session.user) {
      // Clear any existing data when user is not authenticated
      setUnreadCount(0);
      setNotifications([]);
      setError(null);
      return;
    }

    const fetchUnreadCount = async () => {
      try {
        const response = await fetch('/api/notifications/count');
        
        // Handle authentication redirects gracefully
        if (response.status === 307 || response.status === 401) {
          console.log('NotificationBell: User not authenticated, skipping count fetch');
          setUnreadCount(0);
          setError(null);
          return;
        }
        
        if (!response.ok) {
          let errorMessage = 'Failed to fetch unread count';
          try {
            const errorData: ApiError = await response.json();
            errorMessage = errorData.error || errorMessage;
          } catch {
            errorMessage = `HTTP ${response.status}: ${response.statusText || errorMessage}`;
          }
          throw new Error(errorMessage);
        }
        
        let data: CountApiResponse;
        try {
          data = await response.json();
        } catch {
          throw new Error('Invalid response format from count API');
        }
        
        setUnreadCount(data.count || 0);
        setError(null); // Clear any previous errors
      } catch (error) {
        console.error('Error fetching unread count:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch notifications');
      }
    };

    fetchUnreadCount();
    
    // Set up polling for new notifications with reduced frequency
    const interval = setInterval(fetchUnreadCount, 120000); // Check every 2 minutes instead of 30 seconds
    
    return () => clearInterval(interval);
  }, [session]);

  // Fetch notifications when dropdown opens
  useEffect(() => {
    // Don't fetch if dropdown is not open or session is not loaded yet or user is not authenticated
    if (!isOpen) return;
    if (!session) return; // Session is still loading
    if (!session.user) return; // User is not authenticated

    const fetchNotifications = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/notifications?limit=5&includeRead=false');
        
        if (!response.ok) {
          let errorMessage = 'Failed to fetch notifications';
          try {
            const errorData: ApiError = await response.json();
            errorMessage = errorData.error || errorMessage;
          } catch {
            errorMessage = `HTTP ${response.status}: ${response.statusText || errorMessage}`;
          }
          throw new Error(errorMessage);
        }
        
        let data: NotificationsApiResponse;
        try {
          data = await response.json();
        } catch {
          throw new Error('Invalid response format from notifications API');
        }
        
        setNotifications(data.notifications || []);
      } catch (error) {
        console.error('Error fetching notifications:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch notifications');
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotifications();
  }, [isOpen, session]);

  const markAsRead = async (userNotificationIds: string[]) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notificationIds: userNotificationIds })
      });

      if (!response.ok) {
        let errorMessage = 'Failed to mark notifications as read';
        try {
          const errorData: ApiError = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          errorMessage = `HTTP ${response.status}: ${response.statusText || errorMessage}`;
        }
        throw new Error(errorMessage);
      }

      // Update local state using the userNotificationId to match
      setNotifications(prev => 
        prev.map(n => 
          userNotificationIds.includes(n.userNotificationId) ? { ...n, isRead: true } : n
        )
      );
      setUnreadCount(prev => prev - userNotificationIds.length);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      setError(error instanceof Error ? error.message : 'Failed to mark notifications as read');
    }
  };

  const markAllAsRead = () => {
    const unreadIds = notifications.filter(n => !n.isRead).map(n => n.userNotificationId);
    if (unreadIds.length > 0) {
      markAsRead(unreadIds);
    }
  };

  // Don't render on server to prevent hydration issues
  if (!isClient) {
    return (
      <div className="p-2 rounded-full relative">
        <svg 
          className="w-6 h-6" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
          />
        </svg>
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 relative"
        aria-label="Notifications"
      >
        <svg 
          className="w-6 h-6" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
          />
        </svg>
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="text-lg font-medium">Notifications</h3>
            {notifications.some(n => !n.isRead) && (
              <button 
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Mark all as read
              </button>
            )}
          </div>
          
          {error && (
            <div className="px-4 py-3 text-center text-red-600 dark:text-red-400 text-sm">
              {error}
              <button 
                onClick={() => {
                  setError(null);
                  setIsOpen(false);
                  setIsOpen(true); // Trigger re-fetch
                }}
                className="block mx-auto mt-2 text-xs underline hover:no-underline"
              >
                Retry
              </button>
            </div>
          )}
          
          {isLoading ? (
            <div className="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading notifications...
              </div>
            </div>
          ) : notifications.length === 0 && !error ? (
            <div className="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
              No new notifications
            </div>
          ) : !error ? (
            <div className="max-h-96 overflow-y-auto">
              {notifications.map(notification => (
                <div 
                  key={notification.id} 
                  className={`px-4 py-3 border-b border-gray-200 dark:border-gray-700 ${
                    !notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex justify-between">
                    <h4 className="font-medium">{notification.title}</h4>
                    {!notification.isRead && (
                      <button 
                        onClick={() => markAsRead([notification.userNotificationId])}
                        className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Mark as read
                      </button>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {notification.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(notification.createdAt).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          ) : null}
          
          <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700 text-center">
            <a 
              href="/notifications" 
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              View all notifications
            </a>
          </div>
        </div>
      )}
    </div>
  );
}