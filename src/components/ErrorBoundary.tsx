'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';
import GamingCard from './GamingCard';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  resetKeys?: Array<string | number>;
  resetOnPropsChange?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  eventId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Generate a unique event ID for this error
    const eventId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.setState({
      error,
      errorInfo,
      eventId,
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Details');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetKeys, resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    // Reset error state if resetKeys have changed
    if (hasError && resetKeys && prevProps.resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== prevProps.resetKeys![index]
      );
      
      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }

    // Reset error state if any props have changed (when resetOnPropsChange is true)
    if (hasError && resetOnPropsChange && prevProps !== this.props) {
      this.resetErrorBoundary();
    }
  }

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      window.clearTimeout(this.resetTimeoutId);
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    });
  };

  handleRetry = () => {
    this.resetErrorBoundary();
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    const { hasError, error, errorInfo, eventId } = this.state;
    const { children, fallback, showDetails = false } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <GamingCard className="max-w-2xl w-full">
            <div className="text-center space-y-6">
              {/* Error Icon */}
              <div className="flex justify-center">
                <div className="w-20 h-20 bg-red-500/10 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-10 h-10 text-red-400" />
                </div>
              </div>

              {/* Error Message */}
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-white">
                  Oops! Something went wrong
                </h2>
                <p className="text-gray-400">
                    We encountered an unexpected error. Don&apos;t worry, our team has been notified.
                  </p>
                {eventId && (
                  <p className="text-xs text-gray-500 font-mono">
                    Error ID: {eventId}
                  </p>
                )}
              </div>

              {/* Error Details (Development/Debug Mode) */}
              {showDetails && error && (
                <div className="bg-gray-800/50 rounded-lg p-4 text-left">
                  <div className="flex items-center space-x-2 mb-3">
                    <Bug className="w-4 h-4 text-red-400" />
                    <span className="text-sm font-medium text-red-400">Error Details</span>
                  </div>
                  <div className="space-y-2 text-xs font-mono">
                    <div>
                      <span className="text-gray-400">Message:</span>
                      <div className="text-red-300 mt-1 p-2 bg-red-900/20 rounded">
                        {error.message}
                      </div>
                    </div>
                    {error.stack && (
                      <div>
                        <span className="text-gray-400">Stack Trace:</span>
                        <div className="text-gray-300 mt-1 p-2 bg-gray-900/50 rounded max-h-32 overflow-y-auto">
                          <pre className="whitespace-pre-wrap text-xs">
                            {error.stack}
                          </pre>
                        </div>
                      </div>
                    )}
                    {errorInfo?.componentStack && (
                      <div>
                        <span className="text-gray-400">Component Stack:</span>
                        <div className="text-gray-300 mt-1 p-2 bg-gray-900/50 rounded max-h-32 overflow-y-auto">
                          <pre className="whitespace-pre-wrap text-xs">
                            {errorInfo.componentStack}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={this.handleRetry}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg font-semibold text-white transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Try Again</span>
                </button>
                
                <button
                  onClick={this.handleReload}
                  className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold text-white transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Reload Page</span>
                </button>
                
                <button
                  onClick={this.handleGoHome}
                  className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold text-white transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <Home className="w-4 h-4" />
                  <span>Go Home</span>
                </button>
              </div>

              {/* Help Text */}
              <div className="text-sm text-gray-500 space-y-1">
                <p>If this problem persists, please contact support.</p>
                <p>Include the Error ID above when reporting this issue.</p>
              </div>
            </div>
          </GamingCard>
        </div>
      );
    }

    return children;
  }
}

export default ErrorBoundary;

// Functional component wrapper for easier use with hooks
export const ErrorBoundaryWrapper: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}> = ({ children, fallback, onError, showDetails }) => {
  return (
    <ErrorBoundary
      fallback={fallback}
      onError={onError}
      showDetails={showDetails}
    >
      {children}
    </ErrorBoundary>
  );
};

// Specialized error boundaries for different sections
export const DashboardErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    onError={(error, errorInfo) => {
      console.error('Dashboard Error:', error, errorInfo);
      // Could send to error reporting service here
    }}
    showDetails={process.env.NODE_ENV === 'development'}
  >
    {children}
  </ErrorBoundary>
);

export const VideoErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    fallback={
      <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
        <AlertTriangle className="w-8 h-8 text-red-400 mx-auto mb-3" />
        <h3 className="text-lg font-semibold text-white mb-2">Video Loading Error</h3>
        <p className="text-gray-400 mb-4">Unable to load video content</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
        >
          Retry
        </button>
      </div>
    }
    onError={(error, errorInfo) => {
      console.error('Video Error:', error, errorInfo);
    }}
  >
    {children}
  </ErrorBoundary>
);

export const AdminErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    fallback={
      <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
        <AlertTriangle className="w-8 h-8 text-red-400 mx-auto mb-3" />
        <h3 className="text-lg font-semibold text-white mb-2">Admin Panel Error</h3>
        <p className="text-gray-400 mb-4">Unable to load admin functionality</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
        >
          Reload Admin Panel
        </button>
      </div>
    }
    onError={(error, errorInfo) => {
      console.error('Admin Error:', error, errorInfo);
    }}
  >
    {children}
  </ErrorBoundary>
);