'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'default' | 'card' | 'table' | 'list' | 'stats' | 'video' | 'leaderboard';
  count?: number;
}

// Base skeleton component
const SkeletonBase: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("animate-pulse bg-white/10 rounded", className)} />
);

// Video card skeleton
const VideoCardSkeleton: React.FC = () => (
  <div className="glass-card p-6 animate-pulse">
    <div className="bg-white/10 h-48 w-full rounded-lg mb-4" />
    <div className="space-y-3">
      <div className="h-6 bg-white/10 rounded" />
      <div className="h-4 bg-white/10 rounded w-3/4" />
      <div className="flex justify-between items-center">
        <div className="h-3 bg-white/10 rounded w-20" />
        <div className="h-8 bg-white/10 rounded w-16" />
      </div>
    </div>
  </div>
);

// Table row skeleton
const TableRowSkeleton: React.FC = () => (
  <tr className="animate-pulse">
    <td className="px-6 py-4">
      <div className="h-4 w-4 bg-white/10 rounded" />
    </td>
    <td className="px-6 py-4">
      <div className="flex items-center space-x-3">
        <div className="w-16 h-9 bg-white/10 rounded" />
        <div className="space-y-2">
          <div className="h-4 bg-white/10 rounded w-32" />
          <div className="h-3 bg-white/10 rounded w-24" />
        </div>
      </div>
    </td>
    <td className="px-6 py-4">
      <div className="flex space-x-1">
        <div className="h-6 w-16 bg-white/10 rounded-full" />
        <div className="h-6 w-16 bg-white/10 rounded-full" />
      </div>
    </td>
    <td className="px-6 py-4">
      <div className="h-6 w-20 bg-white/10 rounded-full" />
    </td>
    <td className="px-6 py-4">
      <div className="h-8 w-16 bg-white/10 rounded" />
    </td>
  </tr>
);

// List item skeleton
const ListItemSkeleton: React.FC = () => (
  <div className="flex items-center space-x-3 p-4 animate-pulse">
    <div className="w-12 h-12 bg-white/10 rounded-full" />
    <div className="flex-1 space-y-2">
      <div className="h-4 bg-white/10 rounded w-3/4" />
      <div className="h-3 bg-white/10 rounded w-1/2" />
    </div>
    <div className="h-8 w-20 bg-white/10 rounded" />
  </div>
);

// Stats card skeleton
const StatsCardSkeleton: React.FC = () => (
  <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        <div className="h-4 bg-white/10 rounded w-24" />
        <div className="h-8 bg-white/10 rounded w-16" />
      </div>
      <div className="w-12 h-12 bg-white/10 rounded-full" />
    </div>
  </div>
);

// Leaderboard skeleton
const LeaderboardSkeleton: React.FC = () => (
  <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden animate-pulse">
    <div className="px-6 py-4 border-b border-gray-800">
      <div className="h-6 bg-white/10 rounded w-32" />
    </div>
    <div className="overflow-x-auto">
      <table className="min-w-full">
        <thead>
          <tr className="bg-gray-800/50">
            <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-12" /></th>
            <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-16" /></th>
            <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-16" /></th>
            <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-12" /></th>
          </tr>
        </thead>
        <tbody>
          {[...Array(5)].map((_, i) => (
            <tr key={i} className="border-t border-gray-800">
              <td className="px-6 py-4"><div className="h-5 w-5 bg-white/10 rounded" /></td>
              <td className="px-6 py-4"><div className="h-4 bg-white/10 rounded w-24" /></td>
              <td className="px-6 py-4"><div className="h-4 bg-white/10 rounded w-16" /></td>
              <td className="px-6 py-4"><div className="h-4 bg-white/10 rounded w-8" /></td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

// Main LoadingSkeleton component
const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  className, 
  variant = 'default', 
  count = 1 
}) => {
  const renderSkeleton = () => {
    switch (variant) {
      case 'card':
        return <VideoCardSkeleton />;
      
      case 'table':
        return (
          <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-800 animate-pulse">
              <div className="h-6 bg-white/10 rounded w-1/3" />
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-800/50">
                  <tr>
                    <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-4" /></th>
                    <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-16" /></th>
                    <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-20" /></th>
                    <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-16" /></th>
                    <th className="px-6 py-3"><div className="h-4 bg-white/10 rounded w-16" /></th>
                  </tr>
                </thead>
                <tbody>
                  {[...Array(count)].map((_, i) => (
                    <TableRowSkeleton key={i} />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );
      
      case 'list':
        return (
          <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
            <div className="h-6 bg-white/10 rounded w-1/3 mb-4 animate-pulse" />
            <div className="space-y-2">
              {[...Array(count)].map((_, i) => (
                <ListItemSkeleton key={i} />
              ))}
            </div>
          </div>
        );
      
      case 'stats':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(count)].map((_, i) => (
              <StatsCardSkeleton key={i} />
            ))}
          </div>
        );
      
      case 'video':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(count)].map((_, i) => (
              <VideoCardSkeleton key={i} />
            ))}
          </div>
        );
      
      case 'leaderboard':
        return <LeaderboardSkeleton />;
      
      default:
        return <SkeletonBase className={className} />;
    }
  };

  return (
    <div className={cn("", className)}>
      {renderSkeleton()}
    </div>
  );
};

export default LoadingSkeleton;

// Export individual skeleton components for direct use
export {
  VideoCardSkeleton,
  TableRowSkeleton,
  ListItemSkeleton,
  StatsCardSkeleton,
  LeaderboardSkeleton,
  SkeletonBase
};