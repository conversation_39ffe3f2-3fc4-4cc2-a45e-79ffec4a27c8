'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { 
  generateBlurDataURL, 
  getResponsiveSizes,
  type LazyImageProps,
  type ImageLoadingState 
} from '@/lib/image-utils';

interface OptimizedImageProps extends Omit<LazyImageProps, 'blurDataURL'> {
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
  lazy?: boolean;
  threshold?: number;
  rootMargin?: string;
  sizes?: string;
}

/**
 * Optimized image component with lazy loading, WebP support, and fallbacks
 */
export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 75,
  placeholder = 'blur',
  fallbackSrc,
  onLoad,
  onError,
  lazy = true,
  threshold = 0.1,
  rootMargin = '50px',
  sizes,
}: OptimizedImageProps) {
  const [loadingState, setLoadingState] = useState<ImageLoadingState>('loading');
  const [currentSrc, setCurrentSrc] = useState(src);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Generate blur placeholder
  const blurDataURL = generateBlurDataURL();

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        threshold,
        rootMargin,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    observerRef.current = observer;

    return () => {
      observer.disconnect();
    };
  }, [lazy, priority, isInView, threshold, rootMargin]);

  // Handle image load success
  const handleLoad = () => {
    setLoadingState('loaded');
    onLoad?.();
  };

  // Handle image load error with fallback
  const handleError = () => {
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setLoadingState('loading');
    } else {
      setLoadingState('error');
      onError?.();
    }
  };

  // Retry loading
  const retryLoad = () => {
    setLoadingState('loading');
    setCurrentSrc(src);
  };

  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      style={{ width, height }}
    >
      {/* Loading placeholder */}
      {loadingState === 'loading' && (
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-cyan-900/20 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full animate-spin" />
        </div>
      )}

      {/* Error state */}
      {loadingState === 'error' && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center text-gray-400">
          <div className="text-2xl mb-2">📷</div>
          <div className="text-xs text-center mb-2">Failed to load image</div>
          <button
            onClick={retryLoad}
            className="text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
          >
            Retry
          </button>
        </div>
      )}

      {/* Actual image */}
      {isInView && (
        <Image
          src={currentSrc}
          alt={alt}
          width={width}
          height={height}
          quality={quality}
          priority={priority}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          sizes={sizes || getResponsiveSizes()}
          className={cn(
            'object-cover transition-opacity duration-300',
            loadingState === 'loaded' ? 'opacity-100' : 'opacity-0'
          )}
          onLoad={handleLoad}
          onError={handleError}
        />
      )}

      {/* Lazy loading placeholder when not in view */}
      {!isInView && lazy && !priority && (
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-cyan-900/20 flex items-center justify-center">
          <div className="text-2xl opacity-50">🎬</div>
        </div>
      )}
    </div>
  );
}

/**
 * Specialized video thumbnail component
 */
interface VideoThumbnailProps {
  video: {
    id: string;
    title: string;
    thumbnailUrl?: string;
    platforms?: {
      youtube?: string;
      tiktok?: string;
      rumble?: string;
    };
  };
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  onClick?: () => void;
  showPlayOverlay?: boolean;
}

export function VideoThumbnail({
  video,
  width = 400,
  height = 225,
  className,
  priority = false,
  onClick,
  showPlayOverlay = true,
}: VideoThumbnailProps) {
  // Generate thumbnail from platforms if not provided
  const thumbnailSrc = video.thumbnailUrl || generateThumbnailFromPlatforms(video.platforms);
  
  return (
    <div 
      className={cn(
        'relative group cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <OptimizedImage
        src={thumbnailSrc || ''}
        alt={video.title}
        width={width}
        height={height}
        priority={priority}
        fallbackSrc={undefined} // Will show error state if no thumbnail
        className="rounded-lg"
      />
      
      {/* Play overlay */}
      {showPlayOverlay && (
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg">
          <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
            <div className="w-0 h-0 border-l-[8px] border-l-white border-y-[6px] border-y-transparent ml-1" />
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Generate thumbnail URL from platform URLs
 */
function generateThumbnailFromPlatforms(platforms?: {
  youtube?: string;
  tiktok?: string;
  rumble?: string;
}): string | null {
  if (!platforms) return null;

  // Try YouTube first (best thumbnail quality)
  if (platforms.youtube) {
    const videoId = extractYouTubeVideoId(platforms.youtube);
    if (videoId) {
      return `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
    }
  }

  // For other platforms, return null to show fallback
  return null;
}

/**
 * Extract YouTube video ID from URL
 */
function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}