'use client';

import React from 'react';
import Image from 'next/image';

interface HeroSectionProps {
  title: string;
  subtitle: string;
  description: string;
  userName?: string;
}

export default function HeroSection({ title, subtitle, description: _description, userName }: HeroSectionProps) {
  // Create static particles array to avoid hydration issues
  const particles = React.useMemo(() => {
    return Array.from({ length: 20 }, (_, i) => ({
      id: i,
      size: 2 + (i % 4), // Deterministic size based on index
      left: (i * 5) % 100, // Deterministic position based on index
      top: (i * 7) % 100, // Deterministic position based on index
      delay: (i * 0.1) % 2, // Deterministic delay based on index
      duration: 3 + (i % 3) // Deterministic duration based on index
    }));
  }, []);

  return (
    <div className="relative min-h-[60vh] flex items-center justify-center overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 gaming-bg">
        <Image 
          src="https://images.unsplash.com/photo-1643409471378-cdab0f97d983?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxhYnN0cmFjdCUyMGdhbWluZyUyMG5lb24lMjBkaWdpdGFsJTIwZnV0dXJpc3RpY3xlbnwwfDB8fHB1cnJsZXwxNzU0NDMzNDQzfDA&ixlib=rb-4.1.0&q=85"
          alt="Abstract gaming background with neon colors, futuristic digital pattern, cyberpunk aesthetic - MARIOLA GROBELSKA on Unsplash"
          fill
          sizes="100vw"
          className="object-cover opacity-30"
        />
      </div>
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-transparent to-cyan-900/80" />
      
      {/* Floating particles */}
      <div className="absolute inset-0">
        {particles.map(particle => (
          <div
            key={particle.id}
            className="absolute rounded-full bg-white/20"
            style={{
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              animation: 'float 3s ease-in-out infinite',
              animationDelay: `${particle.delay}s`,
              animationDuration: `${particle.duration}s`
            }}
          />
        ))}
      </div>
      
      {/* Content */}
      <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
        {userName && (
          <div className="mb-4">
            <span className="inline-block px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-cyan-300 font-medium">
              Welcome back, {userName}! 👋
            </span>
          </div>
        )}
        
        <div className="mb-6">
          <h1 className="text-5xl md:text-7xl font-bold font-display">
            <span className="cyber-text">{title}</span>
          </h1>
          <h2 className="text-xl md:text-2xl font-medium text-white mt-2">
            {subtitle}
          </h2>
        </div>
        
        <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
          NWA videos Portal.

          Thank you for sharing and liking our video content, your support helps spread the New World Alliances global movement. 
          We can help humanity and promote peace, love, and prosperity throughout the world.
        </p>
        
        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="https://www.newworldalliances.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-xl font-semibold text-white transition-all duration-300 hover:scale-105 neon-glow inline-block text-center"
            aria-label="Get Started - opens in a new window"
          >
            <span className="relative z-10 flex items-center justify-center space-x-2">
              <span>🚀</span>
              <span>Get Started</span>
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-purple-700 to-cyan-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </a>
          
          <a
            href="/leaderboard"
            className="group px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl font-semibold text-white transition-all duration-300 hover:bg-white/20 hover:scale-105 inline-block text-center"
            aria-label="Leaderboard"
          >
            <span className="flex items-center justify-center space-x-2">
              <span>🏆</span>
              <span>Leaderboard</span>
            </span>
          </a>
        </div>
        
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-bounce" />
          </div>
        </div>
      </div>
    </div>
  );
}