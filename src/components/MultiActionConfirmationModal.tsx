'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { X, Check, Heart, Share2, PlayCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EngagementActions {
  liked: boolean;
  shared: boolean;
  completed: boolean;
}

interface MultiActionConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (actions: EngagementActions | 'skip') => void;
  platform: string;
  videoTitle: string;
  videoUrl: string;
}

const PLATFORM_CONFIG = {
  youtube: {
    name: 'YouTube',
    icon: '📺',
    color: 'from-red-500 to-red-600'
  },
  tiktok: {
    name: 'TikTok',
    icon: '🎵',
    color: 'from-pink-500 to-purple-600'
  },
  rumble: {
    name: 'Rumble',
    icon: '⚡',
    color: 'from-green-500 to-green-600'
  }
};

export default function MultiActionConfirmationModal({
  isOpen,
  onClose: _onClose,
  onConfirm,
  platform,
  videoTitle,
  videoUrl: _videoUrl
}: MultiActionConfirmationModalProps) {
  const [mounted, setMounted] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30); // 30 seconds for multi-action
  const [actions, setActions] = useState<EngagementActions>({
    liked: false,
    shared: false,
    completed: false
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleConfirm = useCallback(() => {
    onConfirm(actions);
  }, [onConfirm, actions]);

  const toggleAction = (action: keyof EngagementActions) => {
    setActions(prev => ({
      ...prev,
      [action]: !prev[action]
    }));
  };

  useEffect(() => {
    if (!isOpen) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          // Auto-skip when time runs out
          onConfirm('skip');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Reset timer and actions when modal opens
    setTimeLeft(30);
    setActions({ liked: false, shared: false, completed: false });

    return () => clearInterval(timer);
  }, [isOpen, onConfirm]);

  // Handle keyboard events
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          onConfirm('skip');
          break;
        case '1':
        case 'l':
        case 'L':
          toggleAction('liked');
          break;
        case '2':
        case 's':
        case 'S':
          toggleAction('shared');
          break;
        case '3':
        case 'c':
        case 'C':
          toggleAction('completed');
          break;
        case 'Enter':
          handleConfirm();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, actions, handleConfirm, onConfirm]);

  const calculateBonusPoints = () => {
    let points = 0;
    if (actions.liked) points += 2;
    if (actions.shared) points += 3;
    if (actions.completed) points += 1;
    return points;
  };

  const hasAnyAction = actions.liked || actions.shared || actions.completed;

  if (!mounted || !isOpen) return null;

  const platformConfig = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG] || {
    name: platform,
    icon: '📺',
    color: 'from-blue-500 to-blue-600'
  };

  const bonusPoints = calculateBonusPoints();

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
         role="dialog"
         aria-modal="true"
         aria-labelledby="confirmation-title"
         aria-describedby="confirmation-description">
      <div className="relative w-full max-w-lg">
        {/* Background glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-cyan-600/20 to-purple-600/20 rounded-2xl blur-xl" />
        
        {/* Main modal */}
        <div className="relative bg-gray-900/95 backdrop-blur-xl border-2 border-purple-500/30 rounded-2xl p-6 shadow-2xl">
          {/* Close button */}
          <button
            onClick={() => onConfirm('skip')}
            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white transition-colors"
            aria-label="Close confirmation"
          >
            <X className="h-4 w-4" />
          </button>

          {/* Timer */}
          <div className="absolute top-4 left-4 flex items-center space-x-1 text-xs text-gray-400">
            <Clock className="h-3 w-3" />
            <span>{timeLeft}s</span>
          </div>

          {/* Content */}
          <div className="text-center space-y-6 mt-8">
            {/* Platform icon */}
            <div className={cn(
              "inline-flex items-center justify-center w-16 h-16 rounded-full text-2xl",
              `bg-gradient-to-r ${platformConfig.color}`
            )}>
              {platformConfig.icon}
            </div>

            {/* Title */}
            <div>
              <h2 id="confirmation-title" className="text-xl font-bold text-white mb-2">
                What did you do on {platformConfig.name}?
              </h2>
              <p id="confirmation-description" className="text-gray-300 text-sm leading-relaxed">
                We opened the video for you. Select what you actually did:
              </p>
              <p className="text-gray-400 text-xs mt-1">
                {videoTitle.length > 60 ? `${videoTitle.slice(0, 60)}...` : videoTitle}
              </p>
            </div>

            {/* Honest question message */}
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
              <p className="text-blue-200 text-sm">
                🤝 Be honest - this helps us track community engagement accurately!
              </p>
            </div>

            {/* Action Selection */}
            <div className="space-y-3">
              {/* Like Action */}
              <button
                onClick={() => toggleAction('liked')}
                className={cn(
                  "w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 border-2",
                  actions.liked 
                    ? "bg-gradient-to-r from-pink-500/20 to-red-500/20 border-pink-500/50 text-white" 
                    : "bg-gray-800/50 border-gray-600/50 text-gray-300 hover:border-gray-500/50"
                )}
              >
                <div className="flex items-center space-x-3">
                  <Heart className={cn("h-5 w-5", actions.liked ? "text-pink-400 fill-current" : "text-gray-400")} />
                  <span className="font-medium">I liked this video</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-400">+2 pts</span>
                  <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">1</kbd>
                </div>
              </button>

              {/* Share Action */}
              <button
                onClick={() => toggleAction('shared')}
                className={cn(
                  "w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 border-2",
                  actions.shared 
                    ? "bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/50 text-white" 
                    : "bg-gray-800/50 border-gray-600/50 text-gray-300 hover:border-gray-500/50"
                )}
              >
                <div className="flex items-center space-x-3">
                  <Share2 className={cn("h-5 w-5", actions.shared ? "text-green-400" : "text-gray-400")} />
                  <span className="font-medium">I shared this video</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-400">+3 pts</span>
                  <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">2</kbd>
                </div>
              </button>

              {/* Complete Action */}
              <button
                onClick={() => toggleAction('completed')}
                className={cn(
                  "w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 border-2",
                  actions.completed 
                    ? "bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-500/50 text-white" 
                    : "bg-gray-800/50 border-gray-600/50 text-gray-300 hover:border-gray-500/50"
                )}
              >
                <div className="flex items-center space-x-3">
                  <PlayCircle className={cn("h-5 w-5", actions.completed ? "text-blue-400 fill-current" : "text-gray-400")} />
                  <span className="font-medium">I watched the full video</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-400">+1 pt</span>
                  <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">3</kbd>
                </div>
              </button>
            </div>

            {/* Action buttons */}
            <div className="space-y-3 pt-2">
              {/* Confirm button */}
              <button
                onClick={handleConfirm}
                disabled={!hasAnyAction}
                className={cn(
                  "w-full flex items-center justify-center space-x-3 py-3 px-4 rounded-xl font-medium transition-all duration-200",
                  hasAnyAction
                    ? "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white hover:scale-105 hover:shadow-lg active:scale-95"
                    : "bg-gray-700 text-gray-500 cursor-not-allowed"
                )}
              >
                <Check className="h-5 w-5" />
                <span>
                  {hasAnyAction 
                    ? `Confirm Actions (+${bonusPoints} bonus points!)` 
                    : "Select what you did first"
                  }
                </span>
              </button>

              {/* Nothing/Skip buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={() => onConfirm({ liked: false, shared: false, completed: false })}
                  className="flex-1 py-2 px-4 rounded-xl text-gray-300 font-medium transition-all duration-200 bg-gray-800 hover:bg-gray-700 border border-gray-600"
                >
                  I didn&apos;t do anything
                </button>
                
                <button
                  onClick={() => onConfirm('skip')}
                  className="flex-1 py-2 px-4 rounded-xl text-gray-400 font-medium transition-all duration-200 bg-gray-800 hover:bg-gray-700 border border-gray-600"
                >
                  Skip
                </button>
              </div>
            </div>

            {/* Keyboard shortcuts help */}
            <div className="text-xs text-gray-500 space-y-1">
              <div>Keyboard shortcuts: <kbd className="px-1 py-0.5 bg-gray-700 rounded">1</kbd> Like, <kbd className="px-1 py-0.5 bg-gray-700 rounded">2</kbd> Share, <kbd className="px-1 py-0.5 bg-gray-700 rounded">3</kbd> Complete, <kbd className="px-1 py-0.5 bg-gray-700 rounded">Enter</kbd> Confirm</div>
            </div>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
}
