'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';

const ADMIN_FETCH_DISABLED = process.env.NEXT_PUBLIC_DISABLE_ADMIN_FETCH === 'true';
import Image from 'next/image';
import { Plus, Send, Check, AlertCircle, Video } from 'lucide-react';
import { validatePlatformUrls, isValidYouTubeUrl, isValidTikTokUrl, isValidRumbleUrl } from '@/lib/video-utils';
import LoadingSkeleton from './LoadingSkeleton';
import { useApiRetry } from '@/hooks/useRetry';
import {
   generateAriaLabel
} from '@/lib/accessibility';

interface PlatformUrls {
  youtubeUrl?: string;
  tiktokUrl?: string;
  rumbleUrl?: string;
}

interface AdminVideo {
  id: string;
  title: string;
  description?: string | null;
  platforms: {
    youtube?: string | null;
    tiktok?: string | null;
    rumble?: string | null;
  };
  availablePlatforms: string[];
  thumbnailUrl?: string | null;
  status: string;
  isFeatured: boolean;
  notificationSentAt?: Date | null;
  createdAt: Date;
  user: {
    id: string;
    name: string | null;
    role: string;
  };
  stats?: {
    totalLikes: number;
    totalShares: number;
    totalLinks: number;
  };
}

interface VideoFormData {
  title: string;
  description: string;
  platforms: PlatformUrls;
  thumbnailUrl: string;
}

interface ValidationErrors {
  title?: string;
  description?: string;
  platforms?: string;
  thumbnailUrl?: string;
}

interface AdminVideoManagerProps {
  className?: string;
}

const AdminVideoManager: React.FC<AdminVideoManagerProps> = ({ className = '' }) => {
  // State management
  const [videos, setVideos] = useState<AdminVideo[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());
  const [notificationStates, setNotificationStates] = useState<Record<string, 'idle' | 'sending' | 'sent' | 'error'>>({});
  
  // Form state
  const [formData, setFormData] = useState<VideoFormData>({
    title: '',
    description: '',
    platforms: {},
    thumbnailUrl: ''
  });
  const [formErrors, setFormErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Persistent log: print formErrors on every render
  console.log('formErrors on render:', formErrors);

  // Fetch videos with retry mechanism
  const fetchVideosApi = useCallback(async () => {
    const response = await fetch('/api/videos?type=nwa&limit=50', {
      credentials: 'include'
    });
    if (!response.ok) {
      // Extract server-provided error message if available
      let errorMessage = 'Failed to fetch videos';
      try {
        const errorData = await response.json();
        errorMessage = errorData?.error?.message || errorData?.message || errorMessage;
      } catch {
        // No-op: fall back to default message
      }
      throw new Error(errorMessage);
    }
    
    const data = await response.json();
    setVideos(data.videos || []);
    
    // Initialize notification states
    const states: Record<string, 'idle' | 'sending' | 'sent' | 'error'> = {};
    if (Array.isArray(data.videos)) {
      data.videos.forEach((video: AdminVideo) => {
        states[video.id] = video.notificationSentAt ? 'sent' : 'idle';
      });
    }
    setNotificationStates(states);
    
    return data;
  }, []);

  // Memoize retry logger and options to keep execute stable across renders
  const onRetryFetchVideos = useCallback((attempt: number, error: unknown) => {
    console.log(`Retrying video fetch (${attempt}/3):`, error instanceof Error ? error.message : String(error));
  }, []);
  const fetchRetryOptions = useMemo(() => ({
    maxRetries: 3,
    onRetry: onRetryFetchVideos,
  }), [onRetryFetchVideos]);

  const {
    execute: fetchVideos,
    isLoading: loading,
    error: fetchError
  } = useApiRetry(fetchVideosApi, fetchRetryOptions);

  // Ensure we fetch only once on mount (avoids repeated calls in dev/StrictMode)
  const hasFetchedRef = useRef(false);
  useEffect(() => {
    if (ADMIN_FETCH_DISABLED) return;
    if (!hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchVideos();
    }
  }, [fetchVideos]);

  // Manual fetch button when disabled by env
  const manualFetch = useCallback(() => {
    fetchVideos();
  }, [fetchVideos]);

  // Set error from fetch error
  useEffect(() => {
    if (fetchError) {
      setError(fetchError.message);
    }
  }, [fetchError]);

  // Form validation
  const validateForm = (data: VideoFormData): ValidationErrors => {
    const errors: ValidationErrors = {};
    
    if (!data.title.trim()) {
      errors.title = 'Title is required';
    } else if (data.title.length > 200) {
      errors.title = 'Title must be less than 200 characters';
    }
    
    if (data.description && data.description.length > 1000) {
      errors.description = 'Description must be less than 1000 characters';
    }
    
    // Validate platform URLs
    const { youtubeUrl, tiktokUrl, rumbleUrl } = data.platforms;
    const hasAnyUrl =
      (youtubeUrl && youtubeUrl.trim().length > 0) ||
      (tiktokUrl && tiktokUrl.trim().length > 0) ||
      (rumbleUrl && rumbleUrl.trim().length > 0);

    // Always run format validation if any field is non-empty
    const urlErrors = validatePlatformUrls(data.platforms);
    if (!hasAnyUrl) {
      errors.platforms = 'At least one platform URL is required';
    } else if (urlErrors.length > 0) {
      errors.platforms = urlErrors.join(', ');
    }
    
    if (data.thumbnailUrl && data.thumbnailUrl.trim()) {
      try {
        new URL(data.thumbnailUrl);
      } catch {
        errors.thumbnailUrl = 'Invalid thumbnail URL format';
      }
    }
    
    return errors;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // DEBUG: Log YouTube URL value for diagnosis
    console.log('FormData YouTube URL:', formData.platforms.youtubeUrl);

    const errors = validateForm(formData);
    setFormErrors(errors);

    // DEBUG: Log all validation errors for diagnosis
    console.log('Validation errors:', errors);
    // DEBUG: Print if POST will be attempted
    if (Object.keys(errors).length === 0) {
      console.log('Attempting POST to /api/videos');
    } else {
      console.log('POST blocked by validation errors');
    }

    // DEBUG: Log platform errors for diagnosis
    if (errors.platforms) {
      console.log('Form platform error:', errors.platforms);
    }

    // DEBUG: Log formErrors state after setFormErrors
    setTimeout(() => {
      console.log('FormErrors state after setFormErrors:', formErrors);
    }, 0);

    if (Object.keys(errors).length > 0) {
      // Do NOT reset form or close form if there are validation errors
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/videos', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || undefined,
          platforms: formData.platforms,
          thumbnailUrl: formData.thumbnailUrl || undefined,
          type: 'nwa'
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        // Extract the error message from the API error response format
        const errorMessage = errorData.error?.message || errorData.message || 'Failed to create video';
        throw new Error(errorMessage);
      }
      
      // Reset form and refresh videos
      // Only reset form after successful submission
      setFormData({
        title: '',
        description: '',
        platforms: {},
        thumbnailUrl: ''
      });
      setFormErrors({});
      setShowCreateForm(false);
      await fetchVideos();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create video');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle single video notification
  const handleSendNotification = async (videoId: string) => {
    setNotificationStates(prev => ({ ...prev, [videoId]: 'sending' }));
    
    try {
      const response = await fetch('/api/notifications/send-video', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoIds: [videoId],
          type: 'nwa'
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send notification');
      }
      
      setNotificationStates(prev => ({ ...prev, [videoId]: 'sent' }));
      
      // Update the video's notificationSentAt field
      setVideos(prev => prev.map(video => 
        video.id === videoId 
          ? { ...video, notificationSentAt: new Date() }
          : video
      ));
      
    } catch (err) {
      setNotificationStates(prev => ({ ...prev, [videoId]: 'error' }));
      setError(err instanceof Error ? err.message : 'Failed to send notification');
    }
  };

  // Handle bulk notifications
  const handleBulkNotification = async () => {
    if (selectedVideos.size === 0) return;
    
    const videoIds = Array.from(selectedVideos);
    
    // Set all selected videos to sending state
    const sendingStates: Record<string, 'sending'> = {};
    videoIds.forEach(id => {
      sendingStates[id] = 'sending';
    });
    setNotificationStates(prev => ({ ...prev, ...sendingStates }));
    
    try {
      const response = await fetch('/api/notifications/send-video', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoIds,
          type: 'nwa'
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send bulk notifications');
      }
      
      const result = await response.json();
      
      // Update states based on results
      const newStates: Record<string, 'sent' | 'error'> = {};
      result.results?.forEach((r: { videoId: string }) => {
        newStates[r.videoId] = 'sent';
      });
      result.skippedVideos?.forEach((v: { id: string }) => {
        newStates[v.id] = 'sent'; // Already sent
      });
      
      setNotificationStates(prev => ({ ...prev, ...newStates }));
      setSelectedVideos(new Set());
      
      // Refresh videos to get updated notificationSentAt
      await fetchVideos();
      
    } catch (err) {
      // Set all to error state
      const errorStates: Record<string, 'error'> = {};
      videoIds.forEach(id => {
        errorStates[id] = 'error';
      });
      setNotificationStates(prev => ({ ...prev, ...errorStates }));
      setError(err instanceof Error ? err.message : 'Failed to send bulk notifications');
    }
  };

  // Handle video selection
  const handleVideoSelect = (videoId: string, checked: boolean) => {
    setSelectedVideos(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(videoId);
      } else {
        newSet.delete(videoId);
      }
      return newSet;
    });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVideos(new Set(videos.map(v => v.id)));
    } else {
      setSelectedVideos(new Set());
    }
  };

  // Platform URL input component
  const PlatformUrlInput: React.FC<{
    platform: 'youtubeUrl' | 'tiktokUrl' | 'rumbleUrl';
    label: string;
    placeholder: string;
    value: string;
    onChange: (value: string) => void;
  }> = ({ platform, label, placeholder, value, onChange }) => {
    const [isValid, setIsValid] = useState<boolean | null>(null);
    
    const validateUrl = useCallback((url: string) => {
      if (!url.trim()) {
        setIsValid(null);
        return;
      }
      
      let valid = false;
      switch (platform) {
        case 'youtubeUrl':
          valid = isValidYouTubeUrl(url);
          break;
        case 'tiktokUrl':
          valid = isValidTikTokUrl(url);
          break;
        case 'rumbleUrl':
          valid = isValidRumbleUrl(url);
          break;
      }
      setIsValid(valid);
    }, [platform]);
    
    useEffect(() => {
      validateUrl(value);
    }, [value, validateUrl]);
    
    return (
      <div className="space-y-1">
        <label className="block text-sm font-medium text-gray-300">
          {label}
        </label>
        <div className="relative">
          <input
            type="url"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent pr-10"
          />
          {value.trim() && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              {isValid === true && (
                <Check className="h-4 w-4 text-green-400" />
              )}
              {isValid === false && (
                <AlertCircle className="h-4 w-4 text-red-400" />
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  if (ADMIN_FETCH_DISABLED) {
    return (
      <section
        className={`bg-gray-900 rounded-lg border border-gray-800 overflow-hidden ${className}`}
        aria-labelledby="admin-video-management-title"
      >
        <header className="px-6 py-4 border-b border-gray-800 flex justify-between items-center">
          <h2
            id="admin-video-management-title"
            className="text-xl font-bold text-white"
          >
            Admin Video Management
          </h2>
          <button
            onClick={manualFetch}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm font-medium transition-colors"
          >
            Load Admin Videos
          </button>
        </header>
        {error && (
          <div
            className="px-6 py-3 bg-red-900/20 border-b border-red-800/30 flex items-center space-x-2"
            role="alert"
            aria-live="assertive"
          >
            <AlertCircle className="h-4 w-4 text-red-400" aria-hidden="true" />
            <span className="text-red-300 text-sm">{error}</span>
          </div>
        )}
        <div className="p-6 text-gray-400">
          Admin auto-fetch disabled (NEXT_PUBLIC_DISABLE_ADMIN_FETCH=true). Click &quot;Load Admin Videos&quot; to fetch.
        </div>
      </section>
    );
  }

  if (loading) {
    return (
      <div className={className}>
        <LoadingSkeleton variant="table" count={5} />
      </div>
    );
  }

  

  return (
    <section 
      className={`bg-gray-900 rounded-lg border border-gray-800 overflow-hidden ${className}`}
      aria-labelledby="admin-video-management-title"
    >
      {/* Header */}
      <header className="px-6 py-4 border-b border-gray-800 flex justify-between items-center">
        <h2 
          id="admin-video-management-title"
          className="text-xl font-bold text-white"
        >
          Admin Video Management
        </h2>
        <div className="flex items-center space-x-3">
          {selectedVideos.size > 0 && (
            <button
              onClick={handleBulkNotification}
              aria-label={generateAriaLabel.button(
                'Send bulk notifications',
                `Send notifications for ${selectedVideos.size} selected videos`
              )}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors flex items-center space-x-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
            >
              <Send className="h-4 w-4" aria-hidden="true" />
              <span>Send Notifications ({selectedVideos.size})</span>
            </button>
          )}
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            aria-label={generateAriaLabel.button(
              showCreateForm ? 'Close video creation form' : 'Open video creation form'
            )}
            aria-expanded={showCreateForm}
            aria-controls="video-creation-form"
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm font-medium transition-colors flex items-center space-x-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
          >
            <Plus className="h-4 w-4" aria-hidden="true" />
            <span>Add Video</span>
          </button>
        </div>
      </header>

      {/* Error Display */}
      {error && (
        <div 
          className="px-6 py-3 bg-red-900/20 border-b border-red-800/30 flex items-center space-x-2"
          role="alert"
          aria-live="assertive"
        >
          <AlertCircle className="h-4 w-4 text-red-400" aria-hidden="true" />
          <span className="text-red-300 text-sm">{error}</span>
          <button
            onClick={() => setError(null)}
            aria-label={generateAriaLabel.button('Dismiss error message')}
            className="ml-auto text-red-400 hover:text-red-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 rounded p-1"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
      )}

      {/* Create Form */}
      {showCreateForm && (
        <div className="px-6 py-6 border-b border-gray-800 bg-gray-800/50">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Title */}
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-300">
                  Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter video title"
                />
                {formErrors.title && (
                  <p className="text-red-400 text-xs">{formErrors.title}</p>
                )}
              </div>

              {/* Thumbnail URL */}
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-300">
                  Thumbnail URL
                </label>
                <input
                  type="url"
                  value={formData.thumbnailUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, thumbnailUrl: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="https://example.com/thumbnail.jpg"
                />
                {formErrors.thumbnailUrl && (
                  <p className="text-red-400 text-xs">{formErrors.thumbnailUrl}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-300">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Enter video description"
              />
              {formErrors.description && (
                <p className="text-red-400 text-xs">{formErrors.description}</p>
              )}
            </div>

            {/* Platform URLs */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <h3 className="text-sm font-medium text-gray-300">Platform URLs *</h3>
                <span className="text-xs text-gray-500">(At least one required)</span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <PlatformUrlInput
                  platform="youtubeUrl"
                  label="YouTube URL"
                  placeholder="https://youtube.com/watch?v=..."
                  value={formData.platforms.youtubeUrl || ''}
                  onChange={(value) => setFormData(prev => ({
                    ...prev,
                    platforms: { ...prev.platforms, youtubeUrl: value }
                  }))}
                />
                
                <PlatformUrlInput
                  platform="tiktokUrl"
                  label="TikTok URL"
                  placeholder="https://tiktok.com/@user/video/..."
                  value={formData.platforms.tiktokUrl || ''}
                  onChange={(value) => setFormData(prev => ({
                    ...prev,
                    platforms: { ...prev.platforms, tiktokUrl: value }
                  }))}
                />
                
                <PlatformUrlInput
                  platform="rumbleUrl"
                  label="Rumble URL"
                  placeholder="https://rumble.com/..."
                  value={formData.platforms.rumbleUrl || ''}
                  onChange={(value) => setFormData(prev => ({
                    ...prev,
                    platforms: { ...prev.platforms, rumbleUrl: value }
                  }))}
                />
              </div>
              
              {formErrors.platforms && (
                <p className="text-red-400 text-xs">{formErrors.platforms}</p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  setFormData({
                    title: '',
                    description: '',
                    platforms: {},
                    thumbnailUrl: ''
                  });
                  setFormErrors({});
                }}
                className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white rounded-md font-medium transition-colors flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4" />
                    <span>Create Video</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Videos List */}
      <div className="overflow-x-auto">
        <table className="min-w-full text-left divide-y divide-gray-800">
          <thead className="bg-gray-800/50">
            <tr>
              <th className="px-6 py-3">
                <input
                  type="checkbox"
                  checked={videos.length > 0 && selectedVideos.size === videos.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                />
              </th>
              <th className="px-6 py-3 text-xs font-bold text-gray-400">Video</th>
              <th className="px-6 py-3 text-xs font-bold text-gray-400">Platforms</th>
              <th className="px-6 py-3 text-xs font-bold text-gray-400">Status</th>
              <th className="px-6 py-3 text-xs font-bold text-gray-400">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-800">
            {videos.map((video) => (
              <tr key={video.id} className="hover:bg-gray-800/50 transition-colors">
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedVideos.has(video.id)}
                    onChange={(e) => handleVideoSelect(video.id, e.target.checked)}
                    className="rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                  />
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-3">
                    {video.thumbnailUrl && (
                      <Image
                        src={video.thumbnailUrl}
                        alt={video.title}
                        width={64}
                        height={36}
                        className="w-16 h-9 object-cover rounded"
                      />
                    )}
                    <div>
                      <div className="text-sm font-medium text-white">{video.title}</div>
                      {video.description && (
                        <div className="text-xs text-gray-400 truncate max-w-xs">
                          {video.description}
                        </div>
                      )}
                      <div className="text-xs text-gray-500">
                        {new Date(video.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex flex-wrap gap-1">
                    {video.availablePlatforms.map((platform) => (
                      <span
                        key={platform}
                        className={`px-2 py-1 text-xs rounded-full ${
                          platform === 'youtube' ? 'bg-red-900/20 text-red-300' :
                          platform === 'tiktok' ? 'bg-pink-900/20 text-pink-300' :
                          platform === 'rumble' ? 'bg-green-900/20 text-green-300' :
                          'bg-gray-700 text-gray-300'
                        }`}
                      >
                        {platform}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    {video.isFeatured && (
                      <span className="px-2 py-1 text-xs bg-yellow-900/20 text-yellow-300 rounded-full">
                        Featured
                      </span>
                    )}
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      video.status === 'published' ? 'bg-green-900/20 text-green-300' :
                      video.status === 'draft' ? 'bg-gray-700 text-gray-300' :
                      'bg-red-900/20 text-red-300'
                    }`}>
                      {video.status}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleSendNotification(video.id)}
                      disabled={notificationStates[video.id] === 'sent' || notificationStates[video.id] === 'sending'}
                      className={`px-3 py-1 text-xs rounded-md font-medium transition-colors flex items-center space-x-1 ${
                        notificationStates[video.id] === 'sent' 
                          ? 'bg-green-900/20 text-green-300 cursor-not-allowed'
                          : notificationStates[video.id] === 'sending'
                          ? 'bg-blue-900/20 text-blue-300 cursor-not-allowed'
                          : notificationStates[video.id] === 'error'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-blue-600 hover:bg-blue-700 text-white'
                      }`}
                    >
                      {notificationStates[video.id] === 'sending' ? (
                        <>
                          <div className="w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin" />
                          <span>Sending</span>
                        </>
                      ) : notificationStates[video.id] === 'sent' ? (
                        <>
                          <Check className="h-3 w-3" />
                          <span>Sent</span>
                        </>
                      ) : notificationStates[video.id] === 'error' ? (
                        <>
                          <AlertCircle className="h-3 w-3" />
                          <span>Retry</span>
                        </>
                      ) : (
                        <>
                          <Send className="h-3 w-3" />
                          <span>Notify</span>
                        </>
                      )}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {videos.length === 0 && (
          <div className="text-center py-12 text-gray-400">
            <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No videos found. Create your first video to get started.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default AdminVideoManager;