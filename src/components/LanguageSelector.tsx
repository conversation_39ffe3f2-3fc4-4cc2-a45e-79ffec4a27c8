'use client';

import { useState, useEffect } from 'react';
import { useTranslation, Locale } from '@/lib/i18n';

const LANGUAGE_NAMES: Record<Locale, string> = {
  en: 'English',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
  pt: 'Português',
  zh: '中文',
  ja: '日本語',
  ko: '한국어'
};

const LANGUAGE_FLAGS: Record<Locale, string> = {
  en: '🇺🇸',
  es: '🇪🇸',
  fr: '🇫🇷',
  de: '🇩🇪',
  pt: '🇵🇹',
  zh: '🇨🇳',
  ja: '🇯🇵',
  ko: '🇰🇷'
};

interface LanguageSelectorProps {
  className?: string;
  showFlags?: boolean;
  variant?: 'dropdown' | 'buttons';
}

export default function LanguageSelector({
  className = '',
  showFlags = true,
  variant = 'dropdown'
}: LanguageSelectorProps) {
  const { locale, changeLocale, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  const supportedLocales = i18n.getSupportedLocales();

  const handleLocaleChange = async (newLocale: Locale) => {
    if (newLocale === locale) return;

    setIsChanging(true);
    try {
      await changeLocale(newLocale);
      
      // Store preference
      if (typeof window !== 'undefined') {
        localStorage.setItem('locale', newLocale);
      }
      
      // Close dropdown
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to change locale:', error);
    } finally {
      setIsChanging(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.language-selector')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isOpen]);

  if (variant === 'buttons') {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {supportedLocales.map((loc) => (
          <button
            key={loc}
            onClick={() => handleLocaleChange(loc)}
            disabled={isChanging}
            className={`
              px-3 py-1 rounded-md text-sm font-medium transition-colors
              ${locale === loc
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }
              ${isChanging ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
          >
            {showFlags && LANGUAGE_FLAGS[loc]} {LANGUAGE_NAMES[loc]}
          </button>
        ))}
      </div>
    );
  }

  return (
    <div className={`language-selector relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isChanging}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-md border border-gray-300
          bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2
          focus:ring-blue-500 focus:border-transparent transition-colors
          ${isChanging ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span className="flex items-center space-x-2">
          {showFlags && <span>{LANGUAGE_FLAGS[locale]}</span>}
          <span className="text-sm font-medium">{LANGUAGE_NAMES[locale]}</span>
        </span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
          <div className="py-1">
            {supportedLocales.map((loc) => (
              <button
                key={loc}
                onClick={() => handleLocaleChange(loc)}
                disabled={isChanging}
                className={`
                  w-full text-left px-4 py-2 text-sm transition-colors
                  ${locale === loc
                    ? 'bg-blue-50 text-blue-700 font-medium'
                    : 'text-gray-700 hover:bg-gray-50'
                  }
                  ${isChanging ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
              >
                <span className="flex items-center space-x-3">
                  {showFlags && <span>{LANGUAGE_FLAGS[loc]}</span>}
                  <span>{LANGUAGE_NAMES[loc]}</span>
                  {locale === loc && (
                    <svg className="w-4 h-4 ml-auto text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </span>
              </button>
            ))}
          </div>
        </div>
      )}

      {isChanging && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-md">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}