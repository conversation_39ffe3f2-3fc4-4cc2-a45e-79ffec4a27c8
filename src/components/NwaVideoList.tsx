'use client';

import { useState, useCallback } from 'react';
import MultiPlatformVideoCard from './MultiPlatformVideoCard';
import VideoSearchAndFilter, { SearchFilters } from './VideoSearchAndFilter';
import { useVideoInteractions } from '@/hooks/useVideoInteractions';
import { transformVideoWithPlatforms } from '@/lib/video-utils';
import LoadingSkeleton from './LoadingSkeleton';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useIsMobile } from '@/hooks/useMediaQuery';
import PullToRefresh from './PullToRefresh';
import VirtualScrollList from './VirtualScrollList';

// Define the type for a single NWA Video based on the API response
export type NwaVideo = {
  id: string;
  title: string;
  description: string | null;
  url: string;
  thumbnailUrl: string | null;
  youtubeUrl?: string | null;
  tiktokUrl?: string | null;
  rumbleUrl?: string | null;
  createdAt: string;
  _count: {
    likes: number;
    shares: number;
  };
  likes: { id: string; }[];
  shares: { id: string; }[];
};

interface NwaVideoListProps {
  initialVideos: NwaVideo[];
}

export default function NwaVideoList({ initialVideos }: NwaVideoListProps) {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [isSearching, setIsSearching] = useState(false);
  const isMobile = useIsMobile();

  // Fetch more videos for infinite scroll
  const fetchMoreVideos = useCallback(async (offset: number, limit: number) => {
    const params = new URLSearchParams();
    params.append('offset', offset.toString());
    params.append('limit', limit.toString());
    
    // Apply current search filters
    if (searchFilters.query) params.append('query', searchFilters.query);
    if (searchFilters.platform) params.append('platform', searchFilters.platform);
    if (searchFilters.featured !== undefined && searchFilters.featured !== null) {
      params.append('featured', searchFilters.featured.toString());
    }

    const response = await fetch(`/api/nwa-videos?${params.toString()}`);
    if (!response.ok) {
      const errorData = await response.json();
      // Extract the error message from the API error response format
      const errorMessage = errorData.error?.message || errorData.message || 'Failed to fetch more videos';
      throw new Error(errorMessage);
    }

    const data = await response.json();
    return {
      data: data.videos || [],
      hasMore: data.hasMore || false,
      total: data.total || 0
    };
  }, [searchFilters]);

  // Initialize infinite scroll hook
  const {
    data: videos,
    loading,
    error,
    hasMore,
    refresh,
    isRefreshing,
    getSentinelProps
  } = useInfiniteScroll({
    initialData: initialVideos,
    fetchMore: fetchMoreVideos,
    limit: isMobile ? 10 : 20,
    enabled: !isSearching
  });

  // Handle search and filtering
  const handleSearch = useCallback(async (filters: SearchFilters) => {
    setSearchFilters(filters);
    setIsSearching(true);

    try {
      // If no search query or filters, refresh to show all videos
      if (!filters.query && !filters.platform && !filters.featured) {
        await refresh();
        return;
      }

      // For search, we'll fetch fresh results
      const params = new URLSearchParams();
      if (filters.query) params.append('query', filters.query);
      if (filters.platform) params.append('platform', filters.platform);
      if (filters.featured !== undefined && filters.featured !== null) {
        params.append('featured', filters.featured.toString());
      }
      params.append('limit', '50'); // Get more results for search

      const response = await fetch(`/api/nwa-videos?${params.toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        // Extract the error message from the API error response format
        const errorMessage = errorData.error?.message || errorData.message || 'Search failed';
        throw new Error(errorMessage);
      }

      // Note: For search results, we'll disable infinite scroll and show all results
      // This is handled by the infinite scroll hook's enabled parameter
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  }, [refresh]);

  // Handle video completion
  const handleCompleteVideo = useCallback(async (videoId: string) => {
    try {
      const response = await fetch(`/api/videos/${videoId}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Extract the error message from the API error response format
        const errorMessage = errorData.error?.message || errorData.message || 'Failed to complete video';
        throw new Error(errorMessage);
      }

      // Refresh the video list to get replacement videos
      await refresh();
    } catch (error) {
      console.error('Error completing video:', error);
      throw error;
    }
  }, [refresh]);

  // Get video IDs for the interactions hook
  const videoIds = videos.map(video => video.id);
  const { userInteractions, handlePlatformClick } = useVideoInteractions(videoIds);

  // Transform videos to the format expected by MultiPlatformVideoCard
  const transformedVideos = videos.map(video => {
    const transformed = transformVideoWithPlatforms({
      ...video,
      createdAt: new Date(video.createdAt),
      updatedAt: new Date(video.createdAt),
      status: 'published',
      isFeatured: false,
      duration: null,
      views: 0,
      notificationSentAt: null,
      userId: 'nwa-admin',
      url: video.url || '', // Required by VideoWithPlatforms
      platform: 'youtube', // Required by VideoWithPlatforms
      description: video.description || null // Required by VideoWithPlatforms
    });

    return {
      id: transformed.id,
      title: transformed.title,
      description: transformed.description ?? null,
      platforms: {
        youtube: transformed.platforms.youtube ?? undefined,
        tiktok: transformed.platforms.tiktok ?? undefined,
        rumble: transformed.platforms.rumble ?? undefined,
      },
      thumbnailUrl: transformed.thumbnailUrl ?? undefined,
      createdAt: video.createdAt,
    };
  });



  if (!videos || videos.length === 0) {
    return (
      <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
        <h2 className="text-xl font-bold text-white mb-2">NWA Videos</h2>
        <p className="text-gray-400">No official NWA videos have been posted yet.</p>
      </div>
    );
  }

  const renderVideoGrid = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
      {transformedVideos.map((video) => (
        <div key={video.id} className="relative">
          <MultiPlatformVideoCard
            video={video}
            onPlatformClick={(platform, action) => handlePlatformClick(video.id, platform, action)}
            userInteractions={userInteractions[video.id] || {}}
            searchQuery={searchFilters.query} // Pass search query for highlighting
            onCompleteVideo={handleCompleteVideo}
            showCompleteButton={true}
          />
        </div>
      ))}
      
      {/* Infinite scroll sentinel - only on mobile */}
      {isMobile && hasMore && !isSearching && (
        <div {...getSentinelProps()} className="col-span-full">
          {loading && (
            <div className="flex justify-center py-4">
              <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>
      )}
    </div>
  );

  const renderVirtualizedList = () => (
    <VirtualScrollList
      items={transformedVideos}
      itemHeight={380} // Increased height to accommodate complete button
      containerHeight={600} // Fixed height for virtual scrolling
      renderItem={(video) => (
        <div className="p-3">
          <MultiPlatformVideoCard
            video={video}
            onPlatformClick={(platform, action) => handlePlatformClick(video.id, platform, action)}
            userInteractions={userInteractions[video.id] || {}}
            searchQuery={searchFilters.query}
            onCompleteVideo={handleCompleteVideo}
            showCompleteButton={true}
          />
        </div>
      )}
      isLoading={loading}
      loadingComponent={
        <div className="flex justify-center py-4">
          <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
        </div>
      }
    />
  );

  const content = (
    <div className="bg-gray-900 rounded-lg border border-gray-800">
      <div className="px-3 sm:px-6 py-3 sm:py-4 border-b border-gray-800">
        <h2 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4">NWA Videos</h2>
        
        {/* Search and Filter Component */}
        <VideoSearchAndFilter
          onSearch={handleSearch}
          initialFilters={searchFilters}
          showTypeFilter={false} // Don't show type filter since this is NWA only
          showPlatformFilter={true}
        showFeaturedFilter={false}
          placeholder="Search NWA videos by title or description..."
          className="mb-3 sm:mb-4"
        />
      </div>
      
      <div className="p-3 sm:p-6">
        {/* Search Results Info */}
        {searchFilters.query && (
          <div className="mb-3 sm:mb-4 text-xs sm:text-sm text-gray-400">
            {isSearching ? (
              <span>Searching...</span>
            ) : (
              <span>
                Found {transformedVideos.length} result{transformedVideos.length !== 1 ? 's' : ''} 
                {searchFilters.query && ` for "${searchFilters.query}"`}
              </span>
            )}
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-6 sm:py-8">
            <p className="text-red-400 text-sm sm:text-base mb-2">{error}</p>
            <button
              onClick={refresh}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Loading State */}
        {isSearching ? (
          <LoadingSkeleton variant="video" count={6} />
        ) : transformedVideos.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-400 text-sm sm:text-base">
              {searchFilters.query || searchFilters.platform || searchFilters.featured !== null
                ? "No videos match your search criteria."
                : "No official NWA videos have been posted yet."
              }
            </p>
          </div>
        ) : (
          // Use virtual scrolling for large lists on mobile, regular grid otherwise
          transformedVideos.length > 50 && isMobile ? renderVirtualizedList() : renderVideoGrid()
        )}
      </div>
    </div>
  );

  // Wrap with pull-to-refresh on mobile
  return isMobile ? (
    <PullToRefresh onRefresh={refresh} disabled={isSearching || isRefreshing}>
      {content}
    </PullToRefresh>
  ) : (
    content
  );
}
