'use client';

import { useState, useCallback, useEffect } from 'react';
import { Download, Heart, Share2 } from 'lucide-react';
import LoadingSkeleton from './LoadingSkeleton';

// Define the type for a single NWA Ebook based on the Media model
export type NwaEbook = {
  id: string;
  title: string;
  description: string | null;
  fileUrl: string | null;
  published: boolean;
  createdAt: string;
  _count?: {
    likes: number;
    shares: number;
  };
  likes?: { id: string; }[];
  shares?: { id: string; }[];
};

interface NwaEbookListProps {
  initialEbooks?: NwaEbook[];
  userRole?: string;
}

export default function NwaEbookList({ initialEbooks = [], userRole }: NwaEbookListProps) {
  const [ebooks, setEbooks] = useState<NwaEbook[]>(initialEbooks);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch ebooks from API
  const fetchEbooks = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/media?type=ebook', {
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch ebooks');
      }
      
      const data = await response.json();
      setEbooks(data.media || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ebooks');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch ebooks on mount if no initial data
  useEffect(() => {
    if (initialEbooks.length === 0) {
      fetchEbooks();
    }
  }, [initialEbooks.length, fetchEbooks]);

  // Handle ebook interactions
  const handleDownload = useCallback((ebook: NwaEbook) => {
    if (ebook.fileUrl) {
      window.open(ebook.fileUrl, '_blank');
    }
  }, []);

  const handleLike = useCallback(async (ebookId: string) => {
    try {
      const response = await fetch(`/api/media/${ebookId}/like`, {
        method: 'POST',
        credentials: 'include'
      });
      
      if (response.ok) {
        // Refresh the list to get updated counts
        await fetchEbooks();
      }
    } catch (error) {
      console.error('Error liking ebook:', error);
    }
  }, [fetchEbooks]);

  const handleShare = useCallback(async (ebookId: string) => {
    try {
      const response = await fetch(`/api/media/${ebookId}/share`, {
        method: 'POST',
        credentials: 'include'
      });
      
      if (response.ok) {
        // Refresh the list to get updated counts
        await fetchEbooks();
      }
    } catch (error) {
      console.error('Error sharing ebook:', error);
    }
  }, [fetchEbooks]);

  // Filter ebooks based on user role
  const visibleEbooks = ebooks.filter(ebook => {
    // Admins can see all ebooks
    if (userRole === 'ADMIN' || userRole === 'NWA_TEAM') {
      return true;
    }
    // Regular users only see published ebooks
    return ebook.published;
  });

  if (loading) {
    return (
      <div className="bg-gray-900 rounded-lg border border-gray-800">
        <div className="px-6 py-4 border-b border-gray-800">
          <h2 className="text-xl font-bold text-white">NWA Ebooks</h2>
        </div>
        <div className="p-6">
          <LoadingSkeleton variant="list" count={3} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-900 rounded-lg border border-gray-800">
        <div className="px-6 py-4 border-b border-gray-800">
          <h2 className="text-xl font-bold text-white">NWA Ebooks</h2>
        </div>
        <div className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button
            onClick={fetchEbooks}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (visibleEbooks.length === 0) {
    return (
      <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
        <h2 className="text-xl font-bold text-white mb-2">NWA Ebooks</h2>
        <p className="text-gray-400">No ebooks have been published yet.</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-800">
      <div className="px-6 py-4 border-b border-gray-800">
        <h2 className="text-xl font-bold text-white">NWA Ebooks</h2>
        <p className="text-gray-400 text-sm mt-1">
          Download and share educational content from the New World Alliance
        </p>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {visibleEbooks.map((ebook) => (
            <div key={ebook.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
              {/* Ebook header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                    {ebook.title}
                  </h3>
                  {!ebook.published && (
                    <span className="inline-block px-2 py-1 text-xs bg-yellow-900 text-yellow-300 rounded-full mb-2">
                      Draft
                    </span>
                  )}
                  {ebook.description && (
                    <p className="text-gray-400 text-sm line-clamp-3 mb-4">
                      {ebook.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Interaction buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handleLike(ebook.id)}
                    className="flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <Heart className="h-4 w-4" />
                    <span className="text-sm">{ebook._count?.likes || 0}</span>
                  </button>
                  
                  <button
                    onClick={() => handleShare(ebook.id)}
                    className="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors"
                  >
                    <Share2 className="h-4 w-4" />
                    <span className="text-sm">{ebook._count?.shares || 0}</span>
                  </button>
                </div>

                <button
                  onClick={() => handleDownload(ebook)}
                  disabled={!ebook.fileUrl}
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  <span>Download</span>
                </button>
              </div>

              {/* Creation date */}
              <div className="mt-4 pt-4 border-t border-gray-700">
                <p className="text-gray-500 text-xs">
                  Published {new Date(ebook.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
