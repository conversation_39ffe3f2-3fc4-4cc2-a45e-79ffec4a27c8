'use client';

import { useState, useCallback } from 'react';
import Image from 'next/image';
import { ThumbsUp, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import VideoSearchAndFilter, { SearchFilters } from './VideoSearchAndFilter';
import { HighlightedText } from '@/lib/search-utils';
import LoadingSkeleton from './LoadingSkeleton';
import { cn } from '@/lib/utils';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useIsMobile } from '@/hooks/useMediaQuery';
import PullToRefresh from './PullToRefresh';

// Define the type for a single User Video Link based on the API response
export type UserVideoLink = {
  id: string;
  linkUrl: string;
  user: { id: string; name: string | null; };
  video: { 
    id: string; 
    title: string; 
    description: string | null; 
    thumbnailUrl: string | null; 
  };
  _count: {
    likes: number;
    shares: number;
  };
  likes: { id: string; }[];
  shares: { id: string; }[];
};

interface UserVideoLinkListProps {
  initialLinks: UserVideoLink[];
}

export default function UserVideoLinkList({ initialLinks }: UserVideoLinkListProps) {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [isSearching, setIsSearching] = useState(false);
  const isMobile = useIsMobile();

  // Fetch more video links for infinite scroll
  const fetchMoreLinks = useCallback(async (offset: number, limit: number) => {
    const params = new URLSearchParams();
    params.append('offset', offset.toString());
    params.append('limit', limit.toString());

    const response = await fetch(`/api/video-links?${params.toString()}`);
    if (!response.ok) throw new Error('Failed to fetch more video links');

    const data = await response.json();
    return {
      data: data.videoLinks || [],
      hasMore: data.hasMore || false,
      total: data.total || 0
    };
  }, []);

  // Initialize infinite scroll hook
  const {
    data: links,
    loading,
    error,
    hasMore,
    refresh,
    isRefreshing,
    getSentinelProps
  } = useInfiniteScroll({
    initialData: initialLinks,
    fetchMore: fetchMoreLinks,
    limit: isMobile ? 10 : 20,
    enabled: !isSearching
  });

  // For search, we'll filter the current data client-side
  const filteredLinks = searchFilters.query 
    ? links.filter(link => {
        const query = searchFilters.query!.toLowerCase().trim();
        return link.video.title.toLowerCase().includes(query) ||
               (link.video.description && link.video.description.toLowerCase().includes(query)) ||
               (link.user.name && link.user.name.toLowerCase().includes(query));
      })
    : links;

  // Handle search and filtering
  const handleSearch = useCallback(async (filters: SearchFilters) => {
    setSearchFilters(filters);
    setIsSearching(true);

    try {
      // For user video links, we do client-side filtering
      // The filteredLinks will be updated automatically via the useMemo above
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const handleLike = async (linkId: string) => {
    const link = links.find(l => l.id === linkId);
    if (!link) return;

    const hasLiked = link.likes.length > 0;
    const endpoint = `/api/video-links/${linkId}/like`;

    try {
      const response = await fetch(endpoint, { method: hasLiked ? 'DELETE' : 'POST' });
      if (!response.ok) throw new Error('Failed to update like status');
      
      // Refresh data to get updated counts
      await refresh();
    } catch (error) {
      console.error('Failed to update like status:', error);
    }
  };

  const handleShare = async (linkId: string) => {
    const link = links.find(l => l.id === linkId);
    if (!link) return;

    const hasShared = link.shares.length > 0;
    const endpoint = `/api/video-links/${linkId}/share`;

    try {
      const response = await fetch(endpoint, { method: hasShared ? 'DELETE' : 'POST' });
      if (!response.ok) throw new Error('Failed to update share status');
      
      // Refresh data to get updated counts
      await refresh();
    } catch (error) {
      console.error('Failed to update share status:', error);
    }
  };

  if (!links || links.length === 0) {
    return (
      <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
        <h2 className="text-xl font-bold text-white mb-2">User Shared Videos</h2>
        <p className="text-gray-400">No users have shared video links yet.</p>
      </div>
    );
  }

  const renderTable = () => (
    <div className="overflow-x-auto">
      <table className="min-w-full text-left divide-y divide-gray-800">
        <thead className="bg-gray-800/50">
          <tr>
            <th className="px-3 sm:px-6 py-2 sm:py-3 text-xs font-bold text-gray-400">Video</th>
            <th className="px-3 sm:px-6 py-2 sm:py-3 text-xs font-bold text-gray-400 hidden sm:table-cell">Shared By</th>
            <th className="px-3 sm:px-6 py-2 sm:py-3 text-xs font-bold text-gray-400 text-center hidden md:table-cell">Likes</th>
            <th className="px-3 sm:px-6 py-2 sm:py-3 text-xs font-bold text-gray-400 text-center hidden md:table-cell">Shares</th>
            <th className="px-3 sm:px-6 py-2 sm:py-3 text-xs font-bold text-gray-400">Actions</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-800">
          {filteredLinks.map((link, index) => {
            const hasLiked = link.likes.length > 0;
            const hasShared = link.shares.length > 0;

            return (
              <tr key={link.id} className={cn('hover:bg-gray-800/50 transition-colors', index % 2 !== 0 ? 'bg-gray-800/20' : '')}>
                <td className="px-3 sm:px-6 py-3 sm:py-4 max-w-sm">
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    <a href={link.linkUrl} target="_blank" rel="noopener noreferrer" className="shrink-0">
                      {link.video.thumbnailUrl && (
                        <Image
                          src={link.video.thumbnailUrl}
                          alt={link.video.title}
                          width={64}
                          height={36}
                          className="h-8 w-14 sm:h-9 sm:w-16 rounded-md object-cover hover:opacity-80 transition-opacity"
                        />
                      )}
                    </a>
                    <div className="min-w-0 flex-1">
                      <a href={link.linkUrl} target="_blank" rel="noopener noreferrer">
                        <span className="font-medium text-white hover:text-purple-400 transition-colors text-sm sm:text-base line-clamp-2">
                          {searchFilters.query ? (
                            <HighlightedText 
                              text={link.video.title} 
                              searchQuery={searchFilters.query}
                              className="bg-yellow-400/20 text-yellow-300 px-1 rounded"
                            />
                          ) : (
                            link.video.title
                          )}
                        </span>
                      </a>
                      {link.video.description && (
                        <p className="text-xs sm:text-sm text-gray-500 mt-1 line-clamp-1">
                          {searchFilters.query ? (
                            <HighlightedText 
                              text={link.video.description} 
                              searchQuery={searchFilters.query}
                              className="bg-yellow-400/20 text-yellow-300 px-1 rounded"
                            />
                          ) : (
                            link.video.description
                          )}
                        </p>
                      )}
                      {/* Show user and stats on mobile */}
                      <div className="sm:hidden text-xs text-gray-400 mt-1">
                        By {searchFilters.query && link.user.name ? (
                          <HighlightedText 
                            text={link.user.name} 
                            searchQuery={searchFilters.query}
                            className="bg-yellow-400/20 text-yellow-300 px-1 rounded"
                          />
                        ) : (
                          link.user.name || 'Anonymous'
                        )} • {link._count.likes} likes • {link._count.shares} shares
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-3 sm:px-6 py-3 sm:py-4 text-sm text-gray-300 hidden sm:table-cell">
                  {searchFilters.query && link.user.name ? (
                    <HighlightedText 
                      text={link.user.name} 
                      searchQuery={searchFilters.query}
                      className="bg-yellow-400/20 text-yellow-300 px-1 rounded"
                    />
                  ) : (
                    link.user.name || 'Anonymous'
                  )}
                </td>
                <td className="px-3 sm:px-6 py-3 sm:py-4 text-sm text-gray-300 text-center hidden md:table-cell">{link._count.likes}</td>
                <td className="px-3 sm:px-6 py-3 sm:py-4 text-sm text-gray-300 text-center hidden md:table-cell">{link._count.shares}</td>
                <td className="px-3 sm:px-6 py-3 sm:py-4">
                  <div className="flex items-center space-x-1 sm:space-x-2">
                    <Button 
                      size="sm" 
                      onClick={() => handleLike(link.id)} 
                      variant={hasLiked ? 'secondary' : 'outline'}
                      className="min-h-[36px] min-w-[36px] p-2"
                    >
                      <ThumbsUp className={cn('h-3 w-3 sm:h-4 sm:w-4', hasLiked ? 'text-blue-500' : '')} />
                      <span className="sr-only">Like</span>
                    </Button>
                    <Button 
                      size="sm" 
                      onClick={() => handleShare(link.id)} 
                      variant={hasShared ? 'secondary' : 'outline'}
                      className="min-h-[36px] min-w-[36px] p-2"
                    >
                      <Share2 className={cn('h-3 w-3 sm:h-4 sm:w-4', hasShared ? 'text-green-500' : '')} />
                      <span className="sr-only">Share</span>
                    </Button>
                  </div>
                </td>
              </tr>
            );
          })}
          
          {/* Infinite scroll sentinel - only on mobile */}
          {isMobile && hasMore && !isSearching && (
            <tr>
              <td colSpan={5}>
                <div {...getSentinelProps()} />
                {loading && (
                  <div className="flex justify-center py-4">
                    <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );

  const content = (
    <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
      <div className="px-3 sm:px-6 py-3 sm:py-4 border-b border-gray-800">
        <h2 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4">User Shared Videos</h2>
        
        {/* Search and Filter Component */}
        <VideoSearchAndFilter
          onSearch={handleSearch}
          initialFilters={searchFilters}
          showTypeFilter={false} // Don't show type filter since this is user videos only
          showPlatformFilter={false} // User video links don't have platform-specific URLs
          showFeaturedFilter={false} // User videos don't have featured status
          placeholder="Search user videos by title, description, or user name..."
          className="mb-3 sm:mb-4"
        />

        {/* Search Results Info */}
        {searchFilters.query && (
          <div className="mb-3 sm:mb-4 text-xs sm:text-sm text-gray-400">
            {isSearching ? (
              <span>Searching...</span>
            ) : (
              <span>
                Found {filteredLinks.length} result{filteredLinks.length !== 1 ? 's' : ''} 
                {searchFilters.query && ` for "${searchFilters.query}"`}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="p-4 sm:p-6 text-center">
          <p className="text-red-400 text-sm sm:text-base mb-2">{error}</p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Loading State */}
      {isSearching ? (
        <div className="p-3 sm:p-6">
          <LoadingSkeleton variant="list" count={5} />
        </div>
      ) : filteredLinks.length === 0 ? (
        <div className="p-4 sm:p-6 text-center">
          <p className="text-gray-400 text-sm sm:text-base">
            {searchFilters.query
              ? "No user videos match your search criteria."
              : "No users have shared video links yet."
            }
          </p>
        </div>
      ) : (
        renderTable()
      )}
    </div>
  );

  // Wrap with pull-to-refresh on mobile
  return isMobile ? (
    <PullToRefresh onRefresh={refresh} disabled={isSearching || isRefreshing}>
      {content}
    </PullToRefresh>
  ) : (
    content
  );
}
