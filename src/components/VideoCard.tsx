'use client';

import React, { useState } from 'react';
import GamingCard from './GamingCard';
import VideoHoverPreview from './VideoHoverPreview';
import VideoPreviewModal from './VideoPreviewModal';
import { VideoThumbnail } from './OptimizedImage';

interface Video {
  id: string;
  title: string;
  description: string | null;
  createdAt: string;
}

interface VideoCardProps {
  video: Video;
  onPromote: (videoId: string) => void;
}

export default function VideoCard({ video, onPromote }: VideoCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Convert VideoCard video to match VideoPreviewModal interface
  const previewVideo = {
    ...video,
    platforms: {}, // VideoCard doesn't have platform data
    thumbnailUrl: undefined, // VideoCard doesn't have thumbnail
  };

  return (
    <>
      <GamingCard className="group overflow-hidden">
        {/* Thumbnail with Hover Preview */}
        <VideoHoverPreview 
          video={previewVideo} 
          onPreviewClick={() => setIsModalOpen(true)}
          delay={300}
        >
          <VideoThumbnail
            video={previewVideo}
            width={400}
            height={225}
            className="w-full aspect-video mb-4"
            onClick={() => setIsModalOpen(true)}
            showPlayOverlay={true}
          />
        </VideoHoverPreview>
        
        {/* Content */}
        <div className="space-y-3">
          <h3 className="font-semibold text-white line-clamp-2 group-hover:text-cyan-300 transition-colors">
            {video.title}
          </h3>
          
          {video.description && (
            <p className="text-sm text-gray-400 line-clamp-2">
              {video.description}
            </p>
          )}
          
          <div className="flex items-center justify-between pt-2">
            <span className="text-xs text-gray-500">
              {new Date(video.createdAt).toLocaleDateString()}
            </span>
            
            <button
              onClick={() => onPromote(video.id)}
              className="group/btn relative px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg font-medium text-white text-sm transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>🚀</span>
                <span>Promote</span>
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-700 to-cyan-700 rounded-lg opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300" />
            </button>
          </div>
        </div>
      </GamingCard>

      {/* Video Preview Modal */}
      <VideoPreviewModal
        video={previewVideo}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}