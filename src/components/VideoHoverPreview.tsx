'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Calendar, User, ThumbsUp, Share2, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';
import { VideoThumbnail } from './OptimizedImage';

interface Video {
  id: string;
  title: string;
  description: string | null;
  platforms: {
    youtube?: string;
    tiktok?: string;
    rumble?: string;
  };
  thumbnailUrl?: string;
  createdAt: string;
  user?: {
    name: string | null;
  };
  _count?: {
    likes: number;
    shares: number;
  };
}

interface VideoHoverPreviewProps {
  video: Video;
  children: React.ReactNode;
  onPreviewClick?: () => void;
  delay?: number;
  className?: string;
}

// Platform configuration
const PLATFORMS = {
  youtube: {
    name: 'YouTube',
    icon: '▶️',
    color: 'text-red-400',
  },
  tiktok: {
    name: 'TikTok',
    icon: '🎵',
    color: 'text-gray-300',
  },
  rumble: {
    name: 'Rumble',
    icon: '🎬',
    color: 'text-green-400',
  },
};

export default function VideoHoverPreview({
  video,
  children,
  onPreviewClick,
  delay = 500,
  className
}: VideoHoverPreviewProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  // Get available platforms for this video
  const availablePlatforms = Object.entries(video.platforms)
    .filter(([_, url]) => url)
    .map(([platform, url]) => ({ platform, url: url! }));

  const handleMouseEnter = (_event: React.MouseEvent) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      const rect = containerRef.current?.getBoundingClientRect();
      if (rect) {
        // Calculate position to show preview
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const previewWidth = 320; // Approximate preview width
        const previewHeight = 400; // Approximate preview height

        let x = rect.right + 10; // Show to the right by default
        let y = rect.top;

        // If preview would go off the right edge, show to the left
        if (x + previewWidth > viewportWidth) {
          x = rect.left - previewWidth - 10;
        }

        // If preview would go off the bottom, adjust upward
        if (y + previewHeight > viewportHeight) {
          y = viewportHeight - previewHeight - 10;
        }

        // If preview would go off the top, adjust downward
        if (y < 10) {
          y = 10;
        }

        setPosition({ x, y });
        setIsVisible(true);
      }
    }, delay);
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const handlePreviewMouseEnter = () => {
    // Keep preview visible when hovering over it
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handlePreviewMouseLeave = () => {
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <div
        ref={containerRef}
        className={cn("relative", className)}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {children}
      </div>

      {/* Hover Preview */}
      {isVisible && (
        <div
          ref={previewRef}
          className="fixed z-50 w-80 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl overflow-hidden"
          style={{
            left: position.x,
            top: position.y,
          }}
          onMouseEnter={handlePreviewMouseEnter}
          onMouseLeave={handlePreviewMouseLeave}
        >
          {/* Thumbnail */}
          <div className="relative">
            <VideoThumbnail
              video={video}
              width={320}
              height={180}
              className="w-full aspect-video"
              showPlayOverlay={true}
            />

            {/* Click to expand hint */}
            {onPreviewClick && (
              <div className="absolute top-2 right-2 z-10">
                <button
                  onClick={onPreviewClick}
                  className="p-1.5 bg-black/60 backdrop-blur-sm rounded-full text-white hover:bg-black/80 transition-colors"
                  title="Click to expand"
                >
                  <ExternalLink className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-4 space-y-3">
            {/* Title */}
            <h3 className="font-semibold text-white line-clamp-2 text-sm">
              {video.title}
            </h3>

            {/* Description */}
            {video.description && (
              <p className="text-xs text-gray-400 line-clamp-3">
                {video.description}
              </p>
            )}

            {/* Metadata */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <Calendar className="h-3 w-3" />
                <span>{new Date(video.createdAt).toLocaleDateString()}</span>
              </div>
              
              {video.user?.name && (
                <div className="flex items-center space-x-2 text-xs text-gray-400">
                  <User className="h-3 w-3" />
                  <span>Shared by {video.user.name}</span>
                </div>
              )}
            </div>

            {/* Stats */}
            {video._count && (
              <div className="flex items-center space-x-4 text-xs text-gray-400">
                <div className="flex items-center space-x-1">
                  <ThumbsUp className="h-3 w-3" />
                  <span>{video._count.likes}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Share2 className="h-3 w-3" />
                  <span>{video._count.shares}</span>
                </div>
              </div>
            )}

            {/* Available Platforms */}
            {availablePlatforms.length > 0 && (
              <div>
                <div className="text-xs text-gray-400 mb-2">Available on:</div>
                <div className="flex items-center space-x-2">
                  {availablePlatforms.map(({ platform }) => {
                    const platformConfig = PLATFORMS[platform as keyof typeof PLATFORMS];
                    if (!platformConfig) return null;

                    return (
                      <div
                        key={platform}
                        className="flex items-center space-x-1 px-2 py-1 bg-gray-800 rounded text-xs"
                        title={platformConfig.name}
                      >
                        <span>{platformConfig.icon}</span>
                        <span className={platformConfig.color}>{platformConfig.name}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Click to expand button */}
            {onPreviewClick && (
              <button
                onClick={onPreviewClick}
                className="w-full mt-3 px-3 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white text-xs font-medium rounded-lg transition-all duration-200 hover:scale-105"
              >
                Click to expand preview
              </button>
            )}
          </div>
        </div>
      )}
    </>
  );
}