"use client";

import React from "react";

interface UserActionsProps {
  userId: string;
  userName?: string;
  userEmail: string;
}

export default function UserActions({ userId, userName, userEmail }: UserActionsProps) {
  return (
    <div className="flex gap-2">
      <button
        className="px-2 py-1 text-xs font-semibold rounded bg-blue-600 text-white hover:bg-blue-700 transition"
        onClick={() => window.location.href = `/admin/users/create?edit=${userId}`}
        aria-label={`Edit user ${userName || userEmail}`}
      >
        Edit
      </button>
      <button
        className="px-2 py-1 text-xs font-semibold rounded bg-red-600 text-white hover:bg-red-700 transition"
        onClick={async () => {
          if (confirm('Are you sure you want to delete this user?')) {
            await fetch(`/api/admin/users/${userId}`, { method: 'DELETE' });
            window.location.reload();
          }
        }}
        aria-label={`Delete user ${userName || userEmail}`}
      >
        Delete
      </button>
    </div>
  );
}