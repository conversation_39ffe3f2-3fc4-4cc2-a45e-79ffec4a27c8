'use client';

import { useState, useEffect } from 'react';
import { usePWA } from '@/hooks/usePWA';
import { 
  formAccessibility, 
  buttonAccessibility
} from '@/lib/accessibility';

interface NotificationPreferences {
  nwaVideos: boolean;
  userVideos: boolean;
  pushNotifications: boolean;
}

export default function NotificationSettings() {
  const {
    notificationPermission,
    requestNotificationPermission,
    subscribeToPushNotifications,
    unsubscribeFromPushNotifications,
    serviceWorkerRegistration,
  } = usePWA();

  const [preferences, setPreferences] = useState<NotificationPreferences>({
    nwaVideos: true,
    userVideos: true,
    pushNotifications: false,
  });
  const [loading, setLoading] = useState(false);
  const [pushSubscribed, setPushSubscribed] = useState(false);

  useEffect(() => {
    // Load current preferences
    loadPreferences();
    checkPushSubscription();
  }, [serviceWorkerRegistration]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadPreferences = async () => {
    try {
      const response = await fetch('/api/notifications/preferences');
      if (response.ok) {
        const data = await response.json();
        setPreferences({
          nwaVideos: data.nwaVideos,
          userVideos: data.userVideos,
          pushNotifications: data.pushNotifications || false,
        });
      }
    } catch (error) {
      console.error('Failed to load notification preferences:', error);
    }
  };

  const checkPushSubscription = async () => {
    if (serviceWorkerRegistration) {
      try {
        const subscription = await serviceWorkerRegistration.pushManager.getSubscription();
        setPushSubscribed(!!subscription);
        setPreferences(prev => ({
          ...prev,
          pushNotifications: !!subscription,
        }));
      } catch (error) {
        console.error('Failed to check push subscription:', error);
      }
    }
  };

  const savePreferences = async (newPreferences: NotificationPreferences) => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newPreferences),
      });

      if (!response.ok) {
        throw new Error('Failed to save preferences');
      }

      setPreferences(newPreferences);
    } catch (error) {
      console.error('Failed to save notification preferences:', error);
      // Revert the change
      setPreferences(preferences);
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = async (key: keyof NotificationPreferences, value: boolean) => {
    const newPreferences = { ...preferences, [key]: value };

    // Handle push notification subscription
    if (key === 'pushNotifications') {
      if (value) {
        // Enable push notifications
        try {
          if (notificationPermission !== 'granted') {
            const permission = await requestNotificationPermission();
            if (permission !== 'granted') {
              return; // User denied permission
            }
          }

          const subscription = await subscribeToPushNotifications();
          if (subscription) {
            setPushSubscribed(true);
            await savePreferences(newPreferences);
          }
        } catch (error) {
          console.error('Failed to enable push notifications:', error);
          return;
        }
      } else {
        // Disable push notifications
        try {
          const success = await unsubscribeFromPushNotifications();
          if (success) {
            setPushSubscribed(false);
            await savePreferences(newPreferences);
          }
        } catch (error) {
          console.error('Failed to disable push notifications:', error);
          return;
        }
      }
    } else {
      await savePreferences(newPreferences);
    }
  };

  const getNotificationStatus = () => {
    if (notificationPermission === 'denied') {
      return 'blocked';
    }
    if (notificationPermission === 'granted' && pushSubscribed) {
      return 'enabled';
    }
    return 'disabled';
  };

  const notificationStatus = getNotificationStatus();

  const fieldIds = {
    nwaVideos: formAccessibility.generateFieldIds('nwa-videos'),
    userVideos: formAccessibility.generateFieldIds('user-videos'),
    pushNotifications: formAccessibility.generateFieldIds('push-notifications')
  };

  return (
    <section className="space-y-6" aria-labelledby="notification-settings-title">
      <header>
        <h3 
          id="notification-settings-title"
          className="text-lg font-medium text-gray-900 dark:text-white mb-4"
        >
          Notification Settings
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Choose what types of notifications you want to receive.
        </p>
      </header>

      <div className="space-y-4" role="group" aria-labelledby="notification-settings-title">
        {/* NWA Video Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <label 
              id={fieldIds.nwaVideos.label}
              htmlFor={fieldIds.nwaVideos.field}
              className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
            >
              NWA Video Notifications
            </label>
            <p 
              id={fieldIds.nwaVideos.description}
              className="text-sm text-gray-500 dark:text-gray-400"
            >
              Get notified when new NWA videos are posted by admins
            </p>
          </div>
          <button
            id={fieldIds.nwaVideos.field}
            type="button"
            role="switch"
            disabled={loading}
            onClick={() => handlePreferenceChange('nwaVideos', !preferences.nwaVideos)}
            aria-checked={preferences.nwaVideos}
            aria-labelledby={fieldIds.nwaVideos.label}
            aria-describedby={fieldIds.nwaVideos.description}
            {...buttonAccessibility.getButtonAttributes({
              disabled: loading,
              loading: loading
            })}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 focus-visible:outline-offset-2 ${
              preferences.nwaVideos ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span className="sr-only">
              {preferences.nwaVideos ? 'Disable' : 'Enable'} NWA video notifications
            </span>
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                preferences.nwaVideos ? 'translate-x-5' : 'translate-x-0'
              }`}
              aria-hidden="true"
            />
          </button>
        </div>

        {/* User Video Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <label 
              id={fieldIds.userVideos.label}
              htmlFor={fieldIds.userVideos.field}
              className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
            >
              User Video Notifications
            </label>
            <p 
              id={fieldIds.userVideos.description}
              className="text-sm text-gray-500 dark:text-gray-400"
            >
              Get notified when other users share personal video links
            </p>
          </div>
          <button
            id={fieldIds.userVideos.field}
            type="button"
            role="switch"
            disabled={loading}
            onClick={() => handlePreferenceChange('userVideos', !preferences.userVideos)}
            aria-checked={preferences.userVideos}
            aria-labelledby={fieldIds.userVideos.label}
            aria-describedby={fieldIds.userVideos.description}
            {...buttonAccessibility.getButtonAttributes({
              disabled: loading,
              loading: loading
            })}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 focus-visible:outline-offset-2 ${
              preferences.userVideos ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span className="sr-only">
              {preferences.userVideos ? 'Disable' : 'Enable'} user video notifications
            </span>
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                preferences.userVideos ? 'translate-x-5' : 'translate-x-0'
              }`}
              aria-hidden="true"
            />
          </button>
        </div>

        {/* Push Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <label 
              id={fieldIds.pushNotifications.label}
              htmlFor={fieldIds.pushNotifications.field}
              className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
            >
              Push Notifications
            </label>
            <p 
              id={fieldIds.pushNotifications.description}
              className="text-sm text-gray-500 dark:text-gray-400"
            >
              Receive push notifications on this device even when the app is closed
            </p>
            {notificationStatus === 'blocked' && (
              <p 
                id={fieldIds.pushNotifications.error}
                className="text-sm text-red-600 dark:text-red-400 mt-1"
                role="alert"
              >
                Push notifications are blocked. Please enable them in your browser settings.
              </p>
            )}
          </div>
          <button
            id={fieldIds.pushNotifications.field}
            type="button"
            role="switch"
            disabled={loading || notificationStatus === 'blocked'}
            onClick={() => handlePreferenceChange('pushNotifications', !preferences.pushNotifications)}
            aria-checked={preferences.pushNotifications}
            aria-labelledby={fieldIds.pushNotifications.label}
            aria-describedby={`${fieldIds.pushNotifications.description}${notificationStatus === 'blocked' ? ` ${fieldIds.pushNotifications.error}` : ''}`}
            {...buttonAccessibility.getButtonAttributes({
              disabled: loading || notificationStatus === 'blocked',
              loading: loading
            })}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 focus-visible:outline-offset-2 ${
              preferences.pushNotifications ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
            } ${loading || notificationStatus === 'blocked' ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span className="sr-only">
              {preferences.pushNotifications ? 'Disable' : 'Enable'} push notifications
            </span>
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                preferences.pushNotifications ? 'translate-x-5' : 'translate-x-0'
              }`}
              aria-hidden="true"
            />
          </button>
        </div>
      </div>

      {/* Status indicator */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              notificationStatus === 'enabled'
                ? 'bg-green-500'
                : notificationStatus === 'blocked'
                ? 'bg-red-500'
                : 'bg-yellow-500'
            }`}
          />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Push notifications are{' '}
            <span className="font-medium">
              {notificationStatus === 'enabled'
                ? 'enabled'
                : notificationStatus === 'blocked'
                ? 'blocked'
                : 'disabled'}
            </span>
          </span>
        </div>
      </div>
    </section>
  );
}