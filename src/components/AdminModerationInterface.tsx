'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface ContentReport {
  id: string;
  reason: string;
  description?: string;
  status: string;
  createdAt: string;
  reviewedAt?: string;
  resolution?: string;
  reporter: {
    id: string;
    name?: string;
    email: string;
  };
  reviewer?: {
    id: string;
    name?: string;
  };
  video?: {
    id: string;
    title: string;
    description?: string;
    userId: string;
  };
  videoLink?: {
    id: string;
    linkUrl: string;
    platform: string;
    userId: string;
  };
}

interface ModerationStats {
  pendingReports: number;
  totalReports: number;
  resolvedReports: number;
  flaggedContent: number;
  resolutionRate: number;
}

interface ModerationQueueResponse {
  reports: ContentReport[];
  total: number;
  page: number;
  totalPages: number;
  stats: ModerationStats;
}

const MODERATION_ACTIONS = [
  { value: 'approve', label: 'Dismiss Report', color: 'bg-green-600 hover:bg-green-700' },
  { value: 'hide', label: 'Hide Content', color: 'bg-yellow-600 hover:bg-yellow-700' },
  { value: 'delete', label: 'Delete Content', color: 'bg-red-600 hover:bg-red-700' },
  { value: 'warn_user', label: 'Warn User', color: 'bg-orange-600 hover:bg-orange-700' },
];

const REASON_LABELS = {
  inappropriate: 'Inappropriate Content',
  spam: 'Spam',
  copyright: 'Copyright Violation',
  other: 'Other',
};

export default function AdminModerationInterface() {
  const [data, setData] = useState<ModerationQueueResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedReport, setSelectedReport] = useState<ContentReport | null>(null);
  const [moderationAction, setModerationAction] = useState('');
  const [moderationReason, setModerationReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchModerationQueue = async (page = 1) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/moderation?page=${page}&limit=20`);
      if (response.ok) {
        const data = await response.json();
        setData(data);
      }
    } catch (error) {
      console.error('Error fetching moderation queue:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModerationQueue(currentPage);
  }, [currentPage]);

  const handleModerate = async () => {
    if (!selectedReport || !moderationAction) return;

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/admin/moderation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: selectedReport.id,
          action: moderationAction,
          reason: moderationReason.trim() || undefined,
        }),
      });

      if (response.ok) {
        // Refresh the queue
        await fetchModerationQueue(currentPage);
        // Close modal
        setSelectedReport(null);
        setModerationAction('');
        setModerationReason('');
      } else {
        throw new Error('Failed to moderate report');
      }
    } catch (error) {
      console.error('Error moderating report:', error);
      alert('Failed to moderate report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewed':
        return 'bg-blue-100 text-blue-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && !data) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      {data?.stats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-yellow-600">{data.stats.pendingReports}</div>
            <div className="text-sm text-gray-600">Pending Reports</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-blue-600">{data.stats.totalReports}</div>
            <div className="text-sm text-gray-600">Total Reports</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-green-600">{data.stats.resolvedReports}</div>
            <div className="text-sm text-gray-600">Resolved</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-red-600">{data.stats.flaggedContent}</div>
            <div className="text-sm text-gray-600">Flagged Content</div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-2xl font-bold text-purple-600">{data.stats.resolutionRate.toFixed(1)}%</div>
            <div className="text-sm text-gray-600">Resolution Rate</div>
          </div>
        </div>
      )}

      {/* Reports Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900">Content Reports</h2>
        </div>

        {data?.reports && data.reports.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Content
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reason
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reporter
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data.reports.map((report) => (
                    <tr key={report.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {report.video?.title || 'Video Link'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {report.video ? 'NWA Video' : `${report.videoLink?.platform} Link`}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {REASON_LABELS[report.reason as keyof typeof REASON_LABELS] || report.reason}
                        </div>
                        {report.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {report.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {report.reporter.name || 'Anonymous'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {report.reporter.email}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                          {report.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(report.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {report.status === 'pending' && (
                          <Button
                            onClick={() => setSelectedReport(report)}
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                          >
                            Review
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {data.totalPages > 1 && (
              <div className="px-6 py-4 border-t flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing page {data.page} of {data.totalPages} ({data.total} total reports)
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    variant="outline"
                    size="sm"
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === data.totalPages}
                    variant="outline"
                    size="sm"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="px-6 py-12 text-center">
            <div className="text-gray-500">No reports to review</div>
          </div>
        )}
      </div>

      {/* Moderation Modal */}
      {selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Review Report</h2>
                <button
                  onClick={() => setSelectedReport(null)}
                  disabled={isSubmitting}
                  className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4 mb-6">
                <div>
                  <h3 className="font-medium text-gray-900">Reported Content</h3>
                  <p className="text-gray-600">
                    {selectedReport.video?.title || 'Video Link'} 
                    ({selectedReport.video ? 'NWA Video' : `${selectedReport.videoLink?.platform} Link`})
                  </p>
                  {selectedReport.video?.description && (
                    <p className="text-sm text-gray-500 mt-1">{selectedReport.video.description}</p>
                  )}
                  {selectedReport.videoLink?.linkUrl && (
                    <p className="text-sm text-gray-500 mt-1">
                      <a href={selectedReport.videoLink.linkUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        {selectedReport.videoLink.linkUrl}
                      </a>
                    </p>
                  )}
                </div>

                <div>
                  <h3 className="font-medium text-gray-900">Report Details</h3>
                  <p className="text-gray-600">
                    <span className="font-medium">Reason:</span> {REASON_LABELS[selectedReport.reason as keyof typeof REASON_LABELS] || selectedReport.reason}
                  </p>
                  {selectedReport.description && (
                    <p className="text-gray-600 mt-1">
                      <span className="font-medium">Description:</span> {selectedReport.description}
                    </p>
                  )}
                  <p className="text-gray-600 mt-1">
                    <span className="font-medium">Reported by:</span> {selectedReport.reporter.name || 'Anonymous'} ({selectedReport.reporter.email})
                  </p>
                  <p className="text-gray-600 mt-1">
                    <span className="font-medium">Date:</span> {formatDate(selectedReport.createdAt)}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Moderation Action
                  </label>
                  <div className="space-y-2">
                    {MODERATION_ACTIONS.map((action) => (
                      <label key={action.value} className="flex items-center">
                        <input
                          type="radio"
                          name="action"
                          value={action.value}
                          checked={moderationAction === action.value}
                          onChange={(e) => setModerationAction(e.target.value)}
                          className="mr-3"
                          disabled={isSubmitting}
                        />
                        <span className="text-gray-900">{action.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label htmlFor="moderationReason" className="block text-sm font-medium text-gray-700 mb-2">
                    Reason for Action (Optional)
                  </label>
                  <textarea
                    id="moderationReason"
                    value={moderationReason}
                    onChange={(e) => setModerationReason(e.target.value)}
                    placeholder="Explain your decision..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                    disabled={isSubmitting}
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setSelectedReport(null)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleModerate}
                    disabled={!moderationAction || isSubmitting}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {isSubmitting ? 'Processing...' : 'Submit Decision'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}