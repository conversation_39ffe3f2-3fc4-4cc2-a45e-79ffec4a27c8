'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { CheckCircle } from 'lucide-react';
import { Video } from '@/generated/prisma';

interface UserVideo extends Video {
  user: {
    name: string | null;
  };
}

interface VideoPlaylistProps {
  initialVideos: UserVideo[];
}

export default function VideoPlaylist({ initialVideos }: VideoPlaylistProps) {
  const [videos, setVideos] = useState(initialVideos);

  const handleComplete = async (videoId: string) => {
    try {
      const response = await fetch(`/api/videos/${videoId}/complete`, {
        method: 'POST',
      });

      if (response.ok) {
        setVideos((prevVideos) => prevVideos.filter((video) => video.id !== videoId));
      } else {
        console.error('Failed to mark video as complete');
      }
    } catch (error) {
      console.error('Error completing video:', error);
    }
  };

  if (videos.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-400">You&apos;ve completed all available videos. Great job!</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
      <ul className="divide-y divide-gray-800">
        {videos.map((video) => (
          <li key={video.id} className="p-4 flex items-center justify-between hover:bg-gray-800/50">
            <div className="flex items-center">
              {video.thumbnailUrl && <Image src={video.thumbnailUrl} alt={video.title} width={120} height={68} className="rounded-md object-cover mr-4" />}
              <div>
                <Link href={video.url} target="_blank" rel="noopener noreferrer" className="font-bold text-white text-lg hover:text-purple-400 transition-colors">
                  {video.title}
                </Link>
                <p className="text-sm text-gray-400">{video.platform}</p>
              </div>
            </div>
            <button
              onClick={() => handleComplete(video.id)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg font-semibold flex items-center hover:bg-green-700 transition-colors"
            >
              <CheckCircle className="mr-2 h-5 w-5"/>
              Complete
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}
