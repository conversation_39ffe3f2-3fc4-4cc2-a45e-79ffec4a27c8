'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface HealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  message?: string;
  details?: Record<string, unknown>;
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, HealthCheck>;
  metrics: {
    uptime: number;
    memoryUsage: {
      heapUsed: number;
      heapTotal: number;
      external: number;
    };
    cpuUsage: {
      user: number;
      system: number;
    };
  };
}

interface MetricData {
  name: string;
  value: number;
  timestamp: string;
  tags?: Record<string, string>;
  unit?: string;
}

export default function AdminMonitoringDashboard() {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [metrics, setMetrics] = useState<Record<string, MetricData[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('api_response_time');

  const fetchHealth = async () => {
    try {
      const response = await fetch('/api/admin/monitoring?action=health');
      if (!response.ok) throw new Error('Failed to fetch health data');
      
      const data = await response.json();
      setHealth(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch health');
    }
  };

  const fetchMetrics = async (metric: string) => {
    try {
      const since = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const response = await fetch(`/api/admin/monitoring?action=metrics&metric=${metric}&since=${since}`);
      if (!response.ok) throw new Error('Failed to fetch metrics');
      
      const data = await response.json();
      setMetrics(prev => ({ ...prev, [metric]: data.metrics }));
    } catch (err) {
      console.error('Failed to fetch metrics:', err);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchHealth(),
        fetchMetrics('api_response_time'),
        fetchMetrics('database_query_time'),
        fetchMetrics('cache_operation'),
        fetchMetrics('application_errors')
      ]);
      setLoading(false);
    };

    loadData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchHealth();
      fetchMetrics(selectedMetric);
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, selectedMetric]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const formatMemory = (bytes: number) => {
    return `${Math.round(bytes / 1024 / 1024)} MB`;
  };

  const calculateAverage = (data: MetricData[]) => {
    if (data.length === 0) return 0;
    const sum = data.reduce((acc, item) => acc + item.value, 0);
    return Math.round((sum / data.length) * 100) / 100;
  };

  const getRecentMetrics = (data: MetricData[], count: number = 10) => {
    return data.slice(-count);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">System Monitoring</h1>
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            Auto-refresh
          </label>
          <Button onClick={() => window.location.reload()} variant="outline">
            Refresh Now
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* System Overview */}
      {health && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-600">System Status</div>
                <div className={`inline-flex px-2 py-1 text-sm font-medium rounded-full ${getStatusColor(health.status)}`}>
                  {health.status.toUpperCase()}
                </div>
              </div>
              <div className={`w-4 h-4 rounded-full ${
                health.status === 'healthy' ? 'bg-green-500' :
                health.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
              }`}></div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm text-gray-600">Uptime</div>
            <div className="text-2xl font-bold text-gray-900">
              {formatUptime(health.metrics.uptime)}
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm text-gray-600">Memory Usage</div>
            <div className="text-2xl font-bold text-gray-900">
              {formatMemory(health.metrics.memoryUsage.heapUsed)}
            </div>
            <div className="text-xs text-gray-500">
              of {formatMemory(health.metrics.memoryUsage.heapTotal)}
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm text-gray-600">CPU Usage</div>
            <div className="text-2xl font-bold text-gray-900">
              {Math.round((health.metrics.cpuUsage.user + health.metrics.cpuUsage.system) / 1000)}%
            </div>
          </div>
        </div>
      )}

      {/* Health Checks */}
      {health && (
        <div className="bg-white rounded-lg border">
          <div className="p-4 border-b">
            <h3 className="text-lg font-medium text-gray-900">Health Checks</h3>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(health.checks).map(([name, check]) => (
                <div key={name} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium text-gray-900">{check.service}</div>
                    <div className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(check.status)}`}>
                      {check.status}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    Response: {check.responseTime}ms
                  </div>
                  {check.message && (
                    <div className="text-xs text-gray-500 mt-1">{check.message}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Metrics Dashboard */}
      <div className="bg-white rounded-lg border">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Performance Metrics</h3>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="api_response_time">API Response Time</option>
              <option value="database_query_time">Database Query Time</option>
              <option value="cache_operation">Cache Operations</option>
              <option value="application_errors">Application Errors</option>
            </select>
          </div>
        </div>
        <div className="p-4">
          {metrics[selectedMetric] && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {calculateAverage(metrics[selectedMetric])}
                  </div>
                  <div className="text-sm text-gray-600">Average (24h)</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {metrics[selectedMetric].length}
                  </div>
                  <div className="text-sm text-gray-600">Data Points</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {metrics[selectedMetric].length > 0 ? 
                      Math.max(...metrics[selectedMetric].map(m => m.value)) : 0}
                  </div>
                  <div className="text-sm text-gray-600">Peak Value</div>
                </div>
              </div>

              {/* Simple chart visualization */}
              <div className="mt-6">
                <div className="text-sm font-medium text-gray-700 mb-2">Recent Trends</div>
                <div className="flex items-end space-x-1 h-32">
                  {getRecentMetrics(metrics[selectedMetric], 20).map((metric, index) => {
                    const maxValue = Math.max(...getRecentMetrics(metrics[selectedMetric], 20).map(m => m.value));
                    const height = maxValue > 0 ? (metric.value / maxValue) * 100 : 0;
                    
                    return (
                      <div
                        key={index}
                        className="bg-blue-500 rounded-t flex-1 min-w-0"
                        style={{ height: `${height}%` }}
                        title={`${metric.value} at ${new Date(metric.timestamp).toLocaleTimeString()}`}
                      ></div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recent Alerts */}
      <div className="bg-white rounded-lg border">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium text-gray-900">Recent Alerts</h3>
        </div>
        <div className="p-4">
          <div className="text-center text-gray-500 py-8">
            No recent alerts
          </div>
        </div>
      </div>
    </div>
  );
}