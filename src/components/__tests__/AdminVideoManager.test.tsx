import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react';
import AdminVideoManager from '../AdminVideoManager';

// Mock the fetch API
global.fetch = jest.fn((url) => {
  if (url.toString().includes('/api/videos?type=nwa&limit=50')) {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({ videos: [] }),
    }) as Promise<Response>;
  }

  if (url.toString().includes('/api/videos')) {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({}),
    }) as Promise<Response>;
  }

  return Promise.resolve({ ok: false, json: () => Promise.resolve({}) }) as Promise<Response>;
});

describe('AdminVideoManager', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should submit form with valid data', async () => {
    await act(async () => {
      render(<AdminVideoManager />);
    });

    // Open the create form
    const addButton = await screen.findByText('Add Video');
    await act(async () => {
        fireEvent.click(addButton);
    });

    // Fill in the form
    const titleInput = screen.getByPlaceholderText('Enter video title');
    await act(async () => {
        fireEvent.change(titleInput, { target: { value: 'Test Video' } });
    });

    const youtubeInput = screen.getByPlaceholderText('https://youtube.com/watch?v=...');
    await act(async () => {
        fireEvent.change(youtubeInput, { target: { value: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' } });
    });

    // Submit the form
    const submitButton = screen.getByText('Create Video');
    await act(async () => {
        fireEvent.click(submitButton);
    });

    // Wait for the API calls
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(3);
    });

    // Verify the request payload of the POST request
    const fetchCalls = (global.fetch as jest.Mock).mock.calls;
    const postCall = fetchCalls.find(([url, options]) => url === '/api/videos' && options && options.method === 'POST');
    expect(postCall).not.toBeUndefined();
    if (postCall) {
        const requestBody = JSON.parse(postCall[1].body);
        expect(requestBody).toEqual({
            title: 'Test Video',
            description: undefined,
            platforms: {
                youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            },
            thumbnailUrl: undefined,
            type: 'nwa',
        });
    }
  });
});