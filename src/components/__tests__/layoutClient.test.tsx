// src/components/__tests__/layoutClient.test.tsx

import { render, screen } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import LayoutClient from '../layout-client';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
  useRouter: jest.fn(() => ({
    push: jest.fn(),
  })),
}));

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  SessionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useSession: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn(),
}));

// Mock components
jest.mock('@/components/notification-bell', () => {
  return function MockNotificationBell() {
    return <div data-testid="notification-bell">Notification Bell</div>;
  };
});

jest.mock('@/components/PWAInstallPrompt', () => {
  return function MockPWAInstallPrompt() {
    return <div>PWA Install Prompt</div>;
  };
});

jest.mock('@/contexts/ThemeContext', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
});

describe('LayoutClient Component', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
    },
    expires: '2025-12-31T23:59:59.999Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Unauthenticated User', () => {
    it('should display Sign In button when user is not authenticated', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={null}>
          <LayoutClient session={null}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should have a Sign In button
      expect(screen.getByText('Sign In')).toBeInTheDocument();
    });
  });

  describe('Authenticated User', () => {
    it('should display user profile and Sign Out button when authenticated', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={mockSession}>
          <LayoutClient session={mockSession}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should display user profile
      expect(screen.getByText('Test User')).toBeInTheDocument();
      
      // Should display Sign Out button
      expect(screen.getByText('Sign Out')).toBeInTheDocument();
    });

    it('should display Dashboard link in navigation when authenticated', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={mockSession}>
          <LayoutClient session={mockSession}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should display Dashboard link in navigation
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    it('should display authenticated user navigation elements', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={mockSession}>
          <LayoutClient session={mockSession}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should display authenticated user navigation elements
      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('Leaderboard')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('Sign Out')).toBeInTheDocument();
    });

    it('should display all authenticated UI elements correctly', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={mockSession}>
          <LayoutClient session={mockSession}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should display all authenticated UI elements
      expect(screen.getByText('NWA Media')).toBeInTheDocument();
      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('Leaderboard')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('Sign Out')).toBeInTheDocument();
      expect(screen.getByTestId('notification-bell')).toBeInTheDocument();
    });
  });

  describe('Navigation Elements', () => {
    it('should always display Home and Leaderboard links', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={null}>
          <LayoutClient session={null}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should always display Home link
      expect(screen.getByText('Home')).toBeInTheDocument();
      
      // Should always display Leaderboard link
      expect(screen.getByText('Leaderboard')).toBeInTheDocument();
    });

    it('should display Sign In button for unauthenticated users', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={null}>
          <LayoutClient session={null}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should display Sign In button
      expect(screen.getByText('Sign In')).toBeInTheDocument();
    });
  });

  describe('Notification Bell', () => {
    it('should always display notification bell', () => {
      const useSession = require('next-auth/react').useSession;
      useSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      const usePathname = require('next/navigation').usePathname;
      usePathname.mockReturnValue('/');

      render(
        <SessionProvider session={null}>
          <LayoutClient session={null}>
            <div>Test Content</div>
          </LayoutClient>
        </SessionProvider>
      );

      // Should always display notification bell
      expect(screen.getByTestId('notification-bell')).toBeInTheDocument();
    });
  });
});