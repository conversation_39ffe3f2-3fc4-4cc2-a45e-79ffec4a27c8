import React from 'react';
import { render, screen } from '@testing-library/react';
import AdminMonitoringDashboard from '@/components/AdminMonitoringDashboard';

// Mock the fetch API
global.fetch = jest.fn((url) => {
  if (url.toString().includes('action=health')) {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({
        status: 'healthy',
        checks: {
          database: { service: 'Database', status: 'healthy', responseTime: 50 },
          redis: { service: 'Redis', status: 'healthy', responseTime: 20 },
        },
        metrics: {
          uptime: 123456,
          memoryUsage: { heapUsed: 100000000, heapTotal: 200000000, external: 50000000 },
          cpuUsage: { user: 1000, system: 500 },
        },
      }),
    }) as Promise<Response>;
  }

  if (url.toString().includes('action=metrics')) {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({ metrics: [{ name: 'test', value: 123, timestamp: new Date().toISOString() }] }),
    }) as Promise<Response>;
  }

  return Promise.resolve({ ok: false, json: () => Promise.resolve({}) }) as Promise<Response>;
});

describe('AdminMonitoringDashboard', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should render without crashing and display the title', async () => {
    render(<AdminMonitoringDashboard />);
    expect(await screen.findByText('System Monitoring')).toBeInTheDocument();
  });

  it('should display the health check data', async () => {
    render(<AdminMonitoringDashboard />);
    expect(await screen.findByText('System Status')).toBeInTheDocument();
    expect(await screen.findByText('HEALTHY')).toBeInTheDocument();
    expect(await screen.findByText('Database')).toBeInTheDocument();
    expect(await screen.findByText('Redis')).toBeInTheDocument();
  });

  it('should display the performance metrics', async () => {
    render(<AdminMonitoringDashboard />);
    expect(await screen.findByText('Performance Metrics')).toBeInTheDocument();
    expect(await screen.findByText('API Response Time')).toBeInTheDocument();
  });
});
