import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import type { Prisma } from '@/generated/prisma/client';
import {
  validatePlatformUrls,
  transformVideoWithPlatforms,
  getPrimaryPlatformInfo,
  PlatformUrls
} from '@/lib/video-utils';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { commonSchemas } from '@/lib/api-validation';
import { createApiError } from '@/lib/api-errors';
import { awardPoints, SCORING_CONFIG } from '@/lib/scoring-utils';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';
import { MediaQueryOptimizer } from '@/lib/media-query-optimization';
import { monitoredQueries } from '@/lib/query-performance-monitor';
import { moderateContent, initializeContentFilters } from '@/lib/content-moderation';
import { AuditLogger } from '@/lib/audit-logger';

interface MediaQuery {
  page?: string;
  limit?: string;
  offset?: string;
  type?: string; // 'video', 'ebook', or 'all'
  published?: string; // 'true', 'false', or 'all'
  userId?: string;
  platform?: string;
  featured?: string;
  query?: string;
}

// GET /api/media - Unified endpoint to fetch media items (videos and ebooks)
export const GET = withApiMiddleware<unknown, MediaQuery>({
  ...middlewarePresets.readOnly(rateLimiters.read),
  queryValidation: {
    ...commonSchemas.pagination,
    ...commonSchemas.search,
    type: { type: 'string', required: false },
    published: { type: 'string', required: false },
    userId: { type: 'string', required: false },
  },
})(async (request) => {
  const { validatedQuery, user } = request;
  
  // Use validated and sanitized query parameters
  const typedValidatedQuery = validatedQuery as MediaQuery;
  
  // Use validated query parameters with defaults
  const page = typedValidatedQuery?.page ? parseInt(typedValidatedQuery.page) : 1;
  const limit = typedValidatedQuery?.limit ? Math.min(parseInt(typedValidatedQuery.limit), 50) : 10;
  const offset = typedValidatedQuery?.offset ? parseInt(typedValidatedQuery.offset) : (page - 1) * limit;
  
  // Filter parameters from validated query
  const mediaType = typedValidatedQuery?.type || 'all'; // 'video', 'ebook', or 'all'
  const publishedFilter = typedValidatedQuery?.published || 'published';
  const userId = typedValidatedQuery?.userId;
  const platform = typedValidatedQuery?.platform;
  const featured = typedValidatedQuery?.featured === 'true' ? true : typedValidatedQuery?.featured === 'false' ? false : undefined;
  const query = typedValidatedQuery?.query;

  // Use optimized media query with performance monitoring
  const { media: optimizedMedia, total } = await monitoredQueries.getVideos(
    async () => {
      if (query) {
        // Use search optimization for queries
        return await MediaQueryOptimizer.searchMedia(
          query.trim(),
          { 
            type: mediaType as 'video' | 'ebook' | 'all', 
            platform, 
            userId,
            published: publishedFilter,
            userRole: user?.role 
          },
          { take: limit, skip: offset }
        );
      } else {
        // Build where clause for regular filtering
        const whereClause: Prisma.MediaWhereInput = {};

        // Handle published filter based on user role
        if (publishedFilter === 'published') {
          whereClause.published = true;
        } else if (publishedFilter === 'unpublished') {
          // Only admins can see unpublished content
          if (!user || !['ADMIN', 'NWA_TEAM'].includes(user.role)) {
            whereClause.published = true; // Force published for non-admins
          } else {
            whereClause.published = false;
          }
        } else if (publishedFilter === 'all') {
          // Only admins can see all content regardless of published status
          if (!user || !['ADMIN', 'NWA_TEAM'].includes(user.role)) {
            whereClause.published = true; // Force published for non-admins
          }
          // For admins, don't add published filter to see all
        } else {
          // Default: only published content for non-admins
          if (!user || !['ADMIN', 'NWA_TEAM'].includes(user.role)) {
            whereClause.published = true;
          }
        }

        // Filter by media type
        if (mediaType === 'video') {
          whereClause.type = 'video';
        } else if (mediaType === 'ebook') {
          whereClause.type = 'ebook';
        }

        // Filter by specific user
        if (userId) {
          whereClause.userId = userId;
        }

        // For videos, support platform filtering
        if (mediaType === 'video' && platform) {
          switch (platform.toLowerCase()) {
            case 'youtube':
              whereClause.videoUrl = { contains: 'youtube.com' };
              break;
            case 'tiktok':
              whereClause.videoUrl = { contains: 'tiktok.com' };
              break;
            case 'rumble':
              whereClause.videoUrl = { contains: 'rumble.com' };
              break;
          }
        }

        // Handle scheduled release for ebooks
        if (mediaType === 'ebook' || mediaType === 'all') {
          // Only show ebooks that have been released (releaseTime is null or in the past)
          // unless user is admin
          if (!user || !['ADMIN', 'NWA_TEAM'].includes(user.role)) {
            whereClause.OR = [
              { releaseTime: null },
              { releaseTime: { lte: new Date() } }
            ];
          }
        }

        const media = await MediaQueryOptimizer.getMediaWithRelations(
          whereClause,
          {
            take: limit,
            skip: offset,
            orderBy: [{ createdAt: 'desc' }],
            userId: request.user?.id, // For user interactions
          }
        );

        const total = await prisma.media.count({ where: whereClause });
        return { media, total };
      }
    },
    { userId: request.user?.id, filters: { type: mediaType, published: publishedFilter, platform, featured, query } }
  );

  // Transform media items based on their type
  const transformedMedia = optimizedMedia.map(item => {
    const baseItem = {
      ...item,
      stats: {
        totalLikes: item._count.likes,
        totalShares: item._count.shares,
        totalCompletions: item._count.completions,
      },
      userInteractions: item.userInteractions,
    };

    if (item.type === 'video') {
      // For videos, transform with platform information
      return {
        ...transformVideoWithPlatforms({
          ...item,
          url: item.videoUrl || '',
          platform: item.videoUrl?.includes('youtube') ? 'youtube' :
                    item.videoUrl?.includes('tiktok') ? 'tiktok' :
                    item.videoUrl?.includes('rumble') ? 'rumble' : 'unknown',
          status: 'published', // Default status for media items
          isFeatured: false, // Default featured status
          views: 0 // Default views count
        }),
        ...baseItem
      };
    }

    // For ebooks, return as-is with media-specific fields
    return baseItem;
  });

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  return NextResponse.json({
    media: transformedMedia,
    total,
    hasMore: offset + limit < total,
    pagination: {
      page,
      limit,
      offset,
      total,
      totalPages,
      hasNextPage,
      hasPreviousPage,
    },
    filters: {
      type: mediaType,
      published: publishedFilter,
      userId,
      platform,
      featured,
      query,
    },
  });
});

// POST /api/media - Unified endpoint to create media items (videos or ebooks)
export const POST = withApiMiddleware({
  ...middlewarePresets.secure(rateLimiters.videoCreation),
  bodyValidation: {
    type: { type: 'string', required: true },
    title: { type: 'string', required: true },
    description: { type: 'string', required: false },
    // For videos
    platforms: { type: 'object', required: false },
    thumbnailUrl: { type: 'string', required: false },
    duration: { type: 'number', required: false },
    // For ebooks
    fileUrl: { type: 'string', required: false },
    releaseTime: { type: 'string', required: false },
    published: { type: 'boolean', required: false },
  },
})(async (request) => {
  const { user, validatedBody } = request;
  const {
    type,
    title,
    description,
    platforms,
    thumbnailUrl: _thumbnailUrl,
    duration: _duration,
    fileUrl,
    releaseTime,
    published = false
  } = validatedBody as {
    type: 'video' | 'ebook';
    title: string;
    description?: string;
    platforms?: PlatformUrls;
    thumbnailUrl?: string;
    duration?: number;
    fileUrl?: string;
    releaseTime?: string;
    published?: boolean;
  };

  // Check permissions for admin-only operations
  const isAdmin = user!.role && ['ADMIN', 'NWA_TEAM'].includes(user!.role);
  
  if (type === 'ebook' && !isAdmin) {
    throw createApiError.forbidden('Only admins can create ebooks');
  }

  // Validate required fields based on type
  if (type === 'video') {
    if (!platforms) {
      throw createApiError.validation('Platform URLs are required for videos');
    }
    
    // Additional platform URL validation
    const urlErrors = validatePlatformUrls(platforms);
    if (urlErrors.length > 0) {
      throw createApiError.validation('Invalid URL format', urlErrors);
    }
  } else if (type === 'ebook') {
    if (!fileUrl) {
      throw createApiError.validation('File URL is required for ebooks');
    }
    
    // Validate PDF file URL
    if (!fileUrl.toLowerCase().endsWith('.pdf')) {
      throw createApiError.validation('Ebook file must be a PDF');
    }
  }

  // Content moderation for user-submitted videos
  if (type === 'video' && !isAdmin) {
    await initializeContentFilters();

    const urls = [
      platforms?.youtubeUrl,
      platforms?.tiktokUrl,
      platforms?.rumbleUrl,
    ].filter(Boolean) as string[];

    const moderationResult = await moderateContent(title, description || null, urls);

    if (moderationResult.isBlocked) {
      throw createApiError.forbidden('Content blocked due to policy violations');
    }

    if (moderationResult.isFlagged) {
      console.log('Content flagged for review:', {
        title,
        userId: user!.id,
        score: moderationResult.score,
        matchedFilters: moderationResult.matchedFilters,
      });
    }
  }

  // Prepare data based on media type
  let createData: Prisma.MediaCreateInput;
  
  if (type === 'video') {
    const { platform: _primaryPlatform, url: primaryUrl } = getPrimaryPlatformInfo(platforms!);
    
    createData = {
      type: 'video',
      title,
      description,
      videoUrl: primaryUrl,
      published: true, // Videos are published by default
      user: { connect: { id: user!.id } },
    };
  } else {
    // ebook
    createData = {
      type: 'ebook',
      title,
      description,
      fileUrl,
      releaseTime: releaseTime ? new Date(releaseTime) : null,
      published,
      user: { connect: { id: user!.id } },
    };
  }

  const newMediaItem = await prisma.media.create({
    data: createData,
    include: {
      user: {
        select: { 
          id: true,
          name: true, 
          role: true 
        },
      },
    },
  });

  // Award points for media creation
  if (type === 'video' && !isAdmin) {
    try {
      await awardPoints(
        user!.id, 
        SCORING_CONFIG.CREATE_USER_VIDEO, 
        `Created personal video: "${title}"`
      );
    } catch (error) {
      console.error('Error awarding points for video creation:', error);
    }
  }

  // Log the media creation action
  if (isAdmin) {
    await AuditLogger.logAdminAction(
      user!.id,
      'MEDIA_CREATE',
      'media',
      newMediaItem.id,
      {
        type,
        title,
        description,
        ...(type === 'video' ? { platforms } : { fileUrl, releaseTime, published }),
      },
      {
        userAgent: request.headers.get('user-agent') || undefined,
      }
    );
  } else {
    await AuditLogger.logUserEngagement(
      user!.id,
      'MEDIA_CREATE',
      newMediaItem.id,
      undefined,
      {
        type,
        title,
      }
    );
  }

  // Invalidate relevant caches after media creation
  await safeInvalidateCache(
    () => CacheInvalidation.onVideoChange(newMediaItem.id, user!.id),
    'media creation'
  );

  // Transform response based on media type
  let response;
  if (type === 'video') {
    response = transformVideoWithPlatforms({
      ...newMediaItem,
      url: newMediaItem.videoUrl || '',
      platform: newMediaItem.videoUrl?.includes('youtube') ? 'youtube' :
                newMediaItem.videoUrl?.includes('tiktok') ? 'tiktok' :
                newMediaItem.videoUrl?.includes('rumble') ? 'rumble' : 'unknown',
      status: 'published', // Default status for new media items
      isFeatured: false, // Default featured status
      views: 0 // Default views count
    });
  } else {
    response = newMediaItem;
  }

  return NextResponse.json(response, { status: 201 });
});
