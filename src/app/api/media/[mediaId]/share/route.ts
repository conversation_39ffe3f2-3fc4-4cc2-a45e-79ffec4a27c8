import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { createApiError } from '@/lib/api-errors';
import { awardPoints, SCORING_CONFIG } from '@/lib/scoring-utils';
import { AuditLogger } from '@/lib/audit-logger';

interface RouteParams {
  mediaId: string;
}

// POST /api/media/[mediaId]/share - Share a media item and award leaderboard points
export const POST = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.general),
})(async (request) => {
  const { user } = request;
  const { mediaId } = request.params as RouteParams;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check if media exists and is published (or user is admin)
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    include: { user: { select: { role: true } } }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Check if media is published or user is admin
  if (!media.published && (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role))) {
    throw createApiError.notFound('Media not found');
  }

  // For ebooks, check if it's been released
  if (media.type === 'ebook' && media.releaseTime && media.releaseTime > new Date()) {
    if (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role)) {
      throw createApiError.notFound('Media not found');
    }
  }

  // Check if user has already shared this media
  const existingShare = await prisma.share.findUnique({
    where: {
      userId_mediaId_videoId: {
        userId: user!.id,
        mediaId,
        videoId: "", // For media-based shares, videoId is empty
      },
    },
  });

  if (existingShare) {
    throw createApiError.conflict('You have already shared this media');
  }

  // Create the share record
  await prisma.share.create({
    data: {
      userId: user!.id,
      mediaId,
    },
  });

  // Award points for sharing (as specified in the spec)
  let pointsAwarded = 0;
  try {
    if (media.type === 'video') {
      await awardPoints(
        user!.id,
        SCORING_CONFIG.SHARE_VIDEO,
        `Shared NWA video: "${media.title}"`
      );
      pointsAwarded = SCORING_CONFIG.SHARE_VIDEO;
    } else if (media.type === 'ebook') {
      // Award points for sharing ebooks (requirement from spec)
      const ebookSharePoints = SCORING_CONFIG.SHARE_VIDEO; // Using same points as videos
      await awardPoints(
        user!.id,
        ebookSharePoints,
        `Shared NWA ebook: "${media.title}"`
      );
      pointsAwarded = ebookSharePoints;
    }
  } catch (error) {
    console.error('Error awarding points for media share:', error);
    // Don't fail the share action if scoring fails
  }

  // Log the interaction
  await AuditLogger.logUserEngagement(
    user!.id,
    'MEDIA_SHARE',
    mediaId,
    undefined,
    {
      mediaType: media.type,
      mediaTitle: media.title,
      pointsAwarded,
    }
  );

  // Get updated share count
  const shareCount = await prisma.share.count({
    where: { mediaId },
  });

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} shared successfully`,
    pointsAwarded,
    shareCount,
  });
});

// DELETE /api/media/[mediaId]/share - Remove share (unshare) a media item
export const DELETE = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.general),
})(async (request) => {
  const { user } = request;
  const { mediaId } = request.params as RouteParams;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check if media exists
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    select: { id: true, type: true, title: true }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Find and delete the share
  const existingShare = await prisma.share.findUnique({
    where: {
      userId_mediaId_videoId: {
        userId: user!.id,
        mediaId,
        videoId: "",
      },
    },
  });

  if (!existingShare) {
    throw createApiError.notFound('Share not found');
  }

  await prisma.share.delete({
    where: { id: existingShare.id },
  });

  // Log the interaction
  await AuditLogger.logUserEngagement(
    user!.id,
    'MEDIA_UNSHARE',
    mediaId,
    undefined,
    {
      mediaType: media.type,
      mediaTitle: media.title,
    }
  );

  // Get updated share count
  const shareCount = await prisma.share.count({
    where: { mediaId },
  });

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} unshared successfully`,
    shareCount,
  });
});
