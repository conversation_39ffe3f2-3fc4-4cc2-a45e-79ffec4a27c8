import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { createApiError } from '@/lib/api-errors';
import { AuditLogger } from '@/lib/audit-logger';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';

interface RouteParams {
  mediaId: string;
}

interface ScheduleRequestBody {
  releaseTime: string | null; // ISO string or null to remove scheduling
}

// PATCH /api/media/[mediaId]/schedule - Update scheduled release time (admin only, ebooks only)
export const PATCH = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.strict),
  bodyValidation: {
    releaseTime: { type: 'string', required: false },
  },
})(async (request) => {
  const { user, validatedBody } = request;
  const { mediaId } = request.params as RouteParams;
  const { releaseTime } = validatedBody as unknown as ScheduleRequestBody;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check admin permissions
  if (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role)) {
    throw createApiError.forbidden('Only admins can schedule media releases');
  }

  // Check if media exists and is an ebook
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    select: { 
      id: true, 
      type: true, 
      title: true, 
      releaseTime: true,
      userId: true 
    }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Only ebooks can be scheduled
  if (media.type !== 'ebook') {
    throw createApiError.validation('Only ebooks can have scheduled release times');
  }

  // Validate release time format if provided
  let parsedReleaseTime: Date | null = null;
  if (releaseTime) {
    try {
      parsedReleaseTime = new Date(releaseTime);
      if (isNaN(parsedReleaseTime.getTime())) {
        throw new Error('Invalid date');
      }
      
      // Don't allow scheduling in the past
      if (parsedReleaseTime < new Date()) {
        throw createApiError.validation('Release time cannot be in the past');
      }
    } catch (_error) {
      throw createApiError.validation('Invalid release time format. Use ISO string format.');
    }
  }

  // Update the release time
  const updatedMedia = await prisma.media.update({
    where: { id: mediaId },
    data: { releaseTime: parsedReleaseTime },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          role: true,
        },
      },
    },
  });

  // Log the admin action
  await AuditLogger.logAdminAction(
    user!.id,
    'MEDIA_SCHEDULE',
    'media',
    mediaId,
    {
      mediaType: media.type,
      mediaTitle: media.title,
      previousReleaseTime: media.releaseTime?.toISOString() || null,
      newReleaseTime: parsedReleaseTime?.toISOString() || null,
    },
    {
      userAgent: request.headers.get('user-agent') || undefined,
    }
  );

  // Invalidate relevant caches
  await safeInvalidateCache(
    () => CacheInvalidation.onVideoChange(mediaId, media.userId),
    'media schedule change'
  );

  return NextResponse.json({
    success: true,
    message: `Ebook ${parsedReleaseTime ? 'scheduled for release' : 'release time removed'} successfully`,
    media: updatedMedia,
    releaseTime: parsedReleaseTime?.toISOString() || null,
  });
});
