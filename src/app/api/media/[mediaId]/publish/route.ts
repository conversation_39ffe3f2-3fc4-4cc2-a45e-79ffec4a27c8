import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { createApiError } from '@/lib/api-errors';
import { AuditLogger } from '@/lib/audit-logger';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';

interface RouteParams {
  mediaId: string;
}

interface PublishRequestBody {
  published: boolean;
}

// PATCH /api/media/[mediaId]/publish - Toggle publish/unpublish status (admin only)
export const PATCH = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.strict),
  bodyValidation: {
    published: { type: 'boolean', required: true },
  },
})(async (request) => {
  const { user, validatedBody } = request;
  const { mediaId } = request.params as RouteParams;
  const { published } = validatedBody as unknown as PublishRequestBody;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check admin permissions
  if (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role)) {
    throw createApiError.forbidden('Only admins can publish/unpublish media');
  }

  // Check if media exists
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    select: { 
      id: true, 
      type: true, 
      title: true, 
      published: true,
      userId: true 
    }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Update the published status
  const updatedMedia = await prisma.media.update({
    where: { id: mediaId },
    data: { published },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          role: true,
        },
      },
    },
  });

  // Log the admin action
  await AuditLogger.logAdminAction(
    user!.id,
    published ? 'MEDIA_PUBLISH' : 'MEDIA_UNPUBLISH',
    'media',
    mediaId,
    {
      mediaType: media.type,
      mediaTitle: media.title,
      previousStatus: media.published,
      newStatus: published,
    },
    {
      userAgent: request.headers.get('user-agent') || undefined,
    }
  );

  // Invalidate relevant caches
  await safeInvalidateCache(
    () => CacheInvalidation.onVideoChange(mediaId, media.userId),
    'media publish status change'
  );

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} ${published ? 'published' : 'unpublished'} successfully`,
    media: updatedMedia,
  });
});
