import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { createApiError } from '@/lib/api-errors';
import { AuditLogger } from '@/lib/audit-logger';

interface RouteParams {
  mediaId: string;
}

// POST /api/media/[mediaId]/like - Like a media item
export const POST = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.general),
})(async (request) => {
  const { user } = request;
  const { mediaId } = request.params as RouteParams;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check if media exists and is published (or user is admin)
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    include: { user: { select: { role: true } } }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Check if media is published or user is admin
  if (!media.published && (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role))) {
    throw createApiError.notFound('Media not found');
  }

  // For ebooks, check if it's been released
  if (media.type === 'ebook' && media.releaseTime && media.releaseTime > new Date()) {
    if (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role)) {
      throw createApiError.notFound('Media not found');
    }
  }

  // Check if user has already liked this media
  const existingLike = await prisma.like.findUnique({
    where: {
      userId_mediaId_videoId: {
        userId: user!.id,
        mediaId,
        videoId: "", // For media-based likes, videoId is empty
      },
    },
  });

  if (existingLike) {
    throw createApiError.conflict('You have already liked this media');
  }

  // Create the like
  await prisma.like.create({
    data: {
      userId: user!.id,
      mediaId,
    },
  });

  // Log the interaction
  await AuditLogger.logUserEngagement(
    user!.id,
    'MEDIA_LIKE',
    mediaId,
    undefined,
    {
      mediaType: media.type,
      mediaTitle: media.title,
    }
  );

  // Get updated like count
  const likeCount = await prisma.like.count({
    where: { mediaId },
  });

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} liked successfully`,
    likeCount,
  });
});

// DELETE /api/media/[mediaId]/like - Unlike a media item
export const DELETE = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.general),
})(async (request) => {
  const { user } = request;
  const { mediaId } = request.params as RouteParams;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check if media exists
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    select: { id: true, type: true, title: true }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Find and delete the like
  const existingLike = await prisma.like.findUnique({
    where: {
      userId_mediaId_videoId: {
        userId: user!.id,
        mediaId,
        videoId: "",
      },
    },
  });

  if (!existingLike) {
    throw createApiError.notFound('Like not found');
  }

  await prisma.like.delete({
    where: { id: existingLike.id },
  });

  // Log the interaction
  await AuditLogger.logUserEngagement(
    user!.id,
    'MEDIA_UNLIKE',
    mediaId,
    undefined,
    {
      mediaType: media.type,
      mediaTitle: media.title,
    }
  );

  // Get updated like count
  const likeCount = await prisma.like.count({
    where: { mediaId },
  });

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} unliked successfully`,
    likeCount,
  });
});
