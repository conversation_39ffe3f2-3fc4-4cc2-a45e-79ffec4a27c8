import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { createApiError } from '@/lib/api-errors';
import { AuditLogger } from '@/lib/audit-logger';

interface RouteParams {
  mediaId: string;
}

// POST /api/media/[mediaId]/complete - Mark a media item as completed
export const POST = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.general),
})(async (request) => {
  const { user } = request;
  const { mediaId } = request.params as RouteParams;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check if media exists and is published (or user is admin)
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    include: { user: { select: { role: true } } }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Check if media is published or user is admin
  if (!media.published && (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role))) {
    throw createApiError.notFound('Media not found');
  }

  // For ebooks, check if it's been released
  if (media.type === 'ebook' && media.releaseTime && media.releaseTime > new Date()) {
    if (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role)) {
      throw createApiError.notFound('Media not found');
    }
  }

  // Check if user has already completed this media
  const existingCompletion = await prisma.completedLink.findUnique({
    where: {
      userId_mediaId_linkId: {
        userId: user!.id,
        mediaId,
        linkId: "", // For media-based completions, linkId is empty
      },
    },
  });

  if (existingCompletion) {
    throw createApiError.conflict('You have already completed this media');
  }

  // Create the completion record
  await prisma.completedLink.create({
    data: {
      userId: user!.id,
      mediaId,
    },
  });

  // Log the interaction
  await AuditLogger.logUserEngagement(
    user!.id,
    'MEDIA_COMPLETE',
    mediaId,
    undefined,
    {
      mediaType: media.type,
      mediaTitle: media.title,
    }
  );

  // Get updated completion count
  const completionCount = await prisma.completedLink.count({
    where: { mediaId },
  });

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} marked as completed successfully`,
    completionCount,
  });
});

// DELETE /api/media/[mediaId]/complete - Remove completion status from a media item
export const DELETE = withApiMiddleware<RouteParams>({
  ...middlewarePresets.secure(rateLimiters.general),
})(async (request) => {
  const { user } = request;
  const { mediaId } = request.params as RouteParams;

  if (!mediaId) {
    throw createApiError.validation('Media ID is required');
  }

  // Check if media exists
  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    select: { id: true, type: true, title: true }
  });

  if (!media) {
    throw createApiError.notFound('Media not found');
  }

  // Find and delete the completion
  const existingCompletion = await prisma.completedLink.findUnique({
    where: {
      userId_mediaId_linkId: {
        userId: user!.id,
        mediaId,
        linkId: "",
      },
    },
  });

  if (!existingCompletion) {
    throw createApiError.notFound('Completion not found');
  }

  await prisma.completedLink.delete({
    where: { id: existingCompletion.id },
  });

  // Log the interaction
  await AuditLogger.logUserEngagement(
    user!.id,
    'MEDIA_UNCOMPLETE',
    mediaId,
    undefined,
    {
      mediaType: media.type,
      mediaTitle: media.title,
    }
  );

  // Get updated completion count
  const completionCount = await prisma.completedLink.count({
    where: { mediaId },
  });

  return NextResponse.json({
    success: true,
    message: `${media.type === 'ebook' ? 'Ebook' : 'Video'} completion status removed successfully`,
    completionCount,
  });
});
