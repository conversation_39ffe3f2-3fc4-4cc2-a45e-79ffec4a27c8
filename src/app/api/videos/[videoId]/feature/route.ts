import { prisma } from '@/lib/prisma';
import getServerSession from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ videoId: string }> }
) {
  const session = await getServerSession(authOptions);

  if (!session || session.user?.role !== 'ADMIN') {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  const { videoId } = await params;

  try {
    const video = await prisma.video.findUnique({
      where: { id: videoId },
    });

    if (!video) {
      return new NextResponse('Video not found', { status: 404 });
    }

    const updatedVideo = await prisma.video.update({
      where: { id: videoId },
      data: { isFeatured: !video.isFeatured },
    });

    // Invalidate relevant caches after feature status change
    await safeInvalidateCache(
      () => CacheInvalidation.onFeaturedVideoChange(),
      'video feature toggle'
    );

    return NextResponse.json(updatedVideo);
  } catch (error) {
    console.error('Error toggling feature status:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
