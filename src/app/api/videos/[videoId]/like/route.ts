import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/videos/[videoId]/like - Like a video
export async function POST(
  request: Request,
  { params }: { params: Promise<{ videoId: string }> }
) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { videoId } = await params;
  const userId = session.user.id;

  try {
    // Parse request body to get platform information
    const body = await request.json().catch(() => ({}));
    const platform = body.platform?.toLowerCase() || 'unknown';

    // Check if the video exists
    const video = await prisma.video.findUnique({ where: { id: videoId } });
    if (!video) {
      return NextResponse.json({ message: 'Video not found' }, { status: 404 });
    }

    // Find or create a Media record for this video
    let media = await prisma.media.findFirst({
      where: { 
        videoUrl: video.url,
        type: 'video'
      }
    });
    
    if (!media) {
      // Create a Media record if it doesn't exist
      media = await prisma.media.create({
        data: {
          type: 'video',
          title: video.title,
          description: video.description,
          videoUrl: video.url,
          published: true,
          userId: video.userId,
        }
      });
    }

    // Create like record (existing functionality)
    const newLike = await prisma.like.create({
      data: {
        videoId,
        userId,
        mediaId: media.id,
      },
    });

    // Track engagement with platform information
    if (platform !== 'unknown') {
      await prisma.userEngagement.upsert({
        where: {
          userId_videoId_platform_action: {
            userId,
            videoId,
            platform,
            action: 'like',
          },
        },
        update: {
          likedAt: new Date(),
        },
        create: {
          userId,
          videoId,
          platform,
          action: 'like',
          likedAt: new Date(),
        },
      });
    }

    return NextResponse.json(newLike, { status: 201 });
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2002') {
      return NextResponse.json({ message: 'You have already liked this video.' }, { status: 409 });
    }
    console.error('Error liking video:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/videos/[videoId]/like - Unlike a video
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ videoId: string }> }
) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { videoId } = await params;
  
  const userId = session.user.id;

  try {
    // Find the video to get media info
    const video = await prisma.video.findUnique({ where: { id: videoId } });
    if (!video) {
      return NextResponse.json({ message: 'Video not found' }, { status: 404 });
    }

    // Find the media record
    const media = await prisma.media.findFirst({
      where: { 
        videoUrl: video.url,
        type: 'video'
      }
    });
    
    if (!media) {
      return NextResponse.json({ message: 'Media not found' }, { status: 404 });
    }

    await prisma.like.delete({
      where: {
        userId_mediaId_videoId: { userId, mediaId: media.id, videoId },
      },
    });

    return new NextResponse(null, { status: 204 }); // No Content
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2025') {
      // This means the record to delete was not found.
      return NextResponse.json({ message: 'Like not found' }, { status: 404 });
    }
    console.error('Error unliking video:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
