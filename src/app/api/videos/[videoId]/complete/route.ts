import { prisma } from '@/lib/prisma';
import getServerSession from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ videoId: string }> }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  const { videoId } = await params;
  const userId = session.user?.id;

  try {
    // Find the corresponding mediaId for this video
    const media = await prisma.media.findFirst({
      where: { type: 'video', videoUrl: videoId },
    });
    if (!media) {
      return new NextResponse('Media not found for video', { status: 404 });
    }
    const mediaId = media.id;

    const existingCompletion = await prisma.completedLink.findUnique({
      where: { userId_mediaId_linkId: { userId, mediaId, linkId: '' } },
    });

    if (existingCompletion) {
      return NextResponse.json({ message: 'Video already completed' }, { status: 200 });
    }

    await prisma.completedLink.create({
      data: {
        userId,
        mediaId,
        videoId,
        linkId: '',
        completedAt: new Date(),
      },
    });

    // Invalidate relevant caches
    await safeInvalidateCache(
      () => CacheInvalidation.onVideoCompletion(userId, videoId),
      'video completion'
    );

    return NextResponse.json({ message: 'Video marked as complete' }, { status: 201 });
  } catch (error) {
    console.error('Error completing video:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
