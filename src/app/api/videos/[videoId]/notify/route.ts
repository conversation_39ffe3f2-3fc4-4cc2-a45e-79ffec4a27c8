import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ videoId: string }> }
) {
  const { videoId } = await params;
  const session = await getServerSession(authOptions);

  if (session?.user?.role !== 'ADMIN') {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const video = await prisma.video.findUnique({ where: { id: videoId } });

    if (!video) {
      return new NextResponse('Video not found', { status: 404 });
    }

    if (video.notificationSentAt) {
      return NextResponse.json(
        { message: 'Notification already sent for this video' },
        { status: 400 }
      );
    }

    // Get users who have opted in for NWA video notifications
    const allUsers = await prisma.user.findMany({
      where: {
        nwaVideoNotifications: true,
      },
      select: { id: true },
    });

    const notification = await prisma.notification.create({
      data: {
        title: 'New NWA Video!',
        message: `Check out the new video: "${video.title}"`,
        type: 'VIDEO_RELEASE',
        sentBy: session.user.id,
        videoId: video.id,
        isSent: true,
        sentAt: new Date(),
        recipients: {
          create: allUsers.map(user => ({
            userId: user.id,
          })),
        },
      },
    });

    await prisma.video.update({
      where: { id: videoId },
      data: { notificationSentAt: new Date() },
    });

    return NextResponse.json({ 
      message: 'Notification sent successfully',
      notificationId: notification.id 
    });

  } catch (error) {
    console.error('Error sending notification:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
