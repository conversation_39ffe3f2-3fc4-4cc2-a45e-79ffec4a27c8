import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { transformVideoWithPlatforms } from '@/lib/video-utils';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { commonSchemas } from '@/lib/api-validation';

interface SearchFilters {
  query?: string;
  type?: 'nwa' | 'user' | 'all';
  platform?: 'youtube' | 'tiktok' | 'rumble';
  status?: string;
  userId?: string;
  featured?: boolean;
}

// GET /api/videos/search - Search videos with title and description search
export const GET = withApiMiddleware({
  ...middlewarePresets.readOnly(rateLimiters.read),
  queryValidation: {
    ...commonSchemas.pagination,
    ...commonSchemas.search,
    userId: { type: 'string' },
  },
})(async (request) => {
  const { validatedQuery } = request;
  
  // Use validated and sanitized query parameters
  const typedValidatedQuery = validatedQuery as {
    page?: string;
    limit?: string;
    offset?: string;
    query?: string;
    type?: string;
    platform?: string;
    status?: string;
    userId?: string;
    featured?: string;
  };
  
  // Use validated query parameters with defaults
  const page = typedValidatedQuery?.page ? parseInt(typedValidatedQuery.page) : 1;
  const limit = typedValidatedQuery?.limit ? Math.min(parseInt(typedValidatedQuery.limit), 50) : 10;
  const offset = (page - 1) * limit;
  
  // Search and filter parameters from validated query
  const filters: SearchFilters = {
    query: typedValidatedQuery?.query,
    type: (typedValidatedQuery?.type as SearchFilters['type']) || 'all',
    platform: (typedValidatedQuery?.platform as SearchFilters['platform']),
    status: typedValidatedQuery?.status || 'published',
    userId: typedValidatedQuery?.userId,
    featured: typedValidatedQuery?.featured === 'true' ? true : typedValidatedQuery?.featured === 'false' ? false : undefined,
  };

    // Build where clause based on filters
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {
      status: filters.status,
    };

    // Add search query for title and description
    if (filters.query) {
      const searchQuery = filters.query.trim();
      if (searchQuery) {
        whereClause.OR = [
          {
            title: {
              contains: searchQuery,
              mode: 'insensitive',
            },
          },
          {
            description: {
              contains: searchQuery,
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    // Filter by video type (NWA vs user videos)
    if (filters.type === 'nwa') {
      whereClause.user = {
        role: { in: ['ADMIN', 'NWA_TEAM'] },
      };
    } else if (filters.type === 'user') {
      whereClause.user = {
        role: 'USER',
      };
    }

    // Filter by specific user
    if (filters.userId) {
      whereClause.userId = filters.userId;
    }

    // Filter by featured status
    if (filters.featured !== undefined) {
      whereClause.isFeatured = filters.featured;
    }

    // Filter by platform (if video has URL for that platform)
    if (filters.platform) {
      switch (filters.platform.toLowerCase()) {
        case 'youtube':
          whereClause.youtubeUrl = { not: null };
          break;
        case 'tiktok':
          whereClause.tiktokUrl = { not: null };
          break;
        case 'rumble':
          whereClause.rumbleUrl = { not: null };
          break;
      }
    }

    // Execute search query
    const [videos, total] = await Promise.all([
      prisma.video.findMany({
        where: whereClause,
        orderBy: [
          { isFeatured: 'desc' }, // Featured videos first
          { createdAt: 'desc' },  // Then by creation date
        ],
        take: limit,
        skip: offset,
        include: {
          user: {
            select: { 
              id: true,
              name: true, 
              role: true 
            },
          },
          likes: {
            select: { id: true, userId: true },
          },
          shares: {
            select: { id: true, userId: true },
          },
          engagements: {
            select: { platform: true, action: true, userId: true },
          },
          links: {
            select: { 
              id: true, 
              platform: true, 
              linkUrl: true,
              userId: true,
            },
          },
        },
      }),
      prisma.video.count({
        where: whereClause,
      }),
    ]);

    // Transform videos to include platform information and stats
    const transformedVideos = videos.map(video => ({
      ...transformVideoWithPlatforms(video),
      stats: {
        totalLikes: video.likes.length,
        totalShares: video.shares.length,
        totalLinks: video.links.length,
        platformEngagement: video.engagements.reduce((acc, eng) => {
          if (!acc[eng.platform]) acc[eng.platform] = { likes: 0, shares: 0 };
          if (eng.action === 'like') acc[eng.platform].likes++;
          if (eng.action === 'share') acc[eng.platform].shares++;
          return acc;
        }, {} as Record<string, { likes: number; shares: number }>),
      },
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

  return NextResponse.json({
    videos: transformedVideos,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNextPage,
      hasPreviousPage,
    },
    filters: {
      query: filters.query,
      type: filters.type,
      platform: filters.platform,
      status: filters.status,
      userId: filters.userId,
      featured: filters.featured,
    },
    searchInfo: {
      resultsCount: transformedVideos.length,
      searchQuery: filters.query,
      hasResults: transformedVideos.length > 0,
    },
  });
});