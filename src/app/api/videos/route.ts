import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import type { Prisma } from '@/generated/prisma/client';
import {
  validatePlatformUrls,
  transformVideoWithPlatforms,
  getPrimaryPlatformInfo,
  PlatformUrls
} from '@/lib/video-utils';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { commonSchemas } from '@/lib/api-validation';
import { createApiError } from '@/lib/api-errors';
import { awardPoints, SCORING_CONFIG } from '@/lib/scoring-utils';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';
import { VideoQueryOptimizer } from '@/lib/query-optimization';
import { monitoredQueries } from '@/lib/query-performance-monitor';
import { moderateContent, initializeContentFilters } from '@/lib/content-moderation';
import { AuditLogger } from '@/lib/audit-logger';





interface VideoQuery {
  page?: string;
  limit?: string;
  offset?: string;
  type?: string;
  status?: string;
  userId?: string;
  platform?: string;
  featured?: string;
  query?: string;
}

// GET /api/videos - Unified endpoint to fetch videos with filtering and search
export const GET = withApiMiddleware<unknown, VideoQuery>({
  ...middlewarePresets.readOnly(rateLimiters.read),
  queryValidation: {
    ...commonSchemas.pagination,
    ...commonSchemas.search,
    userId: { type: 'string' },
  },
})(async (request) => {
  const { validatedQuery } = request;
  
  // Use validated and sanitized query parameters
  const typedValidatedQuery = validatedQuery as VideoQuery; // Explicitly cast once
  
  // Use validated query parameters with defaults
  const page = typedValidatedQuery?.page ? parseInt(typedValidatedQuery.page) : 1;
  const limit = typedValidatedQuery?.limit ? Math.min(parseInt(typedValidatedQuery.limit), 50) : 10;
  const offset = typedValidatedQuery?.offset ? parseInt(typedValidatedQuery.offset) : (page - 1) * limit;
  
  // Filter parameters from validated query
  const type = typedValidatedQuery?.type || 'all';
  const status = typedValidatedQuery?.status || 'published';
  const userId = typedValidatedQuery?.userId;
  const platform = typedValidatedQuery?.platform;
  const featured = typedValidatedQuery?.featured === 'true' ? true : typedValidatedQuery?.featured === 'false' ? false : undefined;
  const query = typedValidatedQuery?.query;

    // Use optimized video query with performance monitoring
    const { videos: optimizedVideos, total } = await monitoredQueries.getVideos(
      async () => {
        if (query) {
          // Use search optimization for queries
          return await VideoQueryOptimizer.searchVideos(
            query.trim(),
            { type: type as 'nwa' | 'user' | 'all', platform, userId },
            { take: limit, skip: offset }
          );
        } else {
          // Build where clause for regular filtering
          const whereClause: Prisma.VideoWhereInput = { status };

          // Filter by video type (NWA vs user videos)
          if (type === 'nwa') {
            whereClause.user = { role: { in: ['ADMIN', 'NWA_TEAM'] } };
          } else if (type === 'user') {
            whereClause.user = { role: 'USER' };
          }

          // Filter by specific user
          if (userId) {
            whereClause.userId = userId;
          }

          // Filter by featured status
          if (featured !== undefined) {
            whereClause.isFeatured = featured;
          }

          // Filter by platform
          if (platform) {
            switch (platform.toLowerCase()) {
              case 'youtube':
                whereClause.youtubeUrl = { not: null };
                break;
              case 'tiktok':
                whereClause.tiktokUrl = { not: null };
                break;
              case 'rumble':
                whereClause.rumbleUrl = { not: null };
                break;
            }
          }

          const videos = await VideoQueryOptimizer.getVideosWithRelations(
            whereClause,
            {
              take: limit,
              skip: offset,
              orderBy: [{ createdAt: 'desc' }],
              userId: request.user?.id, // For user interactions
            }
          );

          const total = await prisma.video.count({ where: whereClause });
          return { videos, total };
        }
      },
      { userId: request.user?.id, filters: { type, status, platform, featured, query } }
    );

    // Transform videos to include platform information and stats
    const transformedVideos = optimizedVideos.map(video => ({
      ...transformVideoWithPlatforms(video),
      stats: {
        totalLikes: video._count.likes,
        totalShares: video._count.shares,
        totalLinks: video._count ? video._count.engagements : 0,
        platformEngagement: video.userInteractions || {},
      },
      userInteractions: video.userInteractions,
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      videos: transformedVideos,
      total,
      hasMore: offset + limit < total,
      pagination: {
        page,
        limit,
        offset,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        type,
        status,
        userId,
        platform,
        featured,
        query,
      },
    });
});

// POST /api/videos - Unified endpoint to create videos with multi-platform support
export const POST = withApiMiddleware({
  ...middlewarePresets.secure(rateLimiters.videoCreation),
  bodyValidation: commonSchemas.secureCreateVideo,
})(async (request) => {
  const { user, validatedBody } = request;
  const { title, description, platforms, thumbnailUrl, duration, type = 'user' } = validatedBody as {
    title: string;
    description?: string;
    platforms: PlatformUrls;
    thumbnailUrl?: string;
    duration?: number;
    type?: 'nwa' | 'user';
  };

  // Check permissions for NWA video creation
  if (type === 'nwa' && (!user!.role || !['ADMIN', 'NWA_TEAM'].includes(user!.role))) {
    throw createApiError.forbidden('Insufficient permissions to create NWA videos');
  }

  // Additional platform URL validation (beyond schema validation)
  const urlErrors = validatePlatformUrls(platforms);
  if (urlErrors.length > 0) {
    throw createApiError.validation('Invalid URL format', urlErrors);
  }

  // Content moderation for user-submitted videos
  if (type === 'user') {
    // Initialize content filters if not already done
    await initializeContentFilters();

    // Get all platform URLs for moderation
    const urls = [
      platforms.youtubeUrl,
      platforms.tiktokUrl,
      platforms.rumbleUrl,
    ].filter(Boolean) as string[];

    // Moderate the content
    const moderationResult = await moderateContent(title, description || null, urls);

    // Block content if flagged as inappropriate
    if (moderationResult.isBlocked) {
      throw createApiError.forbidden(
        'Content blocked due to policy violations'
      );
    }

    // If content is flagged but not blocked, set status to pending review
    const _videoStatus = moderationResult.isFlagged ? 'pending' : 'published';
    
    // Log moderation result for admin review if flagged
    if (moderationResult.isFlagged) {
      console.log('Content flagged for review:', {
        title,
        userId: user!.id,
        score: moderationResult.score,
        matchedFilters: moderationResult.matchedFilters,
      });
    }
  }

  // Determine primary platform and URL for backward compatibility
  const { platform: primaryPlatform, url: primaryUrl } = getPrimaryPlatformInfo(platforms);

  const newVideo = await prisma.video.create({
    data: {
      title,
      description,
      url: primaryUrl, // For backward compatibility
      platform: primaryPlatform, // For backward compatibility
      youtubeUrl: platforms.youtubeUrl,
      tiktokUrl: platforms.tiktokUrl,
      rumbleUrl: platforms.rumbleUrl,
      thumbnailUrl,
      duration,
      status: 'published',
      userId: user!.id,
    },
    include: {
      user: {
        select: { 
          id: true,
          name: true, 
          role: true 
        },
      },
    },
  });

  // Award points for user video creation (requirement 9.4)
  if (type === 'user') {
    try {
      await awardPoints(
        user!.id, 
        SCORING_CONFIG.CREATE_USER_VIDEO, 
        `Created personal video: "${title}"`
      );
    } catch (error) {
      console.error('Error awarding points for video creation:', error);
      // Don't fail the video creation if scoring fails
    }
  }

  // Log the video creation action
  if (type === 'nwa') {
    await AuditLogger.logAdminAction(
      user!.id,
      'VIDEO_CREATE',
      'video',
      newVideo.id,
      {
        title,
        description,
        platforms,
        thumbnailUrl,
        duration,
      },
      {
        userAgent: request.headers.get('user-agent') || undefined,
      }
    );
  } else {
    await AuditLogger.logUserEngagement(
      user!.id,
      'VIDEO_CREATE',
      newVideo.id,
      undefined,
      {
        title,
        platforms,
        type,
      }
    );
  }

  // Invalidate relevant caches after video creation
  await safeInvalidateCache(
    () => CacheInvalidation.onVideoChange(newVideo.id, user!.id),
    'video creation'
  );

  // Transform response to include platforms object
  const response = transformVideoWithPlatforms(newVideo);

  return NextResponse.json(response, { status: 201 });
});