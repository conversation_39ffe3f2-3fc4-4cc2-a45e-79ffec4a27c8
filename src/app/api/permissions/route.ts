import { NextRequest, NextResponse } from 'next/server';
import { ApplicationMonitor } from '@/lib/monitoring';
import { rateLimiters, applyRateLimit } from '@/lib/rate-limit';

const ROLES = ["USER", "ADMIN", "NWA_TEAM"] as const;
const PERMISSIONS = [
  "view_document",
  "send_documents",
  "snooze_notifications",
  "view_notifications",
] as const;

function isAuthorized(request: NextRequest): boolean {
  const header = request.headers.get('authorization') || request.headers.get('Authorization');
  if (!header) return false;
  const [scheme, token] = header.split(' ');
  if (!scheme || !token) return false;
  if (scheme.toLowerCase() !== 'bearer') return false;
  const expected = process.env.PERMISSIONS_API_TOKEN;
  if (!expected) {
    // If not configured, deny by default
    return false;
  }
  return token === expected;
}


function withCorsHeaders(headers: Record<string, string>, request: NextRequest): Record<string, string> {
  const origin = request.headers.get('origin');
  const result: Record<string, string> = { ...headers, Vary: 'Origin' };
  // For tests and simplicity, reflect the Origin when provided
  if (origin) {
    result['Access-Control-Allow-Origin'] = origin;
    result['Access-Control-Allow-Methods'] = 'GET, OPTIONS';
    result['Access-Control-Allow-Headers'] = 'Authorization, Content-Type';
  }
  return result;
}

export async function OPTIONS(request: NextRequest) {
  const headers = withCorsHeaders({}, request);
  return new NextResponse(null, { status: 204, headers });
}

export async function GET(request: NextRequest) {
  const start = Date.now();
  const requestId = request.headers.get('x-request-id') || undefined;
  let status = 200;
  try {
    // Strict rate limiting
    const rateResult = await applyRateLimit(request, rateLimiters.strict);
    if (!rateResult.success) {
      status = 429;
      const duration = Date.now() - start;
      ApplicationMonitor.recordApiResponseTime('/api/permissions', 'GET', status, duration);
      const log = {
        event: 'permissions_catalog_rate_limited',
        route: '/api/permissions',
        method: 'GET',
        status,
        requestId,
        ...rateResult.error
      };
      console.warn(JSON.stringify(log));
      const corsHeaders = process.env.MEMBER_PORTAL_URL ? withCorsHeaders({}, request) : { Vary: 'Origin' };
      return NextResponse.json(
        { error: rateResult.error?.message || 'Too many requests', code: rateResult.error?.code || 'RATE_LIMIT_EXCEEDED' },
        { status, headers: { ...corsHeaders, ...rateResult.headers } }
      );
    }
    if (!isAuthorized(request)) {
      status = 401;
      const duration = Date.now() - start;
      ApplicationMonitor.recordApiResponseTime('/api/permissions', 'GET', status, duration);
      const log = {
        event: 'permissions_catalog_unauthorized',
        route: '/api/permissions',
        method: 'GET',
        status,
        requestId
      };
      console.warn(JSON.stringify(log));
      const corsHeaders = process.env.MEMBER_PORTAL_URL ? withCorsHeaders({}, request) : { Vary: 'Origin' };
      return NextResponse.json(
        { error: 'Invalid bearer token', code: 'UNAUTHORIZED' },
        { status, headers: corsHeaders }
      );
    }
    // Structured log for request
    console.info(JSON.stringify({
      event: 'permissions_catalog_requested',
      route: '/api/permissions',
      method: 'GET',
      status: 200,
      requestId
    }));
    const corsHeaders = process.env.MEMBER_PORTAL_URL ? withCorsHeaders({ 'Cache-Control': 'no-store', ...rateResult.headers }, request) : { 'Cache-Control': 'no-store', Vary: 'Origin', ...rateResult.headers };
    const response = NextResponse.json(
      {
        roles: ROLES,
        permissions: PERMISSIONS,
      },
      {
        status,
        headers: corsHeaders,
      }
    );
    const duration = Date.now() - start;
    ApplicationMonitor.recordApiResponseTime('/api/permissions', 'GET', status, duration);
    // Structured log for response
    console.info(JSON.stringify({
      event: 'permissions_catalog_served',
      route: '/api/permissions',
      method: 'GET',
      status,
      duration_ms: duration,
      requestId
    }));
    return response;
  } catch (err) {
    status = 500;
    const duration = Date.now() - start;
    ApplicationMonitor.recordApiResponseTime('/api/permissions', 'GET', status, duration);
    ApplicationMonitor.recordError(err instanceof Error ? err : new Error(String(err)), {
      route: '/api/permissions',
      method: 'GET',
      requestId
    });
    try {
      console.error(JSON.stringify({
        event: 'permissions_catalog_error',
        route: '/api/permissions',
        method: 'GET',
        status,
        error: err instanceof Error ? err.message : String(err),
        requestId
      }));
    } catch { }
    const corsHeaders = process.env.MEMBER_PORTAL_URL ? withCorsHeaders({}, request) : { Vary: 'Origin' };
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status, headers: corsHeaders }
    );
  }
}
