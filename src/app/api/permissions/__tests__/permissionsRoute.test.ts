// src/app/api/permissions/__tests__/permissionsRoute.test.ts
import type { NextRequest } from 'next/server';

// Mock NextResponse.json
const jsonMock = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: { json: jsonMock },
}));

// Make monitoring no-op for Redis writes and alerts in tests
jest.mock('@/lib/monitoring', () => {
  const actual = jest.requireActual('@/lib/monitoring');
  return {
    ...actual,
    ApplicationMonitor: {
      ...actual.ApplicationMonitor,
      recordApiResponseTime: jest.fn(),
      recordError: jest.fn(),
    },
  };
});

// Import after mocks
import { GET } from '../route';

describe('GET /api/permissions', () => {
  const OLD_ENV = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...OLD_ENV };
    process.env.PERMISSIONS_API_TOKEN = 'secret-token';
    process.env.MEMBER_PORTAL_URL = 'http://localhost:3000';
    // NODE_ENV is read-only in newer Node versions, so we don't override it
  });

  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('returns 401 when Authorization header is missing', async () => {
    const req = {
      headers: {
        get: jest.fn().mockImplementation((name: string) => null),
      },
      url: 'http://localhost/api/permissions',
    } as unknown as NextRequest;

    await GET(req);

    expect(jsonMock).toHaveBeenCalledWith(
      { error: 'Invalid bearer token', code: 'UNAUTHORIZED' },
      { status: 401, headers: { Vary: 'Origin' } }
    );
  });

  it('returns 401 when Authorization header has wrong token', async () => {
    const req = {
      headers: {
        get: jest.fn().mockImplementation((name: string) => {
          if (name.toLowerCase() === 'authorization') return 'Bearer wrong-token';
          return null;
        }),
      },
      url: 'http://localhost/api/permissions',
    } as unknown as NextRequest;

    await GET(req);

    expect(jsonMock).toHaveBeenCalledWith(
      { error: 'Invalid bearer token', code: 'UNAUTHORIZED' },
      { status: 401, headers: { Vary: 'Origin' } }
    );
  });

  it('returns 200 with roles and permissions when bearer token matches', async () => {
    const req = {
      headers: {
        get: jest.fn().mockImplementation((name: string) => {
          if (name.toLowerCase() === 'authorization') return 'Bearer secret-token';
          return null;
        }),
      },
      url: 'http://localhost/api/permissions',
    } as unknown as NextRequest;

    await GET(req);

    expect(jsonMock).toHaveBeenCalledWith(
      {
        roles: ["USER", "ADMIN", "NWA_TEAM"],
        permissions: [
          'view_document',
          'send_documents',
          'snooze_notifications',
          'view_notifications',
        ],
      },
      expect.objectContaining({ status: 200, headers: expect.objectContaining({ 'Cache-Control': 'no-store', Vary: 'Origin' }) })
    );
  });

  it('includes CORS headers when Origin matches', async () => {
    const req = {
      headers: {
        get: jest.fn().mockImplementation((name: string) => {
          if (name.toLowerCase() === 'authorization') return 'Bearer secret-token';
          if (name.toLowerCase() === 'origin') return 'https://member.example.com';
          return null;
        }),
      },
      url: 'http://localhost/api/permissions',
    } as unknown as NextRequest;

    await GET(req);

    expect(jsonMock).toHaveBeenCalledWith(
      {
        roles: ["USER", "ADMIN", "NWA_TEAM"],
        permissions: [
          'view_document',
          'send_documents',
          'snooze_notifications',
          'view_notifications',
        ],
      },
      expect.objectContaining({ status: 200, headers: expect.objectContaining({ 'Cache-Control': 'no-store', Vary: 'Origin', 'Access-Control-Allow-Origin': 'https://member.example.com', 'Access-Control-Allow-Methods': 'GET, OPTIONS', 'Access-Control-Allow-Headers': 'Authorization, Content-Type' }) })
    );
  });
});
