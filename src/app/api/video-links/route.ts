import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { validatePlatformUrl } from '@/lib/video-utils';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';
import { VideoLinkQueryOptimizer } from '@/lib/query-optimization';
import { monitoredQueries } from '@/lib/query-performance-monitor';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { commonSchemas } from '@/lib/api-validation';
import { createApiError } from '@/lib/api-errors';
import { InputSanitizer, SQLInjectionPrevention } from '@/lib/security';
import type { Prisma } from '@/generated/prisma/client';

type VideoLinkWhereInput = Prisma.VideoLinkWhereInput;

interface VideoLinksQuery {
  userId?: string;
  page?: string;
  limit?: string;
  offset?: string;
}

// GET /api/video-links - Fetch video links with enhanced video information
export const GET = withApiMiddleware<unknown, VideoLinksQuery>({
  ...middlewarePresets.readOnly(rateLimiters.read),
  queryValidation: {
    ...commonSchemas.pagination,
    userId: { 
      type: 'string',
      custom: (value: unknown) => {
        if (typeof value !== 'string' || !value) return null;
        try {
          SQLInjectionPrevention.sanitizeQueryParam(value, 'uuid');
        } catch (_error) {
          return 'Invalid user ID format';
        }
        return null;
      },
    },
  },
})(async (request) => {
  const { validatedQuery } = request;
  
  // Use validated and sanitized query parameters
  const typedValidatedQuery = validatedQuery as VideoLinksQuery; // Explicitly cast once
  const userId = typedValidatedQuery?.userId;
  const limit = typedValidatedQuery?.limit ? Math.min(parseInt(typedValidatedQuery.limit), 50) : 50;
  const offset = typedValidatedQuery?.offset ? parseInt(typedValidatedQuery.offset) : 0;

  try {
    // Sanitize where clause to prevent SQL injection
    const whereClause: VideoLinkWhereInput = userId ? SQLInjectionPrevention.validateWhereClause({ userId }) as VideoLinkWhereInput : {};
    
    // Use optimized video link query with performance monitoring
    const videoLinks = await monitoredQueries.getVideos(
      async () => {
        return await VideoLinkQueryOptimizer.getVideoLinksWithRelations(
          whereClause,
          {
            take: limit,
            skip: offset,
            orderBy: [{ createdAt: 'desc' }],
            userId: userId ?? undefined, // For user-specific interactions
          }
        );
      },
      { userId, filters: { limit, offset } }
    );

    // Transform video links to include platform information and stats
    const transformedVideoLinks = videoLinks.map(link => ({
      ...link,
      Video: {
        ...link.Video,
        platforms: {
          youtube: link.Video?.youtubeUrl,
          tiktok: link.Video?.tiktokUrl,
          rumble: link.Video?.rumbleUrl,
        },
      },
      stats: {
        totalLikes: link._count.likes,
        totalShares: link._count.shares,
      },
      userInteractions: link.userInteractions,
    }));

    const total = await prisma.videoLink.count({
      where: whereClause,
    });

    return NextResponse.json({
      videoLinks: transformedVideoLinks,
      total,
      hasMore: offset + limit < total,
    });
  } catch (error) {
    console.error('Error fetching video links:', error);
    throw createApiError.internal('Failed to fetch video links');
  }
});

// POST /api/video-links - Create a new video link with validation
export const POST = withApiMiddleware<{ videoId: string; linkUrl: string; platform: string }>({
  ...middlewarePresets.secure(rateLimiters.videoCreation),
  bodyValidation: {
    videoId: {
      required: true,
      type: 'string',
      custom: (value: unknown) => {
        if (typeof value !== 'string' || !value) return null;
        try {
          SQLInjectionPrevention.sanitizeQueryParam(value, 'uuid');
        } catch (_error) {
          return 'Invalid video ID format';
        }
        return null;
      },
    },
    linkUrl: {
      required: true,
      type: 'string',
      custom: (value: unknown) => {
        if (typeof value !== 'string' || !value) return null;
        try {
          InputSanitizer.sanitizeUrl(value);
        } catch (_error) {
          return 'Invalid URL format';
        }
        return null;
      },
    },
    platform: {
      required: true,
      type: 'string',
      enum: ['youtube', 'tiktok', 'rumble'],
    },
  },
})(async (request) => {
  const { user, validatedBody } = request;
  const { videoId, linkUrl, platform } = validatedBody as { videoId: string; linkUrl: string; platform: string };

  try {
    // Additional platform-specific URL validation
    const isValidUrl = validatePlatformUrl(linkUrl, platform.toLowerCase());
    if (!isValidUrl) {
      throw createApiError.validation(`Invalid ${platform} URL format`);
    }

    // Sanitize where clause for database query
    const videoWhereClause = SQLInjectionPrevention.validateWhereClause({ id: videoId }) as Prisma.VideoWhereUniqueInput;

    // Check if the video exists
    const videoExists = await prisma.video.findUnique({
      where: videoWhereClause,
      select: { 
        id: true, 
        title: true, 
        status: true,
        youtubeUrl: true,
        tiktokUrl: true,
        rumbleUrl: true,
      },
    });

    if (!videoExists) {
      throw createApiError.notFound('Video', videoId);
    }

    // Check if video is published
    if (videoExists.status !== 'published') {
      throw createApiError.validation('Cannot create link for unpublished video');
    }

    const newVideoLink = await prisma.videoLink.create({
      data: {
        videoId,
        linkUrl,
        platform: platform.toLowerCase(),
        userId: user!.id,
      },
      include: {
        User: { 
          select: { 
            id: true, 
            name: true, 
            role: true 
          } 
        },
        Video: { 
          select: { 
            id: true, 
            title: true, 
            description: true,
            youtubeUrl: true,
            tiktokUrl: true,
            rumbleUrl: true,
            thumbnailUrl: true,
            status: true,
            isFeatured: true,
            createdAt: true,
          } 
        },
      },
    });

    // Invalidate relevant caches after video link creation
    await safeInvalidateCache(
      () => CacheInvalidation.onVideoLinkChange(user!.id),
      'video link creation'
    );

    // Transform response to include platforms object
    const response = {
      ...newVideoLink,
      Video: {
        ...newVideoLink.Video,
        platforms: {
          youtube: newVideoLink.Video?.youtubeUrl,
          tiktok: newVideoLink.Video?.tiktokUrl,
          rumble: newVideoLink.Video?.rumbleUrl,
        },
      },
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error: unknown) {
    console.error('Error creating video link:', error);
    
    // Handle potential unique constraint violation gracefully
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2002') {
      throw createApiError.conflict('You have already linked this video on this platform');
    }
    
    throw error;
  }
});


