import { NextRequest, NextResponse } from 'next/server';
import getServerSession from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/video-links/[videoLinkId]/share - Share a video link
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ videoLinkId: string }> }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { videoLinkId } = await params;

  const userId = session.user?.id;

  try {
    // Check if the video link exists
    const videoLink = await prisma.videoLink.findUnique({ 
      where: { id: videoLinkId },
      include: { Video: true }
    });
    if (!videoLink) {
      return NextResponse.json({ message: 'Video link not found' }, { status: 404 });
    }

    const newShare = await prisma.videoLinkShare.create({
      data: {
        videoLinkId,
        userId,
      },
    });

    // Track engagement with platform information from the video link
    await prisma.userEngagement.upsert({
      where: {
        userId_videoId_platform_action: {
          userId,
          videoId: videoLink.videoId,
          platform: videoLink.platform.toLowerCase(),
          action: 'share',
        },
      },
      update: {
        createdAt: new Date(),
      },
      create: {
        userId,
        videoId: videoLink.videoId,
        platform: videoLink.platform.toLowerCase(),
        action: 'share',
      },
    });

    return NextResponse.json(newShare, { status: 201 });
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2002') {
      return NextResponse.json({ message: 'You have already shared this video link.' }, { status: 409 });
    }
    console.error('Error sharing video link:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/video-links/[videoLinkId]/share - Un-share a video link
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ videoLinkId: string }> }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { videoLinkId } = await params;

  const userId = session.user?.id;

  try {
    await prisma.videoLinkShare.delete({
      where: {
        userId_videoLinkId: { userId, videoLinkId },
      },
    });

    return new NextResponse(null, { status: 204 }); // No Content
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2025') {
      return NextResponse.json({ message: 'Share not found' }, { status: 404 });
    }
    console.error('Error un-sharing video link:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
