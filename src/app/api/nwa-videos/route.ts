import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { 
  PlatformUrls, 
  validatePlatformUrls, 
  transformVideoWithPlatforms,
  getPrimaryPlatformInfo,
  hasValidPlatformUrl
} from '@/lib/video-utils';

interface CreateVideoRequest {
  title: string;
  description?: string;
  platforms: PlatformUrls;
  thumbnailUrl?: string;
  duration?: number;
}

// GET /api/nwa-videos - Fetch all NWA videos with multi-platform support and filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters - support both offset/limit and page-based pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 results per page
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset') || '0') : (page - 1) * limit;
    
    // Filter parameters
    const status = searchParams.get('status') || 'published';
    const platform = searchParams.get('platform');
    const featured = searchParams.get('featured') === 'true' ? true : undefined;
    const query = searchParams.get('query');

    // Build where clause for NWA videos with additional filters
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {
      user: {
        role: { in: ['ADMIN', 'NWA_TEAM'] },
      },
      status,
    };

    // Add search query for title and description
    if (query) {
      const searchQuery = query.trim();
      if (searchQuery) {
        whereClause.OR = [
          {
            title: {
              contains: searchQuery,
              mode: 'insensitive',
            },
          },
          {
            description: {
              contains: searchQuery,
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    // Filter by featured status
    if (featured !== undefined) {
      whereClause.isFeatured = featured;
    }

    // Filter by platform (if video has URL for that platform)
    if (platform) {
      switch (platform.toLowerCase()) {
        case 'youtube':
          whereClause.youtubeUrl = { not: null };
          break;
        case 'tiktok':
          whereClause.tiktokUrl = { not: null };
          break;
        case 'rumble':
          whereClause.rumbleUrl = { not: null };
          break;
      }
    }

    // Exclude completed videos for the current user (if authenticated)
    if (session?.user?.id) {
      whereClause.completions = {
        none: {
          userId: session.user.id,
        },
      };
    }

    const videos = await prisma.video.findMany({
      where: whereClause,
      orderBy: [
        { isFeatured: 'desc' }, // Featured videos first
        { createdAt: 'asc' },   // Then oldest first (requirement 6.4: oldest videos first, newest last)
      ],
      take: limit,
      skip: offset,
      include: {
        user: {
          select: { 
            id: true,
            name: true, 
            role: true 
          },
        },
        likes: {
          select: { id: true, userId: true },
        },
        shares: {
          select: { id: true, userId: true },
        },
        engagements: {
          select: { platform: true, action: true, userId: true },
        },
        links: {
          select: { 
            id: true, 
            platform: true, 
            linkUrl: true,
            userId: true,
          },
        },
      },
    });

    // Transform videos to include platform information
    const transformedVideos = videos.map(video => ({
      ...transformVideoWithPlatforms(video),
      stats: {
        totalLikes: video.likes.length,
        totalShares: video.shares.length,
        totalLinks: video.links.length,
        platformEngagement: video.engagements.reduce((acc, eng) => {
          if (!acc[eng.platform]) acc[eng.platform] = { likes: 0, shares: 0 };
          if (eng.action === 'like') acc[eng.platform].likes++;
          if (eng.action === 'share') acc[eng.platform].shares++;
          return acc;
        }, {} as Record<string, { likes: number; shares: number }>),
      },
    }));

    const total = await prisma.video.count({
      where: whereClause,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      videos: transformedVideos,
      total,
      hasMore: offset + limit < total,
      pagination: {
        page,
        limit,
        offset,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        status,
        platform,
        featured,
        query,
      },
    });
  } catch (error) {
    console.error('Error fetching NWA videos:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/nwa-videos - Create a new NWA video with multi-platform support
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.role || !['ADMIN', 'NWA_TEAM'].includes(session.user.role)) {
    return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
  }

  try {
    const body: CreateVideoRequest = await request.json();
    const { title, description, platforms, thumbnailUrl, duration } = body;

    // Validation
    if (!title) {
      return NextResponse.json({ message: 'Title is required' }, { status: 400 });
    }

    if (!platforms || !hasValidPlatformUrl(platforms)) {
      return NextResponse.json({ 
        message: 'At least one platform URL is required' 
      }, { status: 400 });
    }

    // Validate platform URLs
    const urlErrors = validatePlatformUrls(platforms);
    if (urlErrors.length > 0) {
      return NextResponse.json({ 
        message: 'Invalid URL format', 
        errors: urlErrors 
      }, { status: 400 });
    }

    // Determine primary platform and URL for backward compatibility
    const { platform: primaryPlatform, url: primaryUrl } = getPrimaryPlatformInfo(platforms);

    const newVideo = await prisma.video.create({
      data: {
        title,
        description,
        url: primaryUrl, // For backward compatibility
        platform: primaryPlatform, // For backward compatibility
        youtubeUrl: platforms.youtubeUrl,
        tiktokUrl: platforms.tiktokUrl,
        rumbleUrl: platforms.rumbleUrl,
        thumbnailUrl,
        duration,
        status: 'published',
        userId: session.user.id,
      },
      include: {
        user: {
          select: { name: true, role: true },
        },
      },
    });

    // Transform response to include platforms object
    const response = transformVideoWithPlatforms(newVideo);

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error creating video:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
