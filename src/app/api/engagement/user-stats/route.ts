import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface UserEngagementStats {
  totalLikes: number;
  totalShares: number;
  totalVideosCompleted: number;
  leaderboardPosition: number;
  platformBreakdown: {
    [platform: string]: {
      likes: number;
      shares: number;
      completed: number;
    };
  };
}

// GET /api/engagement/user-stats - Get user engagement statistics
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const userId = session.user.id;
  const { searchParams } = new URL(request.url);
  const targetUserId = searchParams.get('userId') || userId;

  try {
    // Get user engagement data
    const engagements = await prisma.userEngagement.findMany({
      where: { userId: targetUserId },
      select: {
        platform: true,
        action: true,
      },
    });

    // Get completed videos count
    const completedVideosCount = await prisma.completedLink.count({
      where: { userId: targetUserId },
    });

    // Calculate totals
    const totalLikes = engagements.filter(e => e.action === 'like').length;
    const totalShares = engagements.filter(e => e.action === 'share').length;

    // Calculate platform breakdown
    const platformBreakdown: { [platform: string]: { likes: number; shares: number; completed: number } } = {};
    
    engagements.forEach(engagement => {
      if (!platformBreakdown[engagement.platform]) {
        platformBreakdown[engagement.platform] = { likes: 0, shares: 0, completed: 0 };
      }
      
      if (engagement.action === 'like') {
        platformBreakdown[engagement.platform].likes++;
      } else if (engagement.action === 'share') {
        platformBreakdown[engagement.platform].shares++;
      } else if (engagement.action === 'complete') {
        platformBreakdown[engagement.platform].completed++;
      }
    });

    // Calculate leaderboard position
    // Get user's total score (likes + shares)
    const userTotalScore = totalLikes + totalShares;
    
    // Count users with higher scores
    const usersWithHigherScores = await prisma.$queryRaw<{ count: bigint }[]>`
      SELECT COUNT(*) as count
      FROM (
        SELECT userId, COUNT(*) as total_engagements
        FROM UserEngagement
        GROUP BY userId
        HAVING total_engagements > ${userTotalScore}
      ) as higher_users
    `;
    
    const leaderboardPosition = Number(usersWithHigherScores[0]?.count || 0) + 1;

    const stats: UserEngagementStats = {
      totalLikes,
      totalShares,
      totalVideosCompleted: completedVideosCount,
      leaderboardPosition,
      platformBreakdown,
    };

    return NextResponse.json(stats, { status: 200 });
  } catch (error: unknown) {
    console.error('Error fetching user engagement stats:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}