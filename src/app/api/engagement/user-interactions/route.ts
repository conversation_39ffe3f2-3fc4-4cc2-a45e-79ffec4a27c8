import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';

interface UserInteractionsQuery {
  videoIds?: string;
}

// GET /api/engagement/user-interactions - Get user interactions for videos
export const GET = withApiMiddleware<unknown, UserInteractionsQuery>({
  ...middlewarePresets.authenticated(rateLimiters.read),
  queryValidation: {
    videoIds: { type: 'string' }, // Comma-separated video IDs
  },
})(async (request) => {
  const { user, validatedQuery } = request;
  const userId = user!.id;
  
  // Parse video IDs from query parameter
  const videoIdsParam = (validatedQuery as UserInteractionsQuery)?.videoIds;
  if (!videoIdsParam) {
    return NextResponse.json({ interactions: {} });
  }
  
  const videoIds = videoIdsParam.split(',').filter(id => id.trim());
  if (videoIds.length === 0) {
    return NextResponse.json({ interactions: {} });
  }

  try {
    // Get user engagements for the specified videos
    const engagements = await prisma.userEngagement.findMany({
      where: {
        userId,
        videoId: { in: videoIds },
      },
      select: {
        videoId: true,
        platform: true,
        action: true,
      },
    });

    // Transform engagements into the expected format
    const interactions: {
      [videoId: string]: {
        [platform: string]: {
          liked: boolean;
          shared: boolean;
        };
      };
    } = {};

    // Initialize interactions object for all requested videos
    videoIds.forEach(videoId => {
      interactions[videoId] = {};
    });

    // Populate interactions based on engagements
    engagements.forEach(engagement => {
      const { videoId, platform, action } = engagement;
      
      if (!interactions[videoId][platform]) {
        interactions[videoId][platform] = { liked: false, shared: false };
      }
      
      if (action === 'like') {
        interactions[videoId][platform].liked = true;
      } else if (action === 'share') {
        interactions[videoId][platform].shared = true;
      }
    });

    return NextResponse.json({ interactions });
  } catch (error) {
    console.error('Error fetching user interactions:', error);
    return NextResponse.json(
      { message: 'Failed to fetch user interactions' },
      { status: 500 }
    );
  }
});