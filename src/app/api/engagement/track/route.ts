import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { commonSchemas } from '@/lib/api-validation';
import { createApiError } from '@/lib/api-errors';
import { awardPoints, SCORING_CONFIG } from '@/lib/scoring-utils';
import { CacheInvalidation, safeInvalidateCache } from '@/lib/cache-invalidation';

interface TrackEngagementBody {
  videoId: string;
  platform: 'youtube' | 'tiktok' | 'rumble';
  action: 'like' | 'share' | 'complete';
}

// POST /api/engagement/track - Track platform-specific engagement
export const POST = withApiMiddleware<TrackEngagementBody>({
  ...middlewarePresets.authenticated(rateLimiters.general),
  bodyValidation: commonSchemas.trackEngagement,
})(async (request) => {
  const { user, validatedBody } = request;
  const { videoId, platform, action } = validatedBody as TrackEngagementBody;

  // Check if the video exists
  const video = await prisma.video.findUnique({ where: { id: videoId } });
  if (!video) {
    throw createApiError.notFound('Video');
  }

  const userId = user!.id;

  // Handle video completion separately
  if (action.toLowerCase() === 'complete') {
    // For now, we'll just track completion in the UserEngagement table
    // CompletedLink creation is complex due to schema constraints and requires proper Media mapping

    // Also track in engagement table
    const engagement = await prisma.userEngagement.upsert({
      where: {
        userId_videoId_platform_action: {
          userId,
          videoId,
          platform: platform.toLowerCase(),
          action: action.toLowerCase(),
        },
      },
      update: {
        createdAt: new Date(),
      },
      create: {
        userId,
        videoId,
        platform: platform.toLowerCase(),
        action: action.toLowerCase(),
      },
    });

    // Award points for completing video
    try {
      await awardPoints(
        userId,
        SCORING_CONFIG.COMPLETE_VIDEO,
        `Completed video: "${video.title}"`
      );
    } catch (error) {
      console.error('Error awarding points for video completion:', error);
      // Don't fail the engagement tracking if scoring fails
    }

    // Invalidate relevant caches for video completion
    await safeInvalidateCache(
      () => CacheInvalidation.onVideoCompletion(userId, videoId),
      'video completion engagement'
    );

    return NextResponse.json({ engagement }, { status: 201 });
  }

  // Create or update engagement record for like/share actions
  const engagement = await prisma.userEngagement.upsert({
    where: {
      userId_videoId_platform_action: {
        userId,
        videoId,
        platform: platform.toLowerCase(),
        action: action.toLowerCase(),
      },
    },
    update: {
      likedAt: action.toLowerCase() === 'like' ? new Date() : undefined,
    },
    create: {
      userId,
      videoId,
      platform: platform.toLowerCase(),
      action: action.toLowerCase(),
      likedAt: action.toLowerCase() === 'like' ? new Date() : undefined,
    },
  });

  // Award points for engagement actions
  try {
    const actionLower = action.toLowerCase();
    if (actionLower === 'like') {
      await awardPoints(
        userId,
        SCORING_CONFIG.LIKE_VIDEO,
        `Liked video: "${video.title}" on ${platform}`
      );
    } else if (actionLower === 'share') {
      await awardPoints(
        userId,
        SCORING_CONFIG.SHARE_VIDEO,
        `Shared video: "${video.title}" on ${platform}`
      );
    }
  } catch (error) {
    console.error('Error awarding points for engagement:', error);
    // Don't fail the engagement tracking if scoring fails
  }

  // Invalidate relevant caches for engagement actions
  await safeInvalidateCache(
    () => CacheInvalidation.onEngagementChange(userId, videoId),
    'engagement action'
  );

  return NextResponse.json(engagement, { status: 201 });
});