import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { awardPoints } from '@/lib/scoring-utils';
import { checkForNewAchievements, ACHIEVEMENTS } from '@/lib/achievements';

interface ConfirmEngagementBody {
  videoId: string;
  platform: string;
  action: 'like' | 'share' | 'complete' | 'engage';
  confirmed: 'yes' | 'no' | 'skip';
  timestamp: string;
  bonusPoints?: number;
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body: ConfirmEngagementBody = await request.json();
    const { videoId, platform, action, confirmed } = body;
    const userId = session.user.id;

    // Validate input
    if (!videoId || !platform || !action || !confirmed) {
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 });
    }

    // Check if the video exists
    const video = await prisma.video.findUnique({ 
      where: { id: videoId },
      select: { id: true, title: true }
    });
    
    if (!video) {
      return NextResponse.json({ message: 'Video not found' }, { status: 404 });
    }

    const now = new Date();
    let bonusPoints = 0;
    const newAchievements: string[] = [];

    // Handle confirmation response
    if (confirmed === 'yes') {
      // Award bonus points for confirmed engagement
      bonusPoints = body.bonusPoints || (action === 'share' ? 3 : action === 'like' ? 2 : 1); // Use provided bonus or default
      
      try {
        await awardPoints(
          userId,
          bonusPoints,
          `Confirmed ${action} on ${platform}: "${video.title}"`
        );
      } catch (error) {
        console.error('Error awarding bonus points:', error);
      }

      // Update the engagement record with confirmation
      try {
        await prisma.userEngagement.updateMany({
          where: {
            userId,
            videoId,
            platform: platform.toLowerCase(),
            action: action.toLowerCase(),
          },
          data: {
            confirmed: true,
            confirmedAt: now,
          },
        });
      } catch (error) {
        console.error('Error updating engagement confirmation:', error);
      }

      // Update user's engagement streak
      await updateEngagementStreak(userId);

    } else if (confirmed === 'no') {
      // User says they didn't engage yet - no bonus, but track the response
      await prisma.userEngagement.updateMany({
        where: {
          userId,
          videoId,
          platform: platform.toLowerCase(),
          action: action.toLowerCase(),
        },
        data: {
          confirmed: false,
          confirmedAt: now,
        },
      });
    }
    // Skip = no action taken

    // Check for new achievements
    try {
      const userStats = await getUserEngagementStats(userId);
      if (userStats) {
        // Get current achievements
        const currentAchievements = await prisma.userAchievement.findMany({
          where: { userId },
          select: { achievementId: true }
        });
        
        const currentAchievementIds = currentAchievements.map(a => a.achievementId);
        
        // Check for new achievements
        const newAchievementsList = checkForNewAchievements(currentAchievementIds, userStats);
        
        // Award new achievements
        for (const achievement of newAchievementsList) {
          await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id,
              earnedAt: now,
            }
          });
          
          // Award achievement points
          await awardPoints(
            userId,
            achievement.points,
            `Achievement unlocked: ${achievement.title}`
          );
          
          newAchievements.push(achievement.id);
        }
      }
    } catch (error) {
      console.error('Error checking for new achievements:', error);
    }

    return NextResponse.json({
      success: true,
      confirmed,
      bonusPoints,
      newAchievements: newAchievements.map(id => ACHIEVEMENTS[id]).filter(Boolean),
      message: getConfirmationMessage(confirmed, action, bonusPoints)
    });

  } catch (error) {
    console.error('Error handling engagement confirmation:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update user's engagement streak
async function updateEngagementStreak(userId: string): Promise<void> {
  try {
    // Get the user's last engagement date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Check if user had confirmed engagement today
    const todayEngagement = await prisma.userEngagement.findFirst({
      where: {
        userId,
        confirmed: true,
        confirmedAt: { gte: today }
      }
    });

    // Check if user had confirmed engagement yesterday
    const yesterdayEngagement = await prisma.userEngagement.findFirst({
      where: {
        userId,
        confirmed: true,
        confirmedAt: { 
          gte: yesterday,
          lt: today
        }
      }
    });

    // Get or create user streak record
    let userStreak = await prisma.userEngagementStreak.findUnique({
      where: { userId }
    });

    if (!userStreak) {
      userStreak = await prisma.userEngagementStreak.create({
        data: {
          userId,
          currentStreak: 1,
          longestStreak: 1,
          lastEngagementDate: today
        }
      });
    } else if (todayEngagement) {
      // Update streak
      const newStreak = yesterdayEngagement ? userStreak.currentStreak + 1 : 1;
      
      await prisma.userEngagementStreak.update({
        where: { userId },
        data: {
          currentStreak: newStreak,
          longestStreak: Math.max(newStreak, userStreak.longestStreak),
          lastEngagementDate: today
        }
      });
    }

  } catch (error) {
    console.error('Error updating engagement streak:', error);
  }
}

// Get user engagement stats for achievement checking
async function getUserEngagementStats(userId: string) {
  try {
    const engagements = await prisma.userEngagement.findMany({
      where: { userId },
      select: { platform: true, action: true, confirmed: true }
    });

    const totalLikes = engagements.filter(e => e.action === 'like').length;
    const totalShares = engagements.filter(e => e.action === 'share').length;
    const totalCompletions = engagements.filter(e => e.action === 'complete').length;
    const totalConfirmed = engagements.filter(e => e.confirmed).length;

    // Get current streak
    const userStreak = await prisma.userEngagementStreak.findUnique({
      where: { userId },
      select: { currentStreak: true }
    });

    // Calculate platform breakdown
    const platformStats: Record<string, { likes: number; shares: number; completions: number }> = {};
    const platformsUsed: string[] = [];

    engagements.forEach(engagement => {
      if (!platformStats[engagement.platform]) {
        platformStats[engagement.platform] = { likes: 0, shares: 0, completions: 0 };
        platformsUsed.push(engagement.platform);
      }
      
      if (engagement.action === 'like') {
        platformStats[engagement.platform].likes++;
      } else if (engagement.action === 'share') {
        platformStats[engagement.platform].shares++;
      } else if (engagement.action === 'complete') {
        platformStats[engagement.platform].completions++;
      }
    });

    return {
      totalLikes,
      totalShares,
      totalCompletions,
      totalConfirmed,
      currentStreak: userStreak?.currentStreak || 0,
      platformsUsed,
      platformStats
    };

  } catch (error) {
    console.error('Error getting user engagement stats:', error);
    return null;
  }
}

function getConfirmationMessage(confirmed: string, action: string, bonusPoints: number): string {
  switch (confirmed) {
    case 'yes':
      return `Thanks for confirming! You earned ${bonusPoints} bonus points for your honest ${action}.`;
    case 'no':
      return `No worries! You can always engage with the content later.`;
    case 'skip':
      return `Confirmation skipped. Your original engagement points still count!`;
    default:
      return 'Confirmation recorded.';
  }
}
