import { NextRequest, NextResponse } from 'next/server';
import { CSRFProtection } from '@/lib/security-headers';
import { cookies } from 'next/headers';

export async function GET(_request: NextRequest) {
  try {
    // Generate a new CSRF token
    const csrfToken = CSRFProtection.generateToken();

    // Set the token in an httpOnly cookie
    const cookieStore = await cookies();
    cookieStore.set('csrf-token', csrfToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60, // 1 hour
      path: '/',
    });

    // Return the token to the frontend (but not in cookie for JavaScript access)
    return NextResponse.json({
      csrfToken,
      success: true
    });

  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}

export async function POST(_request: NextRequest) {
  // Allow POST for token refresh
  return GET(_request);
}