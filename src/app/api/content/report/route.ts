import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { reportContent } from '@/lib/content-moderation';
import { z } from 'zod';

const reportSchema = z.object({
  videoId: z.string().optional(),
  videoLinkId: z.string().optional(),
  reason: z.enum(['inappropriate', 'spam', 'copyright', 'other']),
  description: z.string().optional(),
}).refine(
  (data) => data.videoId || data.videoLinkId,
  {
    message: "Either videoId or videoLinkId must be provided",
    path: ["videoId"],
  }
);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = reportSchema.parse(body);

    const report = await reportContent(
      session.user.id,
      validatedData.videoId || null,
      validatedData.videoLinkId || null,
      validatedData.reason,
      validatedData.description
    );

    return NextResponse.json({
      success: true,
      reportId: report.id,
      message: 'Report submitted successfully. Our moderation team will review it shortly.',
    });

  } catch (error) {
    console.error('Error submitting content report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to submit report' },
      { status: 500 }
    );
  }
}

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's submitted reports
    const { prisma } = await import('@/lib/prisma');
    
    const reports = await prisma.contentReport.findMany({
      where: {
        reporterId: session.user.id,
      },
      include: {
        video: {
          select: { id: true, title: true },
        },
        videoLink: {
          select: { id: true, linkUrl: true, platform: true },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 50,
    });

    return NextResponse.json({ reports });

  } catch (error) {
    console.error('Error fetching user reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}