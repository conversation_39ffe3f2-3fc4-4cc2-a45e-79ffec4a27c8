import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withApiMiddleware, middlewarePresets } from '@/lib/api-middleware';
import { rateLimiters } from '@/lib/rate-limit';
import { commonSchemas } from '@/lib/api-validation';
import { createApiError } from '@/lib/api-errors';

interface SendNotificationBody {
  videoIds: string[];
  type?: 'nwa' | 'user';
}

export const dynamic = 'force-dynamic';

// POST /api/notifications/send-video - Send bulk video notifications
export const POST = withApiMiddleware<SendNotificationBody>({
  ...middlewarePresets.adminOnly(rateLimiters.notifications),
  bodyValidation: commonSchemas.sendNotification,
})(async (request) => {
  const { user, validatedBody } = request;
  const { videoIds, type = 'nwa' } = validatedBody as SendNotificationBody;

  // Fetch videos to validate they exist
  const videos = await prisma.video.findMany({
    where: {
      id: { in: videoIds }
    },
    select: {
      id: true,
      title: true,
      notificationSentAt: true
    }
  });

  if (videos.length !== videoIds.length) {
    const foundIds = videos.map(v => v.id);
    const missingIds = videoIds.filter((id: string) => !foundIds.includes(id));
    throw createApiError.notFound('Videos', `Videos not found: ${missingIds.join(', ')}`);
  }

  // Check which videos already have notifications sent
  const videosWithNotifications = videos.filter(v => v.notificationSentAt);
  const videosToNotify = videos.filter(v => !v.notificationSentAt);

  if (videosToNotify.length === 0) {
    return NextResponse.json({
      message: 'All videos already have notifications sent',
      skippedVideos: videosWithNotifications.map(v => ({ id: v.id, title: v.title }))
    }, { status: 400 });
  }

  // Get eligible users based on notification type and preferences
  const userWhereClause = type === 'nwa' 
    ? { nwaVideoNotifications: true }
    : { userVideoNotifications: true };

  const eligibleUsers = await prisma.user.findMany({
    where: userWhereClause,
    select: { id: true }
  });

  if (eligibleUsers.length === 0) {
    return NextResponse.json({
      message: 'No users have opted in for this notification type'
    }, { status: 200 });
  }

  const results = [];

  // Create notifications for each video
  for (const video of videosToNotify) {
    const notification = await prisma.notification.create({
      data: {
        title: type === 'nwa' ? 'New NWA Video!' : 'New User Video!',
        message: `Check out the new video: "${video.title}"`,
        type: type === 'nwa' ? 'VIDEO_RELEASE' : 'USER_VIDEO',
        sentBy: user!.id,
        videoId: video.id,
        isSent: true,
        sentAt: new Date(),
        recipients: {
          create: eligibleUsers.map(user => ({
            userId: user.id,
          })),
        },
      },
    });

    // Update video to mark notification as sent
    await prisma.video.update({
      where: { id: video.id },
      data: { notificationSentAt: new Date() },
    });

    results.push({
      videoId: video.id,
      videoTitle: video.title,
      notificationId: notification.id,
      recipientCount: eligibleUsers.length
    });
  }

  return NextResponse.json({
    message: `Notifications sent successfully for ${results.length} video(s)`,
    results,
    skippedVideos: videosWithNotifications.map(v => ({ id: v.id, title: v.title, reason: 'Already notified' }))
  });
});