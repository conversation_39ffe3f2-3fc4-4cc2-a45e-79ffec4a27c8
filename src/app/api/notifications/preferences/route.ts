import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export const dynamic = 'force-dynamic';

// GET /api/notifications/preferences - Get user notification preferences
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        nwaVideoNotifications: true,
        userVideoNotifications: true,
        pushSubscription: true
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({
      nwaVideos: user.nwaVideoNotifications,
      userVideos: user.userVideoNotifications,
      pushNotifications: !!user.pushSubscription
    });

  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/notifications/preferences - Update user notification preferences (new format)
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { nwaVideos, userVideos, pushNotifications } = body;

    // Validate input
    if (typeof nwaVideos !== 'boolean' && nwaVideos !== undefined) {
      return NextResponse.json(
        { error: 'nwaVideos must be a boolean' },
        { status: 400 }
      );
    }

    if (typeof userVideos !== 'boolean' && userVideos !== undefined) {
      return NextResponse.json(
        { error: 'userVideos must be a boolean' },
        { status: 400 }
      );
    }

    if (typeof pushNotifications !== 'boolean' && pushNotifications !== undefined) {
      return NextResponse.json(
        { error: 'pushNotifications must be a boolean' },
        { status: 400 }
      );
    }

    // Build update data object with only provided fields
    const updateData: { 
      nwaVideoNotifications?: boolean; 
      userVideoNotifications?: boolean;
    } = {};
    
    if (nwaVideos !== undefined) {
      updateData.nwaVideoNotifications = nwaVideos;
    }
    
    if (userVideos !== undefined) {
      updateData.userVideoNotifications = userVideos;
    }

    if (Object.keys(updateData).length === 0 && pushNotifications === undefined) {
      return NextResponse.json(
        { error: 'At least one preference field must be provided' },
        { status: 400 }
      );
    }

    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: updateData,
      select: {
        nwaVideoNotifications: true,
        userVideoNotifications: true,
        pushSubscription: true
      }
    });

    return NextResponse.json({
      message: 'Notification preferences updated successfully',
      preferences: {
        nwaVideos: updatedUser.nwaVideoNotifications,
        userVideos: updatedUser.userVideoNotifications,
        pushNotifications: !!updatedUser.pushSubscription
      }
    });

  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/notifications/preferences - Update user notification preferences (legacy format)
export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { nwaVideoNotifications, userVideoNotifications } = body;

    // Validate input
    if (typeof nwaVideoNotifications !== 'boolean' && nwaVideoNotifications !== undefined) {
      return NextResponse.json(
        { error: 'nwaVideoNotifications must be a boolean' },
        { status: 400 }
      );
    }

    if (typeof userVideoNotifications !== 'boolean' && userVideoNotifications !== undefined) {
      return NextResponse.json(
        { error: 'userVideoNotifications must be a boolean' },
        { status: 400 }
      );
    }

    // Build update data object with only provided fields
    const updateData: { nwaVideoNotifications?: boolean; userVideoNotifications?: boolean } = {};
    
    if (nwaVideoNotifications !== undefined) {
      updateData.nwaVideoNotifications = nwaVideoNotifications;
    }
    
    if (userVideoNotifications !== undefined) {
      updateData.userVideoNotifications = userVideoNotifications;
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'At least one preference field must be provided' },
        { status: 400 }
      );
    }

    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: updateData,
      select: {
        nwaVideoNotifications: true,
        userVideoNotifications: true
      }
    });

    return NextResponse.json({
      message: 'Notification preferences updated successfully',
      preferences: {
        nwaVideoNotifications: updatedUser.nwaVideoNotifications,
        userVideoNotifications: updatedUser.userVideoNotifications
      }
    });

  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}