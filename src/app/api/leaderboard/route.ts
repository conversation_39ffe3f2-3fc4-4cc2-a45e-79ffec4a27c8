import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cacheService } from '@/lib/cache-service';
import { UserQueryOptimizer } from '@/lib/query-optimization';
import { monitoredQueries } from '@/lib/query-performance-monitor';

export async function GET() {
  try {
    // Try to get from cache first
    const cachedLeaderboard = await cacheService.getLeaderboard();
    if (cachedLeaderboard) {
      return NextResponse.json(cachedLeaderboard);
    }

    // Use optimized leaderboard query with performance monitoring
    const leaderboard = await monitoredQueries.getLeaderboard(
      async () => {
        // Check if we should use LeaderboardEntry table or calculate from User table
        const leaderboardEntryCount = await prisma.leaderboardEntry.count();
        
        if (leaderboardEntryCount > 0) {
          // Use existing leaderboard entries
          return await prisma.leaderboardEntry.findMany({
            take: 50,
            orderBy: [{ rank: 'asc' }],
          });
        } else {
          // Calculate leaderboard from User table using optimized query
          const users = await UserQueryOptimizer.getLeaderboardOptimized(50);
          
          // Transform to leaderboard format with ranks
          return users.map((user, index) => ({
            id: user.id,
            name: user.name || 'Anonymous',
            points: user.totalPoints,
            level: user.level,
            rank: index + 1,
            isCurrentUser: false, // This would need session context to determine
          }));
        }
      }
    );

    const response = { leaderboard };

    // Cache the result
    await cacheService.setLeaderboard(response);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Leaderboard query error:', error);
    return NextResponse.json({ error: 'Failed to fetch leaderboard' }, { status: 500 });
  }
}
