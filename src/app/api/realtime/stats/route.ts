import { NextRequest } from 'next/server';
import getServerSession from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { cacheService } from '@/lib/cache-service';

export const dynamic = 'force-dynamic';

interface StatsData {
  totalVideos: number;
  totalUsers: number;
  totalViews: number;
  nwaVideosCount: number;
  userVideoLinksCount: number;
  timestamp: string;
}

interface CachedDashboardStats {
  totalVideos: number;
  totalUsers: number;
  totalViews: number;
}

interface UserStatsData {
  points: number;
  level: number;
  totalPoints: number;
  leaderboardPosition: number;
  timestamp: string;
}

// GET /api/realtime/stats - Server-Sent Events endpoint for real-time statistics
export async function GET(request: NextRequest) {
  // Server-side kill switch to stop realtime entirely (prevents runaway connections)
  if (process.env.SERVER_DISABLE_REALTIME === 'true') {
    return new Response('Realtime disabled', {
      status: 204,
      headers: { 'Cache-Control': 'no-store' },
    });
  }

  const session = await getServerSession(authOptions);
  
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Set up SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  const encoder = new TextEncoder();
  let isConnected = true;

  const stream = new ReadableStream({
    start(controller) {
      // Function to send data to client
      const sendData = (data: StatsData | UserStatsData | { error: string } | { type: string; timestamp: string }, event?: string) => {
        if (!isConnected) return;
        
        const eventName = event ? `event: ${event}\n` : '';
        const message = `${eventName}data: ${JSON.stringify(data)}\n\n`;
        
        try {
          controller.enqueue(encoder.encode(message));
        } catch (error) {
          console.error('Error sending SSE data:', error);
          isConnected = false;
        }
      };

      // Function to fetch and send statistics
      const fetchAndSendStats = async () => {
        if (!isConnected) return;

        try {
          // Try to get cached dashboard statistics first
          let dashboardStats = await cacheService.getDashboardStats() as CachedDashboardStats | null;
          if (!dashboardStats) {
            // If not cached, fetch from database
            const [totalVideos, totalUsers, totalViews] = await Promise.all([
              prisma.video.count(),
              prisma.user.count(),
              prisma.video.aggregate({
                _sum: { views: true }
              }).then(result => result._sum.views || 0)
            ]);

            dashboardStats = { totalVideos, totalUsers, totalViews };
            // Cache the statistics
            await cacheService.setDashboardStats(dashboardStats);
          }

          // Count NWA videos (excluding completed ones for current user)
          const nwaVideosCount = await prisma.video.count({
            where: {
              user: {
                role: { in: ['ADMIN', 'NWA_TEAM'] },
              },
              completions: {
                none: {
                  userId: session.user?.id,
                },
              },
            },
          });

          // Count user video links
          const userVideoLinksCount = await prisma.videoLink.count();

          const statsData: StatsData = {
            totalVideos: dashboardStats?.totalVideos || 0,
            totalUsers: dashboardStats?.totalUsers || 0,
            totalViews: dashboardStats?.totalViews || 0,
            nwaVideosCount,
            userVideoLinksCount,
            timestamp: new Date().toISOString(),
          };

          sendData(statsData, 'stats');

          // Try to get cached user stats first
          let userStats = await cacheService.getUserStats(session.user?.id) as UserStatsData | null;
          
          if (!userStats) {
            // Fetch user-specific statistics
            const user = await prisma.user.findUnique({
              where: { id: session.user?.id },
              select: {
                score: true,
                level: true,
                totalPoints: true,
              }
            });

            if (user) {
              // Get user's leaderboard position
              let leaderboardPosition = 1;
              try {
                const leaderboardResult = await prisma.$queryRaw<Array<{ position: bigint }>>`
                  SELECT COUNT(*) + 1 as position
                  FROM User
                  WHERE totalPoints > (SELECT totalPoints FROM User WHERE id = ${session.user?.id})
                `;
                
                leaderboardPosition = leaderboardResult && leaderboardResult[0] 
                  ? Number(leaderboardResult[0].position) 
                  : 1;
              } catch (error) {
                console.error('Error calculating leaderboard position:', error);
              }

              userStats = {
                points: user.score || 0,
                level: user.level || 1,
                totalPoints: user.totalPoints || 0,
                leaderboardPosition,
                timestamp: new Date().toISOString(), // Add timestamp here
              };

              // Cache user stats
              await cacheService.setUserStats(session.user?.id, userStats);
            }
          }

          if (userStats) {
            const userStatsData: UserStatsData = {
              points: userStats.points || 0,
              level: userStats.level || 0,
              totalPoints: userStats.totalPoints || 0,
              leaderboardPosition: userStats.leaderboardPosition || 0,
              timestamp: userStats.timestamp || new Date().toISOString(),
            };

            sendData(userStatsData, 'userStats');
          }

        } catch (error) {
          console.error('Error fetching stats for SSE:', error);
          sendData({ error: 'Failed to fetch statistics' }, 'error');
        }
      };

      // Send initial data
      fetchAndSendStats();

      // Set up interval to send updates every 10 seconds
      const interval = setInterval(fetchAndSendStats, 10000);

      // Handle client disconnect
      request.signal.addEventListener('abort', () => {
        isConnected = false;
        clearInterval(interval);
        try {
          controller.close();
        } catch (error) {
          console.error('Error closing SSE controller:', error);
        }
      });

      // Send heartbeat every 30 seconds to keep connection alive
      const heartbeatInterval = setInterval(() => {
        if (isConnected) {
          sendData({ type: 'heartbeat', timestamp: new Date().toISOString() }, 'heartbeat');
        } else {
          clearInterval(heartbeatInterval);
        }
      }, 30000);
    },
  });

  return new Response(stream, { headers });
}