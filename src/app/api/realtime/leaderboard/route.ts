import { NextRequest } from 'next/server';
import getServerSession from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { cacheService } from '@/lib/cache-service';

export const dynamic = 'force-dynamic';

interface LeaderboardEntry {
  id: string;
  name: string;
  points: number;
  level: number;
  rank: number;
  isCurrentUser: boolean;
}

interface LeaderboardData {
  leaderboard: LeaderboardEntry[];
  timestamp: string;
}

// GET /api/realtime/leaderboard - Server-Sent Events endpoint for real-time leaderboard
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Set up SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  const encoder = new TextEncoder();
  let isConnected = true;

  const stream = new ReadableStream({
    start(controller) {
      // Function to send data to client
      const sendData = (data: LeaderboardData | { error: string } | { type: string; timestamp: string }, event?: string) => {
        if (!isConnected) return;
        
        const eventName = event ? `event: ${event}\n` : '';
        const message = `${eventName}data: ${JSON.stringify(data)}\n\n`;
        
        try {
          controller.enqueue(encoder.encode(message));
        } catch (error) {
          console.error('Error sending SSE data:', error);
          isConnected = false;
        }
      };

      // Function to fetch and send leaderboard
      const fetchAndSendLeaderboard = async () => {
        if (!isConnected) return;

        try {
          // Try to get cached leaderboard first
          const cachedLeaderboard = await cacheService.getLeaderboard() as LeaderboardData | null;
          let leaderboard;

          if (cachedLeaderboard && cachedLeaderboard.leaderboard) {
            leaderboard = cachedLeaderboard.leaderboard;
          } else {
            // If not cached, fetch from database
            leaderboard = await prisma.leaderboardEntry.findMany({
              take: 50,
              orderBy: [
                { rank: 'asc' },
              ],
            });

            // Cache the result
            await cacheService.setLeaderboard({ leaderboard });
          }

          // Map to include current user flag
          const leaderboardWithCurrentUser: LeaderboardEntry[] = leaderboard.map((entry: LeaderboardEntry) => ({
            id: entry.id,
            name: entry.name || 'Anonymous',
            points: entry.points,
            level: entry.level,
            rank: entry.rank,
            isCurrentUser: entry.id === session.user?.id,
          }));

          const leaderboardData: LeaderboardData = {
            leaderboard: leaderboardWithCurrentUser,
            timestamp: new Date().toISOString(),
          };

          sendData(leaderboardData, 'leaderboard');

        } catch (error) {
          console.error('Error fetching leaderboard for SSE:', error);
          sendData({ error: 'Failed to fetch leaderboard' }, 'error');
        }
      };

      // Send initial data
      fetchAndSendLeaderboard();

      // Set up interval to send updates every 15 seconds
      const interval = setInterval(fetchAndSendLeaderboard, 15000);

      // Handle client disconnect
      request.signal.addEventListener('abort', () => {
        isConnected = false;
        clearInterval(interval);
        try {
          controller.close();
        } catch (error) {
          console.error('Error closing SSE controller:', error);
        }
      });

      // Send heartbeat every 30 seconds to keep connection alive
      const heartbeatInterval = setInterval(() => {
        if (isConnected) {
          sendData({ type: 'heartbeat', timestamp: new Date().toISOString() }, 'heartbeat');
        } else {
          clearInterval(heartbeatInterval);
        }
      }, 30000);
    },
  });

  return new Response(stream, { headers });
}