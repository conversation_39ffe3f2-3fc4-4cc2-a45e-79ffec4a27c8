import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateLocaleSchema = z.object({
  locale: z.enum(['en', 'es', 'fr', 'de', 'pt', 'zh', 'ja', 'ko'])
});

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's preferred locale from database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { locale: true }
    });

    return NextResponse.json({
      locale: user?.locale || 'en'
    });

  } catch (error) {
    console.error('Error fetching user locale:', error);
    return NextResponse.json(
      { error: 'Failed to fetch locale preference' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { locale } = updateLocaleSchema.parse(body);

    // Update user's locale preference
    await prisma.user.update({
      where: { id: session.user.id },
      data: { locale }
    });

    return NextResponse.json({
      success: true,
      locale
    });

  } catch (error) {
    console.error('Error updating user locale:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid locale', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update locale preference' },
      { status: 500 }
    );
  }
}