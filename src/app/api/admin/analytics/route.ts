import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { cacheService } from '@/lib/cache-service';

interface PlatformMetrics {
  platform: string;
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  totalEngagements: number;
  uniqueUsers: number;
}

interface UserBehaviorMetrics {
  totalVideos: number;
  totalCompletions: number;
  totalSkips: number;
  completionRate: number;
  skipRate: number;
  averageEngagementsPerVideo: number;
}

interface EngagementTrend {
  date: string;
  likes: number;
  shares: number;
  completions: number;
}

interface AnalyticsData {
  platformMetrics: PlatformMetrics[];
  userBehavior: UserBehaviorMetrics;
  engagementTrends: EngagementTrend[];
  topVideos: Array<{
    id: string;
    title: string;
    totalEngagements: number;
    likes: number;
    shares: number;
    completions: number;
  }>;
  totalUsers: number;
  activeUsers: number;
}

// GET /api/admin/analytics - Get comprehensive engagement analytics
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is admin
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'ADMIN') {
    return NextResponse.json({ message: 'Forbidden - Admin access required' }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const days = parseInt(searchParams.get('days') || '30');

  try {
    // Try to get cached analytics data first
    const cachedAnalytics = await cacheService.getAnalytics(days);
    if (cachedAnalytics) {
      return NextResponse.json(cachedAnalytics, { status: 200 });
    }

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get platform metrics
    const platformEngagements = await prisma.userEngagement.groupBy({
      by: ['platform', 'action'],
      _count: {
        id: true,
      },
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Get unique users per platform
    const platformUsers = await prisma.userEngagement.groupBy({
      by: ['platform'],
      _count: {
        userId: true,
      },
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Process platform metrics
    const platformMetricsMap = new Map<string, PlatformMetrics>();
    
    platformEngagements.forEach(({ platform, action, _count }) => {
      if (!platformMetricsMap.has(platform)) {
        platformMetricsMap.set(platform, {
          platform,
          totalLikes: 0,
          totalShares: 0,
          totalCompletions: 0,
          totalEngagements: 0,
          uniqueUsers: 0,
        });
      }
      
      const metrics = platformMetricsMap.get(platform)!;
      metrics.totalEngagements += _count.id;
      
      if (action === 'like') {
        metrics.totalLikes += _count.id;
      } else if (action === 'share') {
        metrics.totalShares += _count.id;
      } else if (action === 'complete') {
        metrics.totalCompletions += _count.id;
      }
    });

    // Add unique users count
    platformUsers.forEach(({ platform, _count }) => {
      const metrics = platformMetricsMap.get(platform);
      if (metrics) {
        metrics.uniqueUsers = _count.userId;
      }
    });

    const platformMetrics = Array.from(platformMetricsMap.values());

    // Get user behavior metrics
    const totalVideos = await prisma.video.count({
      where: {
        status: 'published',
      },
    });

    const totalCompletions = await prisma.completedLink.count({
      where: {
        completedAt: {
          gte: startDate,
        },
      },
    });

    const totalEngagements = await prisma.userEngagement.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Calculate user behavior metrics
    const totalSkips = Math.max(0, totalEngagements - totalCompletions);
    const completionRate = totalVideos > 0 ? (totalCompletions / totalVideos) * 100 : 0;
    const skipRate = totalVideos > 0 ? (totalSkips / totalVideos) * 100 : 0;
    const averageEngagementsPerVideo = totalVideos > 0 ? totalEngagements / totalVideos : 0;

    const userBehavior: UserBehaviorMetrics = {
      totalVideos,
      totalCompletions,
      totalSkips,
      completionRate,
      skipRate,
      averageEngagementsPerVideo,
    };

    // Get engagement trends (daily data for the past period)
    const engagementTrendsRaw = await prisma.$queryRaw<Array<{
      date: string;
      action: string;
      count: number;
    }>>`
      SELECT 
        DATE(createdAt) as date,
        action,
        COUNT(*) as count
      FROM UserEngagement 
      WHERE createdAt >= ${startDate}
      GROUP BY DATE(createdAt), action
      ORDER BY date ASC
    `;

    // Process engagement trends
    const trendsMap = new Map<string, EngagementTrend>();
    
    engagementTrendsRaw.forEach(({ date, action, count }) => {
      const dateStr = new Date(date).toISOString().split('T')[0];
      
      if (!trendsMap.has(dateStr)) {
        trendsMap.set(dateStr, {
          date: dateStr,
          likes: 0,
          shares: 0,
          completions: 0,
        });
      }
      
      const trend = trendsMap.get(dateStr)!;
      if (action === 'like') {
        trend.likes = Number(count);
      } else if (action === 'share') {
        trend.shares = Number(count);
      } else if (action === 'complete') {
        trend.completions = Number(count);
      }
    });

    const engagementTrends = Array.from(trendsMap.values()).sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Get top videos by engagement
    const topVideosRaw = await prisma.$queryRaw<Array<{
      id: string;
      title: string;
      total_engagements: number;
      likes: number;
      shares: number;
      completions: number;
    }>>`
      SELECT 
        v.id,
        v.title,
        COUNT(ue.id) as total_engagements,
        SUM(CASE WHEN ue.action = 'like' THEN 1 ELSE 0 END) as likes,
        SUM(CASE WHEN ue.action = 'share' THEN 1 ELSE 0 END) as shares,
        SUM(CASE WHEN ue.action = 'complete' THEN 1 ELSE 0 END) as completions
      FROM Video v
      LEFT JOIN UserEngagement ue ON v.id = ue.videoId
      WHERE ue.createdAt >= ${startDate} OR ue.createdAt IS NULL
      GROUP BY v.id, v.title
      ORDER BY total_engagements DESC
      LIMIT 10
    `;

    const topVideos = topVideosRaw.map(video => ({
      id: video.id,
      title: video.title,
      totalEngagements: Number(video.total_engagements),
      likes: Number(video.likes),
      shares: Number(video.shares),
      completions: Number(video.completions),
    }));

    // Get user counts
    const totalUsers = await prisma.user.count();
    const activeUsers = await prisma.user.count({
      where: {
        lastActivityAt: {
          gte: startDate,
        },
      },
    });

    const analyticsData: AnalyticsData = {
      platformMetrics,
      userBehavior,
      engagementTrends,
      topVideos,
      totalUsers,
      activeUsers,
    };

    // Cache the analytics data
    await cacheService.setAnalytics(days, analyticsData);

    return NextResponse.json(analyticsData, { status: 200 });
  } catch (error: unknown) {
    console.error('Error fetching analytics data:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}