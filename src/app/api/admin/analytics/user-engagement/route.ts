import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Role } from '@/generated/prisma/client';

interface VideoLinkEngagement {
  videoLinkId: string;
  title: string;
  url: string;
  platform: string;
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  totalEngagements: number;
  uniqueUsers: number;
  topEngagers: {
    userId: string;
    userName: string | null;
    userEmail: string;
    engagements: number;
    likes: number;
    shares: number;
  }[];
}

interface UserEngagementSummary {
  userId: string;
  userName: string | null;
  userEmail: string;
  userRole: Role;
  totalVideoLinksEngaged: number;
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  engagementScore: number;
  favoriteVideos: {
    videoLinkId: string;
    title: string;
    engagements: number;
  }[];
  favoritePlatforms: {
    platform: string;
    engagements: number;
  }[];
  lastEngagementDate: Date | null;
  engagementFrequency: number; // engagements per day since registration
}

interface UserEngagementResponse {
  videoLinkPerformance: VideoLinkEngagement[];
  userEngagementSummary: UserEngagementSummary[];
  platformInsights: {
    [platform: string]: {
      totalEngagements: number;
      totalLikes: number;
      totalShares: number;
      totalCompletions: number;
      uniqueUsers: number;
      topVideoLinks: {
        videoLinkId: string;
        title: string;
        engagements: number;
      }[];
    };
  };
  timeBasedInsights: {
    [date: string]: {
      totalEngagements: number;
      uniqueUsers: number;
      newEngagers: number;
    };
  };
  summary: {
    totalVideoLinks: number;
    totalEngagements: number;
    totalUniqueEngagers: number;
    averageEngagementsPerVideoLink: number;
    averageEngagementsPerUser: number;
    topPlatform: string;
    mostEngagedVideoLink: string;
  };
}

// GET /api/admin/analytics/user-engagement - Get comprehensive user engagement report
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session.user?.role !== Role.ADMIN) {
    return NextResponse.json({ message: 'Forbidden - Admin access required' }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const days = parseInt(searchParams.get('days') || '30');
  const limit = parseInt(searchParams.get('limit') || '20');

  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get all video links with their engagements
    const videoLinksWithEngagements = await prisma.videoLink.findMany({
      select: {
        id: true,
        linkUrl: true,
        platform: true,
        Video: {
          select: {
            title: true,
          },
        },
        likes: {
          where: { createdAt: { gte: startDate } },
          select: {
            userId: true,
            user: { select: { name: true, email: true } },
            createdAt: true,
          },
        },
        shares: {
          where: { createdAt: { gte: startDate } },
          select: {
            userId: true,
            user: { select: { name: true, email: true } },
            createdAt: true,
          },
        },
        _count: {
          select: {
            likes: { where: { createdAt: { gte: startDate } } },
            shares: { where: { createdAt: { gte: startDate } } },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Process video link engagement data
    const videoLinkPerformance: VideoLinkEngagement[] = videoLinksWithEngagements.map(videoLink => {
      const totalLikes = videoLink.likes.length;
      const totalShares = videoLink.shares.length;
      const totalCompletions = 0; // VideoLinks don't have direct completions
      const totalEngagements = totalLikes + totalShares;

      // Get unique users
      const allEngagers = [
        ...videoLink.likes.map(l => ({ userId: l.userId, userName: l.user.name, userEmail: l.user.email, type: 'like' })),
        ...videoLink.shares.map(s => ({ userId: s.userId, userName: s.user.name, userEmail: s.user.email, type: 'share' })),
      ];

      const uniqueUsers = new Set(allEngagers.map(e => e.userId)).size;

      // Calculate top engagers for this video link
      const userEngagementMap = new Map<string, { userId: string; userName: string | null; userEmail: string; likes: number; shares: number }>();
      
      allEngagers.forEach(engager => {
        if (!userEngagementMap.has(engager.userId)) {
          userEngagementMap.set(engager.userId, {
            userId: engager.userId,
            userName: engager.userName,
            userEmail: engager.userEmail,
            likes: 0,
            shares: 0,
          });
        }
        
        const userData = userEngagementMap.get(engager.userId)!;
        if (engager.type === 'like') userData.likes++;
        if (engager.type === 'share') userData.shares++;
      });

      const topEngagers = Array.from(userEngagementMap.values())
        .map(user => ({
          ...user,
          engagements: user.likes + user.shares,
        }))
        .sort((a, b) => b.engagements - a.engagements)
        .slice(0, 5);

      return {
        videoLinkId: videoLink.id,
        title: videoLink.Video.title,
        url: videoLink.linkUrl,
        platform: videoLink.platform,
        totalLikes,
        totalShares,
        totalCompletions,
        totalEngagements,
        uniqueUsers,
        topEngagers,
      };
    });

    // Get user engagement summary
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        VideoLinkLikes: {
          where: { createdAt: { gte: startDate } },
          select: {
            createdAt: true,
            videoLink: { 
              select: { 
                id: true, 
                platform: true,
                Video: {
                  select: { title: true }
                }
              } 
            },
          },
        },
        VideoLinkShares: {
          where: { createdAt: { gte: startDate } },
          select: {
            createdAt: true,
            videoLink: { 
              select: { 
                id: true, 
                platform: true,
                Video: {
                  select: { title: true }
                }
              } 
            },
          },
        },
        completedLinks: {
          where: { completedAt: { gte: startDate } },
          select: { completedAt: true },
        },
      },
    });

    const userEngagementSummary: UserEngagementSummary[] = users
      .map(user => {
        const totalLikes = user.VideoLinkLikes.length;
        const totalShares = user.VideoLinkShares.length;
        const totalCompletions = user.completedLinks.length;
        const engagementScore = (totalLikes * 2) + (totalShares * 3) + totalCompletions;

        // Get all engagement dates
        const allEngagements = [
          ...user.VideoLinkLikes.map(l => l.createdAt),
          ...user.VideoLinkShares.map(s => s.createdAt),
          ...user.completedLinks.map(c => c.completedAt),
        ];

        const lastEngagementDate = allEngagements.length > 0 
          ? new Date(Math.max(...allEngagements.map(d => d.getTime())))
          : null;

        // Calculate engagement frequency (engagements per day since registration)
        const daysSinceRegistration = Math.max(1, Math.floor((new Date().getTime() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)));
        const engagementFrequency = (totalLikes + totalShares + totalCompletions) / daysSinceRegistration;

        // Favorite videos (most engaged with)
        const videoEngagementMap = new Map<string, { videoLinkId: string; title: string; engagements: number }>();
        
        [...user.VideoLinkLikes, ...user.VideoLinkShares].forEach(engagement => {
          const videoLinkId = engagement.videoLink.id;
          const title = engagement.videoLink.Video.title;
          
          if (!videoEngagementMap.has(videoLinkId)) {
            videoEngagementMap.set(videoLinkId, { videoLinkId, title, engagements: 0 });
          }
          videoEngagementMap.get(videoLinkId)!.engagements++;
        });

        const favoriteVideos = Array.from(videoEngagementMap.values())
          .sort((a, b) => b.engagements - a.engagements)
          .slice(0, 3);

        // Favorite platforms
        const platformEngagementMap = new Map<string, number>();
        [...user.VideoLinkLikes, ...user.VideoLinkShares].forEach(engagement => {
          const platform = engagement.videoLink.platform;
          platformEngagementMap.set(platform, (platformEngagementMap.get(platform) || 0) + 1);
        });

        const favoritePlatforms = Array.from(platformEngagementMap.entries())
          .map(([platform, engagements]) => ({ platform, engagements }))
          .sort((a, b) => b.engagements - a.engagements)
          .slice(0, 3);

        const uniqueVideoLinks = new Set([
          ...user.VideoLinkLikes.map(l => l.videoLink.id),
          ...user.VideoLinkShares.map(s => s.videoLink.id),
        ]).size;

        return {
          userId: user.id,
          userName: user.name,
          userEmail: user.email,
          userRole: user.role,
          totalVideoLinksEngaged: uniqueVideoLinks,
          totalLikes,
          totalShares,
          totalCompletions,
          engagementScore,
          favoriteVideos,
          favoritePlatforms,
          lastEngagementDate,
          engagementFrequency: Number(engagementFrequency.toFixed(3)),
        };
      })
      .filter(user => user.engagementScore > 0)
      .sort((a, b) => b.engagementScore - a.engagementScore);

    // Platform insights
    const platformInsights: Record<string, {
      totalEngagements: number;
      totalLikes: number;
      totalShares: number;
      totalCompletions: number;
      uniqueUsers: Set<string>;
      videoLinks: { videoLinkId: string; title: string; engagements: number }[];
    }> = {};
    
    videoLinkPerformance.forEach(vl => {
      if (!platformInsights[vl.platform]) {
        platformInsights[vl.platform] = {
          totalEngagements: 0,
          totalLikes: 0,
          totalShares: 0,
          totalCompletions: 0,
          uniqueUsers: new Set(),
          videoLinks: [],
        };
      }
      
      const platform = platformInsights[vl.platform];
      platform.totalEngagements += vl.totalEngagements;
      platform.totalLikes += vl.totalLikes;
      platform.totalShares += vl.totalShares;
      platform.totalCompletions += vl.totalCompletions;
      vl.topEngagers.forEach(engager => platform.uniqueUsers.add(engager.userId));
      platform.videoLinks.push({ videoLinkId: vl.videoLinkId, title: vl.title, engagements: vl.totalEngagements });
    });

    // Convert Set to number and get top video links
    const finalPlatformInsights: { [platform: string]: {
      totalEngagements: number;
      totalLikes: number;
      totalShares: number;
      totalCompletions: number;
      uniqueUsers: number;
      topVideoLinks: { videoLinkId: string; title: string; engagements: number }[];
    } } = {};
    
    Object.keys(platformInsights).forEach(platform => {
      finalPlatformInsights[platform] = {
        totalEngagements: platformInsights[platform].totalEngagements,
        totalLikes: platformInsights[platform].totalLikes,
        totalShares: platformInsights[platform].totalShares,
        totalCompletions: platformInsights[platform].totalCompletions,
        uniqueUsers: platformInsights[platform].uniqueUsers.size,
        topVideoLinks: platformInsights[platform].videoLinks
          .sort((a, b) => b.engagements - a.engagements)
          .slice(0, 5),
      };
    });

    // Time-based insights (daily breakdown)
    const timeBasedInsights: Record<string, {
      totalEngagements: number;
      uniqueUsers: Set<string>;
      newEngagers: Set<string>;
    }> = {};
    const allEngagementDates = [
      ...videoLinksWithEngagements.flatMap(vl => [
        ...vl.likes.map(l => ({ date: l.createdAt.toISOString().split('T')[0], userId: l.userId })),
        ...vl.shares.map(s => ({ date: s.createdAt.toISOString().split('T')[0], userId: s.userId })),
      ]),
    ];

    allEngagementDates.forEach(({ date, userId }) => {
      if (!timeBasedInsights[date]) {
        timeBasedInsights[date] = {
          totalEngagements: 0,
          uniqueUsers: new Set(),
          newEngagers: new Set(),
        };
      }
      
      timeBasedInsights[date].totalEngagements++;
      timeBasedInsights[date].uniqueUsers.add(userId);
    });

    // Convert Sets to numbers
    const finalTimeBasedInsights: { [date: string]: {
      totalEngagements: number;
      uniqueUsers: number;
      newEngagers: number;
    } } = {};
    
    Object.keys(timeBasedInsights).forEach(date => {
      finalTimeBasedInsights[date] = {
        totalEngagements: timeBasedInsights[date].totalEngagements,
        uniqueUsers: timeBasedInsights[date].uniqueUsers.size,
        newEngagers: 0, // Simplified for now
      };
    });

    // Calculate summary
    const totalEngagements = videoLinkPerformance.reduce((sum, vl) => sum + vl.totalEngagements, 0);
    const totalUniqueEngagers = new Set(userEngagementSummary.map(u => u.userId)).size;
    const averageEngagementsPerVideoLink = videoLinkPerformance.length > 0 ? totalEngagements / videoLinkPerformance.length : 0;
    const averageEngagementsPerUser = totalUniqueEngagers > 0 ? totalEngagements / totalUniqueEngagers : 0;
    
    const topPlatform = Object.entries(finalPlatformInsights)
      .sort(([,a], [,b]) => b.totalEngagements - a.totalEngagements)[0]?.[0] || '';
    
    const mostEngagedVideoLink = videoLinkPerformance.length > 0 ? videoLinkPerformance[0].title : '';

    const response: UserEngagementResponse = {
      videoLinkPerformance: videoLinkPerformance.slice(0, limit),
      userEngagementSummary: userEngagementSummary.slice(0, limit),
      platformInsights: finalPlatformInsights,
      timeBasedInsights: finalTimeBasedInsights,
      summary: {
        totalVideoLinks: videoLinksWithEngagements.length,
        totalEngagements,
        totalUniqueEngagers,
        averageEngagementsPerVideoLink: Number(averageEngagementsPerVideoLink.toFixed(2)),
        averageEngagementsPerUser: Number(averageEngagementsPerUser.toFixed(2)),
        topPlatform,
        mostEngagedVideoLink,
      },
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error fetching user engagement data:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
