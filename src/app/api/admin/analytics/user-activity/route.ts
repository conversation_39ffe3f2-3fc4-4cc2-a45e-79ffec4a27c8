import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Role } from '@/generated/prisma/client';

interface UserActivity {
  id: string;
  name: string | null;
  email: string;
  role: Role;
  createdAt: Date;
  lastActivityAt: Date | null;
  totalEngagements: number;
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  engagementScore: number;
  leaderboardPosition: number;
  platformBreakdown: {
    [platform: string]: {
      likes: number;
      shares: number;
      completions: number;
    };
  };
  recentActivity: {
    date: string;
    action: string;
    platform: string;
    videoId?: string;
  }[];
  activityTrend: {
    [date: string]: {
      likes: number;
      shares: number;
      completions: number;
    };
  };
  daysSinceLastActivity: number | null;
  isActive: boolean;
}

interface UserActivityResponse {
  users: UserActivity[];
  summary: {
    totalUsers: number;
    activeUsers: number;
    topPerformers: number;
    lowEngagementUsers: number;
    averageEngagementScore: number;
    totalPlatformEngagements: {
      [platform: string]: number;
    };
  };
  filters: {
    days: number;
    sortBy: string;
    limit: number;
    minEngagement: number;
  };
}

// GET /api/admin/analytics/user-activity - Get detailed user activity analysis
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session.user?.role !== Role.ADMIN) {
    return NextResponse.json({ message: 'Forbidden - Admin access required' }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const days = parseInt(searchParams.get('days') || '30');
  const sortBy = searchParams.get('sortBy') || 'engagementScore'; // engagementScore, lastActivity, totalShares, totalLikes
  const limit = parseInt(searchParams.get('limit') || '50');
  const minEngagement = parseInt(searchParams.get('minEngagement') || '0');

  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get all users with their basic info
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        lastActivityAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get user activity details
    const userActivityData = await Promise.all(
      users.map(async (user) => {
        // Get user engagements within the time period
        const engagements = await prisma.userEngagement.findMany({
          where: {
            userId: user.id,
            createdAt: { gte: startDate },
          },
          select: {
            action: true,
            platform: true,
            videoId: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 20, // Recent activity limit
        });

        // Calculate engagement statistics
        const totalLikes = engagements.filter(e => e.action === 'like').length;
        const totalShares = engagements.filter(e => e.action === 'share').length;
        const totalCompletions = engagements.filter(e => e.action === 'complete').length;
        const totalEngagements = totalLikes + totalShares + totalCompletions;

        // Calculate engagement score (likes=2, shares=3, completions=1)
        const engagementScore = (totalLikes * 2) + (totalShares * 3) + totalCompletions;

        // Platform breakdown
        const platformBreakdown: { [platform: string]: { likes: number; shares: number; completions: number } } = {};
        engagements.forEach(engagement => {
          if (!platformBreakdown[engagement.platform]) {
            platformBreakdown[engagement.platform] = { likes: 0, shares: 0, completions: 0 };
          }
          
          if (engagement.action === 'like') {
            platformBreakdown[engagement.platform].likes++;
          } else if (engagement.action === 'share') {
            platformBreakdown[engagement.platform].shares++;
          } else if (engagement.action === 'complete') {
            platformBreakdown[engagement.platform].completions++;
          }
        });

        // Recent activity
        const recentActivity = engagements.slice(0, 10).map(e => ({
          date: e.createdAt.toISOString(),
          action: e.action,
          platform: e.platform,
          videoId: e.videoId || undefined,
        }));

        // Activity trend (daily breakdown)
        const activityTrend: { [date: string]: { likes: number; shares: number; completions: number } } = {};
        engagements.forEach(engagement => {
          const date = engagement.createdAt.toISOString().split('T')[0];
          if (!activityTrend[date]) {
            activityTrend[date] = { likes: 0, shares: 0, completions: 0 };
          }
          
          if (engagement.action === 'like') {
            activityTrend[date].likes++;
          } else if (engagement.action === 'share') {
            activityTrend[date].shares++;
          } else if (engagement.action === 'complete') {
            activityTrend[date].completions++;
          }
        });

        const now = new Date();
        const daysSinceLastActivity = user.lastActivityAt 
          ? Math.floor((now.getTime() - user.lastActivityAt.getTime()) / (1000 * 60 * 60 * 24))
          : null;

        const isActive = daysSinceLastActivity !== null && daysSinceLastActivity <= 7;

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          createdAt: user.createdAt,
          lastActivityAt: user.lastActivityAt,
          totalEngagements,
          totalLikes,
          totalShares,
          totalCompletions,
          engagementScore,
          leaderboardPosition: 0, // Will be calculated after sorting
          platformBreakdown,
          recentActivity,
          activityTrend,
          daysSinceLastActivity,
          isActive,
        };
      })
    );

    // Filter users based on minimum engagement
    const filteredUsers = userActivityData.filter(user => user.engagementScore >= minEngagement);

    // Sort users based on sortBy parameter
    const sortedUsers = filteredUsers.sort((a, b) => {
      switch (sortBy) {
        case 'lastActivity':
          if (!a.lastActivityAt) return 1;
          if (!b.lastActivityAt) return -1;
          return b.lastActivityAt.getTime() - a.lastActivityAt.getTime();
        case 'totalShares':
          return b.totalShares - a.totalShares;
        case 'totalLikes':
          return b.totalLikes - a.totalLikes;
        case 'totalEngagements':
          return b.totalEngagements - a.totalEngagements;
        case 'engagementScore':
        default:
          return b.engagementScore - a.engagementScore;
      }
    });

    // Assign leaderboard positions
    const usersWithPositions = sortedUsers.map((user, index) => ({
      ...user,
      leaderboardPosition: index + 1,
    }));

    // Apply limit
    const limitedUsers = usersWithPositions.slice(0, limit);

    // Calculate summary statistics
    const activeUsers = userActivityData.filter(u => u.isActive).length;
    const topPerformers = userActivityData.filter(u => u.engagementScore > 20).length;
    const lowEngagementUsers = userActivityData.filter(u => u.engagementScore < 5).length;
    const totalEngagementScore = userActivityData.reduce((sum, u) => sum + u.engagementScore, 0);
    const averageEngagementScore = userActivityData.length > 0 ? totalEngagementScore / userActivityData.length : 0;

    // Calculate total platform engagements
    const totalPlatformEngagements: { [platform: string]: number } = {};
    userActivityData.forEach(user => {
      Object.entries(user.platformBreakdown).forEach(([platform, stats]) => {
        if (!totalPlatformEngagements[platform]) {
          totalPlatformEngagements[platform] = 0;
        }
        totalPlatformEngagements[platform] += stats.likes + stats.shares + stats.completions;
      });
    });

    const response: UserActivityResponse = {
      users: limitedUsers,
      summary: {
        totalUsers: users.length,
        activeUsers,
        topPerformers,
        lowEngagementUsers,
        averageEngagementScore: Number(averageEngagementScore.toFixed(2)),
        totalPlatformEngagements,
      },
      filters: {
        days,
        sortBy,
        limit,
        minEngagement,
      },
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error fetching user activity data:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
