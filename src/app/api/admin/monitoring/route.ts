import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ApplicationMonitor } from '@/lib/monitoring';
import { z } from 'zod';

const metricsQuerySchema = z.object({
  metric: z.string().optional(),
  since: z.string().optional(),
  tags: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';
    
    switch (action) {
      case 'health':
        const health = await ApplicationMonitor.getSystemHealth();
        return NextResponse.json(health);
        
      case 'metrics':
        const query = metricsQuerySchema.parse({
          metric: searchParams.get('metric'),
          since: searchParams.get('since'),
          tags: searchParams.get('tags'),
        });
        
        const since = query.since ? new Date(query.since) : new Date(Date.now() - 24 * 60 * 60 * 1000);
        const tags = query.tags ? JSON.parse(query.tags) : undefined;
        
        if (!query.metric) {
          return NextResponse.json(
            { error: 'Metric name is required' },
            { status: 400 }
          );
        }
        
        const metrics = ApplicationMonitor.getMetrics(query.metric, tags, since);
        return NextResponse.json({ metrics });
        
      case 'alerts':
        // Return current alert status
        return NextResponse.json({
          alerts: [], // Would fetch from alert store
          rules: [] // Would fetch configured alert rules
        });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in monitoring API:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch monitoring data' },
      { status: 500 }
    );
  }
}