import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { AuditLogger } from '@/lib/audit-logger';
import { z } from 'zod';

const auditFilterSchema = z.object({
  userId: z.string().optional(),
  action: z.string().optional(),
  resource: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const rawParams = Object.fromEntries(searchParams.entries());
    
    const validatedParams = auditFilterSchema.parse(rawParams);
    
    const filter = {
      userId: validatedParams.userId,
      action: validatedParams.action,
      resource: validatedParams.resource,
      startDate: validatedParams.startDate ? new Date(validatedParams.startDate) : undefined,
      endDate: validatedParams.endDate ? new Date(validatedParams.endDate) : undefined,
      page: validatedParams.page ? parseInt(validatedParams.page) : 1,
      limit: validatedParams.limit ? parseInt(validatedParams.limit) : 50,
    };

    // Check if requesting stats only
    const statsOnly = searchParams.get('stats') === 'true';
    
    if (statsOnly) {
      const days = searchParams.get('days') ? parseInt(searchParams.get('days')!) : 30;
      const stats = await AuditLogger.getStats(days);
      return NextResponse.json({ stats });
    }

    const result = await AuditLogger.getLogs(filter);

    return NextResponse.json({
      success: true,
      ...result,
    });

  } catch (error) {
    console.error('Error fetching audit logs:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    );
  }
}

// Cleanup endpoint for log rotation
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const retentionDays = parseInt(searchParams.get('retentionDays') || '365');

    if (retentionDays < 30) {
      return NextResponse.json(
        { error: 'Retention period must be at least 30 days' },
        { status: 400 }
      );
    }

    const deletedCount = await AuditLogger.cleanupOldLogs(retentionDays);

    // Log the cleanup action
    await AuditLogger.logAdminAction(
      session.user.id,
      'AUDIT_LOG_CLEANUP',
      'audit_logs',
      undefined,
      { deletedCount, retentionDays },
      {
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || undefined,
      }
    );

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${deletedCount} old audit log entries`,
      deletedCount,
    });

  } catch (error) {
    console.error('Error cleaning up audit logs:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup audit logs' },
      { status: 500 }
    );
  }
}