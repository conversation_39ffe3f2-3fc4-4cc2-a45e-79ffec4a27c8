import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getModerationQueue, moderateReport, getModerationStats } from '@/lib/content-moderation';
import { AuditLogger } from '@/lib/audit-logger';
import { z } from 'zod';

const moderateSchema = z.object({
  reportId: z.string(),
  action: z.enum(['approve', 'reject', 'hide', 'delete', 'warn_user']),
  reason: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const statsOnly = searchParams.get('stats') === 'true';

    if (statsOnly) {
      const stats = await getModerationStats();
      return NextResponse.json({ stats });
    }

    const moderationQueue = await getModerationQueue(page, limit);
    const stats = await getModerationStats();

    return NextResponse.json({
      ...moderationQueue,
      stats,
    });

  } catch (error) {
    console.error('Error fetching moderation queue:', error);
    return NextResponse.json(
      { error: 'Failed to fetch moderation queue' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = moderateSchema.parse(body);

    const updatedReport = await moderateReport(
      validatedData.reportId,
      session.user.id,
      validatedData.action,
      validatedData.reason
    );

    // Log the moderation action
    await AuditLogger.logModerationAction(
      session.user.id,
      validatedData.action,
      validatedData.reportId,
      {
        reason: validatedData.reason,
        reportStatus: updatedReport.status,
      },
      {
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || undefined,
      }
    );

    return NextResponse.json({
      success: true,
      report: updatedReport,
      message: `Report ${validatedData.action}d successfully`,
    });

  } catch (error) {
    console.error('Error moderating report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to moderate report' },
      { status: 500 }
    );
  }
}