import { Role } from '@/generated/prisma/client';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

// GET /api/admin/users - Node-compatible handler
export async function GET(_request: Request) {
  // Extract session from request if needed (stub for demo)
  // Replace with actual session extraction logic in production
  const session = { user: { role: Role.ADMIN } };
  if (!session || session.user?.role !== Role.ADMIN) {
    return new Response(JSON.stringify({ message: 'Forbidden' }), { status: 403 });
  }
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        notificationPreference: true,
      },
    });
    return new Response(JSON.stringify(users), { status: 200 });
  } catch (error) {
    console.error('Error fetching users:', error);
    return new Response(JSON.stringify({ message: 'Internal Server Error' }), { status: 500 });
  }
}

// POST /api/admin/users - Node-compatible handler
export async function POST(request: Request) {
  // Extract session and body from request (stub for demo)
  // Replace with actual session extraction and body parsing in production
  const session = { user: { role: Role.ADMIN } };
  let body;
  const contentType = request.headers.get('content-type') || '';
  try {
    if (contentType.includes('application/json')) {
      body = await request.json();
      console.log('[UserManagement][POST] Parsed JSON body:', body);
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      const formData = await request.formData();
      body = Object.fromEntries(formData.entries());
      console.log('[UserManagement][POST] Parsed form-urlencoded body:', body);
    } else if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData();
      body = Object.fromEntries(formData.entries());
      console.log('[UserManagement][POST] Parsed multipart body:', body);
    } else {
      console.error('[UserManagement][POST] Unsupported Content-Type:', contentType);
      return new Response(JSON.stringify({ message: 'Unsupported Content-Type' }), { status: 415 });
    }
  } catch (err) {
    console.error('[UserManagement][POST] Error parsing request body:', err);
    return new Response(JSON.stringify({ message: 'Invalid request body' }), { status: 400 });
  }
  if (!session || session.user?.role !== Role.ADMIN) {
    console.log('[UserManagement][POST] Forbidden: session', session);
    return new Response(JSON.stringify({ message: 'Forbidden' }), { status: 403 });
  }
  try {
    const { name, email, password, role } = body;
    console.log('[UserManagement][POST] Incoming fields:', { name, email, password, role, rawBody: body });
    if (!name || !email || !password || !role) {
      console.log('[UserManagement][POST] Missing required fields:', { name, email, password, role, rawBody: body });
      return new Response(JSON.stringify({
        message: 'Missing required fields',
        received: { name, email, password, role, rawBody: body }
      }), { status: 400 });
    }
    const hashedPassword = await bcrypt.hash(password, 10);
    console.log('[UserManagement][POST] Hashed password:', hashedPassword);
    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role,
      },
    });
    console.log('[UserManagement][POST] Created user:', newUser);
    const { password: _, ...userWithoutPassword } = newUser;
    return new Response(JSON.stringify(userWithoutPassword), { status: 201 });
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2002') {
      console.log('[UserManagement][POST] Prisma unique constraint error:', error);
      return new Response(JSON.stringify({ message: 'Email already in use.' }), { status: 409 });
    }
    console.error('[UserManagement][POST] Error creating user:', error);
    return new Response(JSON.stringify({ message: 'Internal Server Error' }), { status: 500 });
  }
}
