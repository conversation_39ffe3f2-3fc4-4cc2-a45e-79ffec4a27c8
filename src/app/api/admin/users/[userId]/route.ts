import bcrypt from 'bcryptjs';
import { NextRequest, NextResponse } from 'next/server';
import { Role } from '@/generated/prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/admin/users/[userId] - Get user details (Admin only)
export async function GET(
 request: NextRequest,
 { params }: { params: Promise<{ userId: string }> }
) {
 const session = await getServerSession(authOptions);
 if (!session || session.user?.role !== Role.ADMIN) {
   return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
 }

 const { userId } = await params;
 try {
   const user = await prisma.user.findUnique({
     where: { id: userId },
   });
   if (!user) {
     return NextResponse.json({ message: 'User not found' }, { status: 404 });
   }
   const { password: _password, ...userWithoutPassword } = user;
   return NextResponse.json(userWithoutPassword);
 } catch (error: unknown) {
   console.error(`Error fetching user ${userId}:`, error);
   return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
 }
}

// PUT /api/admin/users/[userId] - Update a user (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== Role.ADMIN) {
    return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
  }

  const { userId } = await params;
  try {
    const body = await request.json();
    const { name, email, role, notificationPreference } = body;

// Hash password if present in update
if (body.password) {
  body.password = await bcrypt.hash(body.password, 10);
}
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name,
        email,
        role,
        notificationPreference,
      },
    });

    // Exclude password from the response
    const { password: _password, ...userWithoutPassword } = updatedUser;

    return NextResponse.json(userWithoutPassword);
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2025') {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }
    console.error(`Error updating user ${userId}:`, error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/admin/users/[userId] - Delete a user (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== Role.ADMIN) {
    return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
  }

  const { userId } = await params;

  try {
    await prisma.user.delete({
      where: { id: userId },
    });

    return new NextResponse(null, { status: 204 }); // No Content
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 'P2025') {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }
    console.error(`Error deleting user ${userId}:`, error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
