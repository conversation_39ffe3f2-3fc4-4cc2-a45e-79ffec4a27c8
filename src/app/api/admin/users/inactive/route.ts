import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Role } from '@/generated/prisma/client';

interface InactiveUser {
  id: string;
  name: string | null;
  email: string;
  role: Role;
  createdAt: Date;
  lastActivityAt: Date | null;
  totalEngagements: number;
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  daysSinceLastActivity: number | null;
  daysSinceRegistration: number;
  engagementScore: number;
  status: 'never_logged_in' | 'inactive' | 'low_engagement' | 'no_engagement';
}

interface InactiveUsersResponse {
  users: InactiveUser[];
  summary: {
    totalInactiveUsers: number;
    neverLoggedIn: number;
    inactiveUsers: number;
    lowEngagementUsers: number;
    noEngagementUsers: number;
  };
  filters: {
    inactivityDays: number;
    minEngagementScore: number;
    includeNeverLoggedIn: boolean;
  };
}

// GET /api/admin/users/inactive - Get inactive users report
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session.user?.role !== Role.ADMIN) {
    return NextResponse.json({ message: 'Forbidden - Admin access required' }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const inactivityDays = parseInt(searchParams.get('inactivityDays') || '30');
  const minEngagementScore = parseInt(searchParams.get('minEngagementScore') || '5');
  const includeNeverLoggedIn = searchParams.get('includeNeverLoggedIn') !== 'false';
  const limit = parseInt(searchParams.get('limit') || '100');

  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - inactivityDays);

    // Get all users with their engagement data
    const usersWithEngagement = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        lastActivityAt: true,
        _count: {
          select: {
            UserEngagement: true,
            completedLinks: true,
          },
        },
      },
      orderBy: {
        lastActivityAt: 'asc', // Show least active users first
      },
      take: limit * 2, // Get more to filter properly
    });

    // Get detailed engagement statistics for each user
    const userEngagementStats = await Promise.all(
      usersWithEngagement.map(async (user) => {
        const engagements = await prisma.userEngagement.groupBy({
          by: ['action'],
          where: { userId: user.id },
          _count: { id: true },
        });

        const likes = engagements.find(e => e.action === 'like')?._count.id || 0;
        const shares = engagements.find(e => e.action === 'share')?._count.id || 0;
        const completions = engagements.find(e => e.action === 'complete')?._count.id || 0;
        const totalEngagements = likes + shares + completions;

        const now = new Date();
        const daysSinceLastActivity = user.lastActivityAt 
          ? Math.floor((now.getTime() - user.lastActivityAt.getTime()) / (1000 * 60 * 60 * 24))
          : null;
        const daysSinceRegistration = Math.floor((now.getTime() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24));

        // Calculate engagement score (likes=2, shares=3, completions=1)
        const engagementScore = (likes * 2) + (shares * 3) + completions;

        // Determine user status
        let status: InactiveUser['status'];
        if (!user.lastActivityAt) {
          status = 'never_logged_in';
        } else if (daysSinceLastActivity && daysSinceLastActivity > inactivityDays) {
          status = 'inactive';
        } else if (totalEngagements === 0) {
          status = 'no_engagement';
        } else if (engagementScore < minEngagementScore) {
          status = 'low_engagement';
        } else {
          return null; // User is active, skip
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          createdAt: user.createdAt,
          lastActivityAt: user.lastActivityAt,
          totalEngagements,
          totalLikes: likes,
          totalShares: shares,
          totalCompletions: completions,
          daysSinceLastActivity,
          daysSinceRegistration,
          engagementScore,
          status,
        } as InactiveUser;
      })
    );

    // Filter out null values and apply filters
    const inactiveUsers = userEngagementStats
      .filter((user): user is InactiveUser => user !== null)
      .filter(user => {
        if (!includeNeverLoggedIn && user.status === 'never_logged_in') {
          return false;
        }
        return true;
      })
      .slice(0, limit);

    // Calculate summary statistics
    const summary = {
      totalInactiveUsers: inactiveUsers.length,
      neverLoggedIn: inactiveUsers.filter(u => u.status === 'never_logged_in').length,
      inactiveUsers: inactiveUsers.filter(u => u.status === 'inactive').length,
      lowEngagementUsers: inactiveUsers.filter(u => u.status === 'low_engagement').length,
      noEngagementUsers: inactiveUsers.filter(u => u.status === 'no_engagement').length,
    };

    const response: InactiveUsersResponse = {
      users: inactiveUsers,
      summary,
      filters: {
        inactivityDays,
        minEngagementScore,
        includeNeverLoggedIn,
      },
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error fetching inactive users:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
