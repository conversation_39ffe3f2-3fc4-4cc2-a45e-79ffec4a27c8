/**
 * API endpoint for generating video thumbnails
 */

import { NextRequest, NextResponse } from 'next/server';
import { generateThumbnailUrl } from '@/lib/image-utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { platforms } = body;

    if (!platforms || typeof platforms !== 'object') {
      return NextResponse.json(
        { error: 'Platforms object is required' },
        { status: 400 }
      );
    }

    // Generate thumbnail URL from platform URLs
    const thumbnailUrl = generateThumbnailUrl(platforms);

    if (!thumbnailUrl) {
      return NextResponse.json(
        { error: 'Could not generate thumbnail from provided platforms' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      thumbnailUrl,
      success: true
    });

  } catch (error) {
    console.error('Error generating thumbnail:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const youtube = searchParams.get('youtube');
  const tiktok = searchParams.get('tiktok');
  const rumble = searchParams.get('rumble');

  const platforms = {
    ...(youtube && { youtube }),
    ...(tiktok && { tiktok }),
    ...(rumble && { rumble }),
  };

  if (Object.keys(platforms).length === 0) {
    return NextResponse.json(
      { error: 'At least one platform URL is required' },
      { status: 400 }
    );
  }

  try {
    const thumbnailUrl = generateThumbnailUrl(platforms);

    if (!thumbnailUrl) {
      return NextResponse.json(
        { error: 'Could not generate thumbnail from provided platforms' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      thumbnailUrl,
      success: true
    });

  } catch (error) {
    console.error('Error generating thumbnail:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}