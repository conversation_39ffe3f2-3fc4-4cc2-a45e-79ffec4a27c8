import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NotificationService } from '@/lib/notification-service';
import { prisma } from '@/lib/prisma';
import type { EmailPreference } from '@/generated/prisma/client';

export const dynamic = 'force-dynamic'; // Force dynamic rendering

// GET /api/email-preferences - Get user's email notification preferences
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's preferences
    const preferences = await prisma.emailPreference.findMany({
      where: {
        userId: session.user.id,
      },
    });

    // If no preferences are found, it might be a new user. 
    // For now, we return the existing preferences or an empty array.
    const result = preferences.map((pref: EmailPreference) => ({
      type: pref.notificationType,
      isEnabled: pref.isEnabled,
    }));

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching email preferences:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/email-preferences - Update user's email notification preferences
export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { preferences } = await req.json();

    if (!Array.isArray(preferences)) {
      return NextResponse.json({ error: 'Invalid preferences format' }, { status: 400 });
    }

    await NotificationService.updateEmailPreferences(
      session.user.id,
      preferences
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating email preferences:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}