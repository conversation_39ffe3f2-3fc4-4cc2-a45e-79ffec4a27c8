import { NextRequest, NextResponse } from 'next/server';
import { ApplicationMonitor } from '@/lib/monitoring';

export async function GET(_request: NextRequest) {
  try {
    const health = await ApplicationMonitor.getSystemHealth();
    
    // Return appropriate HTTP status based on health
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;
    
    return NextResponse.json({
      status: health.status,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV,
      uptime: health.metrics.uptime,
      checks: health.checks,
      system: {
        memory: {
          used: Math.round(health.metrics.memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(health.metrics.memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(health.metrics.memoryUsage.external / 1024 / 1024),
          unit: 'MB'
        },
        cpu: health.metrics.cpuUsage
      }
    }, { status: statusCode });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 503 });
  }
}