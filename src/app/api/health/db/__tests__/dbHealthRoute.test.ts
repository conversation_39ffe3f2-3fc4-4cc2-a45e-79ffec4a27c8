// src/app/api/health/db/__tests__/dbHealthRoute.test.ts
import type { NextRequest } from 'next/server';

// Mock NextResponse.json
const jsonMock = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: { json: jsonMock },
}));

// Mock prisma client to avoid real DB access in unit test
jest.mock('@/lib/prisma', () => ({
  prisma: {
    $queryRaw: jest.fn().mockResolvedValue([{ ok: 1 }]),
  },
}));

import { GET } from '../route';

describe('GET /api/health/db', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns 200 when DB query succeeds', async () => {
    await GET();

    expect(jsonMock).toHaveBeenCalledWith(
      { status: 'healthy', ok: true },
      { status: 200, headers: { 'Cache-Control': 'no-store' } }
    );
  });
});
