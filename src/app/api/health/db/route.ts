import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/health/db - Check database connectivity via a trivial query
export async function GET() {
  try {
    // A simple raw query that should work on PostgreSQL
    const result = await prisma.$queryRaw<Array<{ ok: number }>>`SELECT 1 as ok`;
    const ok = Array.isArray(result) && result.length > 0 && result[0]?.ok === 1;

    return NextResponse.json(
      { status: ok ? 'healthy' : 'degraded', ok },
      { status: ok ? 200 : 503, headers: { 'Cache-Control': 'no-store' } }
    );
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        ok: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503, headers: { 'Cache-Control': 'no-store' } }
    );
  }
}
