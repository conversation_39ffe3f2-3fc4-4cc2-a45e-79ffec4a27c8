import { NextResponse } from 'next/server';
import { cacheService } from '@/lib/cache-service';

// GET /api/health/cache - Check cache health status
export async function GET() {
  try {
    const healthCheck = await cacheService.healthCheck();
    
    return NextResponse.json({
      status: healthCheck.redis ? 'healthy' : 'degraded',
      cache: {
        redis: {
          available: healthCheck.redis,
          status: healthCheck.redis ? 'connected' : 'disconnected',
        },
      },
      timestamp: healthCheck.timestamp,
      message: healthCheck.redis 
        ? 'Cache is operational' 
        : 'Cache is unavailable - falling back to database queries',
    }, { 
      status: healthCheck.redis ? 200 : 503 
    });
  } catch (error) {
    console.error('Cache health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      cache: {
        redis: {
          available: false,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      timestamp: new Date().toISOString(),
      message: 'Cache health check failed',
    }, { status: 503 });
  }
}