// src/app/api/health/__tests__/healthRoute.test.ts
import type { NextRequest } from 'next/server';

// Mock NextResponse.json
const jsonMock = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: { json: jsonMock },
}));

// Import after mocks
import { GET } from '../route';

describe('GET /api/health', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns 200 with ok: true', async () => {
    await GET();

    expect(jsonMock).toHaveBeenCalledWith(
      { ok: true },
      { status: 200, headers: { 'Cache-Control': 'no-store' } }
    );
  });
});
