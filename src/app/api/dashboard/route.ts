import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

import { cacheService } from '@/lib/cache-service';
import { DashboardQueryOptimizer } from '@/lib/query-optimization';
import { monitoredQueries } from '@/lib/query-performance-monitor';

// Enable CORS
const allowedOrigins = [
  `http://localhost:${process.env.PORT || 3000}`,
  'https://nwapromote.com',
  'https://www.nwapromote.com'
];

const corsHeaders = (origin: string) => ({
  'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : allowedOrigins[0],
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Credentials': 'true',
  'Vary': 'Origin',
  'Access-Control-Max-Age': '86400',
});

// Marked with underscore to indicate it's for future use
interface _LeaderboardResult {
  position: bigint;
}

// Marked with underscore to indicate it's for future use
interface _VideoWithUser {
  id: string;
  title: string;
  description: string | null;
  url: string;
  thumbnailUrl: string | null;
  views: number;
  createdAt: Date;
  user: {
    id: string;
    name: string | null;
    email: string;
  };
}

export const dynamic = 'force-dynamic'; // Force dynamic rendering

// GET /api/dashboard - Fetch user dashboard data
export async function GET(request: Request) {
  const origin = request.headers.get('origin') || '';
  
  try {
    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: corsHeaders(origin)
      });
    }

    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Dashboard API called, checking session...');
    }
    
    const session = await getServerSession(authOptions);
    
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Session check result:', {
        hasSession: !!session,
        hasUser: !!(session?.user),
        userId: session?.user?.id,
        userEmail: session?.user?.email
      });
    }
    
    if (!session?.user) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('No session found, returning 401 Unauthorized');
      }
      
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }), 
        { 
          status: 401, 
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        }
      );
    }
    
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Session found, proceeding with dashboard data fetch for user:', session.user.id);
    }

    // Use optimized dashboard query with performance monitoring
    const dashboardData = await monitoredQueries.getDashboard(
      async () => {
        // Try to get cached data first
        const cachedData = await cacheService.getDashboardStats();
        if (cachedData) {
          // If we have cached stats, get other data optimally
          return await DashboardQueryOptimizer.getDashboardData(session.user.id);
        }
        
        // If no cache, fetch everything optimally
        return await DashboardQueryOptimizer.getDashboardData(session.user.id);
      },
      session.user.id
    );

    if (!dashboardData.user) {
      return new NextResponse(
        JSON.stringify({ error: 'User not found' }), 
        { 
          status: 404, 
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        }
      );
    }

    // Cache the results for future requests
    await Promise.all([
      cacheService.setDashboardStats(dashboardData.dashboardStats),
      cacheService.setNWAVideos(session.user.id, dashboardData.nwaVideos),
      cacheService.setUserVideoLinks(dashboardData.userVideoLinks),
      cacheService.setUserStats(session.user.id, dashboardData.userStats),
    ]);
    
    // Return the dashboard data
    const responseData = {
      points: dashboardData.user.score || 0,
      level: dashboardData.user.level || 1,
      totalPoints: dashboardData.user.totalPoints || 0,
      leaderboardPosition: dashboardData.userStats.leaderboardPosition,
      nwaVideos: dashboardData.nwaVideos,
      userVideoLinks: dashboardData.userVideoLinks,
      totalVideos: dashboardData.dashboardStats.totalVideos,
      totalUsers: dashboardData.dashboardStats.totalUsers,
      totalViews: dashboardData.dashboardStats.totalViews,
      isAdmin: dashboardData.user.role === 'ADMIN' || dashboardData.user.role === 'NWA_TEAM',
    };

    return new NextResponse(
      JSON.stringify(responseData),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      }
    );
    
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return new NextResponse(
      JSON.stringify({ 
        error: 'Failed to fetch dashboard data',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      }
    );
  }
}