// src/app/auth/error/__tests__/errorPage.test.tsx

import { render, screen } from '@testing-library/react';
import AuthErrorPage from '../page';
import { useSearchParams } from 'next/navigation';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
});

describe('Auth Error Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should display OAuthAccountNotLinked error message', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'error') return 'OAuthAccountNotLinked';
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Account Already Linked')).toBeInTheDocument();
    expect(screen.getByText('This account is already linked with another profile. Please sign in with the original account or contact support.')).toBeInTheDocument();
  });

  it('should display OAuthCallback error message', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'error') return 'OAuthCallback';
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Authentication Failed')).toBeInTheDocument();
    expect(screen.getByText('We couldn\'t complete the authentication process. Please try again or contact support if the problem persists.')).toBeInTheDocument();
  });

  it('should display AccessDenied error message', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'error') return 'AccessDenied';
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText('You do not have permission to access this resource.')).toBeInTheDocument();
  });

  it('should display Verification error message', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'error') return 'Verification';
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Verification Error')).toBeInTheDocument();
    expect(screen.getByText('The verification token has expired or is invalid. Please try signing in again.')).toBeInTheDocument();
  });

  it('should display default error message for unknown errors', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'error') return 'UnknownError';
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Authentication Error')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred during authentication. Please try again.')).toBeInTheDocument();
  });

  it('should display default error message when no error is provided', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Authentication Error')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred during authentication. Please try again.')).toBeInTheDocument();
  });

  it('should include links to try again and go home', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'error') return 'OAuthCallback';
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    render(<AuthErrorPage />);

    expect(screen.getByText('Try Again')).toBeInTheDocument();
    expect(screen.getByText('Go to Homepage')).toBeInTheDocument();
    
    // Check that the links have the correct hrefs
    const tryAgainLink = screen.getByText('Try Again').closest('a');
    const homeLink = screen.getByText('Go to Homepage').closest('a');
    
    expect(tryAgainLink).toHaveAttribute('href', '/auth/signin?callbackUrl=%2Fdashboard');
    expect(homeLink).toHaveAttribute('href', '/');
  });
});