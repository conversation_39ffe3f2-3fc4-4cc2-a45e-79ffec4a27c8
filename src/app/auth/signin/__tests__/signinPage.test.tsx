// src/app/auth/signin/__tests__/signinPage.test.tsx

import { render, screen } from '@testing-library/react';
import SignInPage from '../page';
import { useSearchParams } from 'next/navigation';

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  signIn: jest.fn(),
  getCsrfToken: jest.fn(),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
});

describe('Sign In Page', () => {
  const mockSignIn = require('next-auth/react').signIn;

  beforeEach(() => {
    jest.clearAllMocks();
    (useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue(null)
    });
  });

  it('should display the sign in page with correct elements', () => {
    render(<SignInPage />);

    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByText('You need to be signed in to access this page.')).toBeInTheDocument();
    expect(screen.getByText('Sign In with Member Portal')).toBeInTheDocument();
    expect(screen.getByText('You will be redirected to the Member Portal for authentication.')).toBeInTheDocument();
  });

  it('should display OAuthAccountNotLinked error message', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => key === 'error' ? 'OAuthAccountNotLinked' : null
    });

    render(<SignInPage />);

    expect(screen.getByText('Authentication Error')).toBeInTheDocument();
    expect(screen.getByText('This account is already linked with another profile.')).toBeInTheDocument();
  });

  it('should display OAuthCallback error message', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => key === 'error' ? 'OAuthCallback' : null
    });

    render(<SignInPage />);

    expect(screen.getByText('Authentication Error')).toBeInTheDocument();
    expect(screen.getByText('Authentication failed. Please try again.')).toBeInTheDocument();
  });

  it('should display default error message for unknown errors', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => key === 'error' ? 'UnknownError' : null
    });

    render(<SignInPage />);

    expect(screen.getByText('Authentication Error')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument();
  });

  it('should redirect to NextAuth signin endpoint when Sign In button is clicked', () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    // Mock window.location to prevent JSDOM navigation error
    const originalLocation = window.location;
    delete (window as any).location;
    (window as any).location = { href: '', assign: jest.fn() };

    render(<SignInPage />);

    const signInButton = screen.getByText('Sign In with Member Portal');
    // Wrap interaction in act to avoid warnings
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { act } = require('react-dom/test-utils');
    act(() => {
      signInButton.click();
    });

    expect(window.location.href).toContain('/api/auth/signin/member-portal');
    expect(window.location.href).toContain(encodeURIComponent('/dashboard'));

    // restore original location
    (window as any).location = originalLocation;
  });

  it('should show loading state when signing in', async () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: (key: string) => {
        if (key === 'callbackUrl') return '/dashboard';
        return null;
      }
    });

    // Mock window.location to prevent navigation
    const originalLocation = window.location;
    delete (window as any).location;
    (window as any).location = { href: '', assign: jest.fn() };

    render(<SignInPage />);

    const signInButton = screen.getByText('Sign In with Member Portal');
    // Wrap interaction in act to avoid warnings
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { act } = require('react-dom/test-utils');
    act(() => {
      signInButton.click();
    });

    // Wait a tick for state update
    await new Promise(resolve => setTimeout(resolve, 0));

    expect(signInButton).toBeDisabled();
    expect(signInButton).toHaveTextContent(/Redirecting to Member Portal|Sign In with Member Portal/);

    // restore original location
    (window as any).location = originalLocation;
  });
});