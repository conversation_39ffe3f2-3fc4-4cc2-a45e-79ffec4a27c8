'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense, useState } from 'react';

function SignInPageContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const [loading, setLoading] = useState(false);
  const [_testUserLoading, setTestUserLoading] = useState(false);

  const handleSignIn = () => {
    setLoading(true);
    try {
      // When signing in, redirect to the originally intended page or dashboard
      const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

      // Use NextAuth's built-in OAuth provider for member portal
      window.location.href = `/api/auth/signin/member-portal?callbackUrl=${encodeURIComponent(callbackUrl)}`;
    } catch (err) {
      console.error('Sign in error:', err);
      setLoading(false);
    }
  };

  const _handleTestUserLogin = async () => {
    setTestUserLoading(true);
    try {
      // Create or get test user
      const response = await fetch('/api/test-user-creation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to create test user');
      }

      const _data = await response.json();
      
      // Redirect to dashboard after successful test user login
      window.location.href = '/dashboard';
    } catch (err) {
      console.error('Test user login error:', err);
      setTestUserLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', flexDirection: 'column', backgroundColor: '#f0f2f5' }}>
      <div style={{ padding: '40px', backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 4px 8px rgba(0,0,0,0.1)', textAlign: 'center', maxWidth: '400px', width: '100%' }}>
        <h1 style={{ marginBottom: '24px', color: '#333' }}>Sign In</h1>
        <p style={{ marginBottom: '24px', color: '#666' }}>You need to be signed in to access this page.</p>
        
        {error && (
          <div style={{ marginBottom: '24px', color: 'red', padding: '10px', border: '1px solid red', borderRadius: '4px' }}>
            <p><strong>Authentication Error</strong></p>
            <p>{error === 'OAuthAccountNotLinked' 
              ? 'This account is already linked with another profile.' 
              : error === 'OAuthCallback' 
              ? 'Authentication failed. Please try again.'
              : error === 'Callback' 
              ? 'Authentication callback failed. Please try again.'
              : error === 'TokenExchangeFailed'
              ? 'Failed to exchange token. Please try again.'
              : error === 'UserInfoFailed'
              ? 'Failed to retrieve user info. Please try again.'
              : error === 'UserCreationFailed'
              ? 'Failed to create user account. Please contact support.'
              : error === 'NetworkError'
              ? 'Network error occurred. Please check your connection and try again.'
              : error === 'OAuthInitiationFailed'
              ? 'Failed to initiate OAuth flow. Please try again.'
              : error === 'ConfigurationError'
              ? 'Service configuration error. Please contact support.'
              : error === 'server_error'
              ? 'Server error occurred during authentication. Please try again.'
              : 'An unexpected error occurred. Please try again.'}</p>
          </div>
        )}

        <button 
          onClick={handleSignIn} 
          disabled={loading}
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            color: 'white',
            backgroundColor: loading ? '#666' : '#0070f3',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer',
            width: '100%'
          }}
        >
          {loading ? 'Redirecting to Member Portal...' : 'Sign In with Member Portal'}
        </button>
        
        <p style={{ marginTop: '24px', fontSize: '14px', color: '#999' }}>
          You will be redirected to the Member Portal for authentication.
        </p>
      </div>
    </div>
  );
}

// Wrap the page content with Suspense because useSearchParams requires it.
export default function SignInPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SignInPageContent />
    </Suspense>
  );
}