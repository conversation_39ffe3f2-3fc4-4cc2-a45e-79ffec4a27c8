'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Users, Video, Eye, Trophy, Award, Star } from 'lucide-react';
import Sidebar from '@/components/Sidebar';
import NwaVideoList, { NwaVideo } from '@/components/NwaVideoList';
import UserVideoLinkList, { UserVideoLink } from '@/components/UserVideoLinkList';
import AdminVideoManager from '@/components/AdminVideoManager';
import NwaEbookList from '@/components/NwaEbookList';
import { cn } from '@/lib/utils';
import GamingCard from '@/components/GamingCard';
import NotificationBell from '@/components/NotificationBell';
import LoadingSkeleton from '@/components/LoadingSkeleton';
import { DashboardErrorBoundary, AdminErrorBoundary, VideoErrorBoundary } from '@/components/ErrorBoundary';
import { useApiRetry } from '@/hooks/useRetry';
import { useRealTimeStats } from '@/hooks/useRealTimeStats';
import UserVideoCreationForm from '@/components/UserVideoCreationForm';
import { useKeyboardNavigation, GLOBAL_SHORTCUTS } from '@/hooks/useKeyboardNavigation';
import { useScreenReader, useDynamicAnnouncements } from '@/hooks/useScreenReader';
import KeyboardShortcutsHelp, { KeyboardShortcutsButton } from '@/components/KeyboardShortcutsHelp';

// Control auto-fetch via environment variable
const DASHBOARD_AUTO_FETCH_DISABLED =
  process.env.NEXT_PUBLIC_DISABLE_DASHBOARD_AUTO_FETCH === 'true';

// (removed duplicate DASHBOARD_AUTO_FETCH_DISABLED declaration)


interface DashboardData {
  nwaVideos: NwaVideo[];
  userVideoLinks: UserVideoLink[];
  isAdmin: boolean;
  totalVideos: number;
  totalUsers: number;
  totalViews: number;
  points: number;
  level: number;
  totalPoints: number;
  leaderboardPosition: number;
}



// Types for leaderboard (used by useRealTimeStats hook)
interface _LeaderboardEntry {
  id: string;
  name: string;
  points: number;
  level: number;
  rank: number;
  isCurrentUser: boolean;
}

// Custom hook for dashboard data with retry mechanism
const useDashboardData = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);

  const fetchDashboardDataApi = useCallback(async () => {
    const response = await fetch('/api/dashboard', {
      credentials: 'include'
    });
    
    // Handle authentication redirects gracefully
    if (response.status === 307) {
      console.log('Dashboard: User not authenticated, redirecting to sign in');
      throw new Error('Please sign in to access your dashboard');
    }
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Authentication required');
      } else if (response.status >= 500) {
        throw new Error('Server error occurred');
      } else {
        throw new Error('Network connection error');
      }
    }
    
    const data = await response.json();
    setDashboardData(data);
    return data;
  }, []);

  // Memoize retry options so execute remains referentially stable to avoid repeated useEffect triggers
  const onRetryDashboard = useCallback((attempt: number, err: unknown) => {
    console.log(`Retrying dashboard fetch (${attempt}/3):`, err instanceof Error ? err.message : String(err));
  }, []);
  const dashboardRetryOptions = useMemo(() => ({
    maxRetries: 3,
    retryDelay: 1000,
    onRetry: onRetryDashboard,
  }), [onRetryDashboard]);

  const {
    execute: fetchDashboardData,
    isLoading: loading,
    error,
    retryCount,
    canRetry
  } = useApiRetry(fetchDashboardDataApi, dashboardRetryOptions);

  return { 
    dashboardData, 
    loading, 
    error, 
    fetchDashboardData, 
    retryCount, 
    canRetry 
  };
};

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { dashboardData, loading, error, fetchDashboardData, retryCount } = useDashboardData();
  const hasFetchedRef = useRef(false);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);

  // Screen reader and announcements
  const { announceNavigation, announceAction, announceError } = useScreenReader();
  const { announceCountChange, announceChange } = useDynamicAnnouncements();

  // Keyboard navigation setup
  const dashboardShortcuts = [
    ...GLOBAL_SHORTCUTS,
    {
      key: 'r',
      action: () => {
        handleRetry();
        announceAction('Refreshing dashboard data');
      },
      description: 'Refresh dashboard data',
      category: 'Dashboard'
    },
    {
      key: 'k',
      action: () => {
        setShowKeyboardHelp(true);
        announceAction('Opening keyboard shortcuts help');
      },
      description: 'Show keyboard shortcuts',
      category: 'Help'
    }
  ];

  useKeyboardNavigation({
    shortcuts: dashboardShortcuts,
    enableGlobalShortcuts: true,
    announceShortcuts: true
  });

  // Real-time statistics and leaderboard
  const {
    stats: realTimeStats,
    userStats: realTimeUserStats,
    leaderboard: realTimeLeaderboard,
    isStatsConnected,
    isLeaderboardConnected: _isLeaderboardConnected,
    statsError,
    leaderboardError: _leaderboardError,
    lastStatsUpdate,
    lastLeaderboardUpdate,
    reconnectStats,
    reconnectLeaderboard: _reconnectLeaderboard
  } = useRealTimeStats(true);

  // Ensure dashboard fetch only runs once after session is ready
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (DASHBOARD_AUTO_FETCH_DISABLED) return;

    if (!hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchDashboardData();
    }
  }, [session, status, router, fetchDashboardData]);

  const handleRetry = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Use real-time data when available, fallback to dashboard data
  const currentStats = realTimeStats || {
    totalVideos: dashboardData?.totalVideos || 0,
    totalUsers: dashboardData?.totalUsers || 0,
    totalViews: dashboardData?.totalViews || 0,
    nwaVideosCount: dashboardData?.nwaVideos?.length || 0,
    userVideoLinksCount: dashboardData?.userVideoLinks?.length || 0,
    timestamp: ''
  };

  const currentUserStats = realTimeUserStats || {
    points: dashboardData?.points || 0,
    level: dashboardData?.level || 1,
    totalPoints: dashboardData?.totalPoints || 0,
    leaderboardPosition: dashboardData?.leaderboardPosition || 1,
    timestamp: ''
  };

  const _currentLeaderboard = realTimeLeaderboard.length > 0 ? realTimeLeaderboard : [];
  const _leaderboardLoading = !dashboardData && realTimeLeaderboard.length === 0;

  // Determine the most recent update time
  const lastUpdateTime = useMemo(() => {
    return lastStatsUpdate || lastLeaderboardUpdate || new Date(0);
  }, [lastStatsUpdate, lastLeaderboardUpdate]);

  // State for formatted time
  const [formattedLastUpdateTime, setFormattedLastUpdateTime] = useState('');

  // Format time on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setFormattedLastUpdateTime(lastUpdateTime.toLocaleTimeString());
    }
  }, [lastUpdateTime]);

  // Announce data changes to screen readers
  useEffect(() => {
    if (dashboardData) {
      announceNavigation('Dashboard', 'Dashboard data loaded successfully');
    }
  }, [dashboardData, announceNavigation]);

  useEffect(() => {
    if (error) {
      announceError(error.message, 'Dashboard loading');
    }
  }, [error, announceError]);

  // Announce real-time updates
  useEffect(() => {
    if (realTimeStats) {
      announceCountChange('nwa-videos', realTimeStats.nwaVideosCount, 'NWA Videos');
      announceCountChange('total-videos', realTimeStats.totalVideos, 'Total Videos');
      announceCountChange('total-users', realTimeStats.totalUsers, 'Total Users');
      announceCountChange('user-links', realTimeStats.userVideoLinksCount, 'User Video Links');
    }
  }, [realTimeStats, announceCountChange]);

  useEffect(() => {
    if (realTimeUserStats) {
      announceCountChange('user-points', realTimeUserStats.totalPoints, 'Your Points');
      announceCountChange('user-level', realTimeUserStats.level, 'Your Level');
      announceCountChange('user-rank', realTimeUserStats.leaderboardPosition, 'Your Rank');
    }
  }, [realTimeUserStats, announceCountChange]);

  useEffect(() => {
    if (realTimeLeaderboard.length > 0) {
      announceChange('leaderboard', realTimeLeaderboard.length, (count) => `Leaderboard updated with ${count} entries`);
    }
  }, [realTimeLeaderboard, announceChange]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex justify-center items-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto" />
          <p className="text-white font-medium">Loading your gaming dashboard...</p>
        </div>
      </div>
    );
  }

  // If auto-fetch is disabled and we have no data yet, show a manual load prompt
  if (DASHBOARD_AUTO_FETCH_DISABLED && !dashboardData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <GamingCard className="max-w-md w-full text-center">
          <div className="text-6xl mb-4">⏯️</div>
          <h3 className="text-xl font-bold text-white mb-2">Dashboard auto-fetch is disabled</h3>
          <p className="text-gray-400 mb-4">
            In development, automatic data fetching is disabled to prevent runaway requests.
          </p>
          <button
            onClick={fetchDashboardData}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg font-semibold text-white transition-all duration-300 hover:scale-105"
          >
            Load Dashboard Data
          </button>
        </GamingCard>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <GamingCard className="max-w-md w-full text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-bold text-white mb-2">Oops! Something went wrong</h3>
          <p className="text-gray-400 mb-4">{error.message}</p>
          {retryCount > 0 && (
            <p className="text-sm text-gray-500 mb-4">Retry attempt: {retryCount}/3</p>
          )}
          <button
            onClick={handleRetry}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg font-semibold text-white transition-all duration-300 hover:scale-105"
          >
            Try Again
          </button>
        </GamingCard>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex">
      {/* Skip Link */}
      <a 
        href="#main-content" 
        className="skip-link"
        onFocus={(e) => e.target.style.top = '6px'}
        onBlur={(e) => e.target.style.top = '-40px'}
      >
        Skip to main content
      </a>
      
      {/* Sidebar - Hidden on mobile, shown on tablet+ */}
      <aside className="hidden lg:block" aria-label="Main navigation">
        <Sidebar session={session} />
      </aside>
      
      <div className="flex-1 overflow-auto">
        <header className="bg-gray-900 border-b border-gray-800 sticky top-0 z-10" role="banner">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4 flex justify-between items-center">
            <h1 className="text-xl sm:text-2xl font-bold text-white">Dashboard</h1>
            <nav className="flex items-center space-x-2 sm:space-x-4" aria-label="User actions">
              <NotificationBell />
            </nav>
          </div>
        </header>
        
        <main 
          id="main-content"
          className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8"
          role="main"
          aria-label="Dashboard content"
        >
          {/* Leaderboard section removed as requested */}

          {/* Statistics Section with Real-time Updates */}
          <DashboardErrorBoundary>
            <div className="mb-6 sm:mb-8">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-2 sm:space-y-0">
                <h2 className="text-lg sm:text-xl font-bold text-white">Statistics</h2>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      isStatsConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
                    )} />
                    <span className="text-xs sm:text-sm text-gray-400">
                      {isStatsConnected ? "Live" : "Offline"}
                    </span>
                  </div>
                  <div className="text-xs sm:text-sm text-gray-400">
                    Last updated: <span id="last-updated-time">{formattedLastUpdateTime || lastUpdateTime.toISOString()}</span>
                  </div>
                </div>
              </div>
              
              {loading ? (
                <LoadingSkeleton variant="stats" count={4} />
              ) : statsError ? (
                <div className="p-4 sm:p-6 text-center">
                  <div className="text-red-400 mb-2">{statsError}</div>
                  <button
                    onClick={reconnectStats}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
                  >
                    Reconnect Statistics
                  </button>
                </div>
              ) : (
                <>
                  <div className="grid mobile-2-cols sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
                    {/* System Statistics */}
                    <div className="bg-gray-800 rounded-lg p-3 sm:p-6 border border-gray-700 transition-all duration-300 hover:border-purple-500/50">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div className="mb-2 sm:mb-0">
                          <p className="text-xs sm:text-sm font-medium text-gray-400">NWA Videos</p>
                          <p className="text-lg sm:text-2xl font-bold text-white">{currentStats.nwaVideosCount}</p>
                        </div>
                        <div className="p-2 sm:p-3 rounded-full bg-purple-500/10 text-purple-400 self-end sm:self-auto">
                          <Video className="h-4 w-4 sm:h-6 sm:w-6" />
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800 rounded-lg p-3 sm:p-6 border border-gray-700 transition-all duration-300 hover:border-blue-500/50">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div className="mb-2 sm:mb-0">
                          <p className="text-xs sm:text-sm font-medium text-gray-400">Total Videos</p>
                          <p className="text-lg sm:text-2xl font-bold text-white">{currentStats.totalVideos}</p>
                        </div>
                        <div className="p-2 sm:p-3 rounded-full bg-blue-500/10 text-blue-400 self-end sm:self-auto">
                          <Video className="h-4 w-4 sm:h-6 sm:w-6" />
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800 rounded-lg p-3 sm:p-6 border border-gray-700 transition-all duration-300 hover:border-green-500/50">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div className="mb-2 sm:mb-0">
                          <p className="text-xs sm:text-sm font-medium text-gray-400">Total Users</p>
                          <p className="text-lg sm:text-2xl font-bold text-white">{currentStats.totalUsers}</p>
                        </div>
                        <div className="p-2 sm:p-3 rounded-full bg-green-500/10 text-green-400 self-end sm:self-auto">
                          <Users className="h-4 w-4 sm:h-6 sm:w-6" />
                        </div>
                      </div>
                    </div>

                    {/* User Links statistic - Only show for admins */}
                    {dashboardData?.isAdmin && (
                      <div className="bg-gray-800 rounded-lg p-3 sm:p-6 border border-gray-700 transition-all duration-300 hover:border-yellow-500/50">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                          <div className="mb-2 sm:mb-0">
                            <p className="text-xs sm:text-sm font-medium text-gray-400">User Links</p>
                            <p className="text-lg sm:text-2xl font-bold text-white">{currentStats.userVideoLinksCount}</p>
                          </div>
                          <div className="p-2 sm:p-3 rounded-full bg-yellow-500/10 text-yellow-400 self-end sm:self-auto">
                            <Eye className="h-4 w-4 sm:h-6 sm:w-6" />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* User Personal Statistics */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mt-4 sm:mt-6">
                    <div className="bg-gradient-to-br from-purple-900/20 to-purple-800/20 rounded-lg p-4 sm:p-6 border border-purple-500/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-xs sm:text-sm font-medium text-purple-300">Your Points</p>
                          <p className="text-lg sm:text-2xl font-bold text-white">{currentUserStats.totalPoints.toLocaleString()}</p>
                        </div>
                        <div className="p-2 sm:p-3 rounded-full bg-purple-500/20 text-purple-400">
                          <Star className="h-4 w-4 sm:h-6 sm:w-6" />
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-br from-cyan-900/20 to-cyan-800/20 rounded-lg p-4 sm:p-6 border border-cyan-500/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-xs sm:text-sm font-medium text-cyan-300">Your Level</p>
                          <p className="text-lg sm:text-2xl font-bold text-white">{currentUserStats.level}</p>
                        </div>
                        <div className="p-2 sm:p-3 rounded-full bg-cyan-500/20 text-cyan-400">
                          <Award className="h-4 w-4 sm:h-6 sm:w-6" />
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-br from-yellow-900/20 to-yellow-800/20 rounded-lg p-4 sm:p-6 border border-yellow-500/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-xs sm:text-sm font-medium text-yellow-300">Your Rank</p>
                          <p className="text-lg sm:text-2xl font-bold text-white">#{currentUserStats.leaderboardPosition}</p>
                        </div>
                        <div className="p-2 sm:p-3 rounded-full bg-yellow-500/20 text-yellow-400">
                          <Trophy className="h-4 w-4 sm:h-6 sm:w-6" />
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </DashboardErrorBoundary>

          {/* User Video Creation Form - Only show for admins */}
          {dashboardData?.isAdmin && (
            <VideoErrorBoundary>
              <div className="mb-6 sm:mb-8">
                <UserVideoCreationForm 
                  onVideoCreated={fetchDashboardData}
                  className="max-w-4xl mx-auto"
                />
              </div>
            </VideoErrorBoundary>
          )}


          {/* Admin Video Management Section - Only show for admins */}
          {dashboardData?.isAdmin && (
            <AdminErrorBoundary>
              <div className="mb-8">
                <AdminVideoManager />
              </div>
            </AdminErrorBoundary>
          )}

          {/* NWA Videos Section */}
          <VideoErrorBoundary>
            <div className="mb-8">
              {loading ? (
                <LoadingSkeleton variant="video" count={6} />
              ) : (
                <NwaVideoList initialVideos={dashboardData?.nwaVideos || []} />
              )}
            </div>
          </VideoErrorBoundary>

          {/* NWA Ebooks Section */}
          <VideoErrorBoundary>
            <div className="mb-8">
              {loading ? (
                <LoadingSkeleton variant="list" count={3} />
              ) : (
                <NwaEbookList userRole={session?.user?.role} />
              )}
            </div>
          </VideoErrorBoundary>

          {/* User Video Links Section - Only show for admins */}
          {dashboardData?.isAdmin && (
            <VideoErrorBoundary>
              <div>
                {loading ? (
                  <LoadingSkeleton variant="list" count={5} />
                ) : (
                  <UserVideoLinkList initialLinks={dashboardData?.userVideoLinks || []} />
                )}
              </div>
            </VideoErrorBoundary>
          )}
        </main>
      </div>


      {/* Keyboard Shortcuts Help */}
      <KeyboardShortcutsHelp
        shortcuts={dashboardShortcuts}
        isOpen={showKeyboardHelp}
        onClose={() => setShowKeyboardHelp(false)}
      />

      {/* Floating Keyboard Shortcuts Button */}
      <KeyboardShortcutsButton
        onClick={() => setShowKeyboardHelp(true)}
      />
    </div>
  );
}