'use client';

import React from 'react';
import HeroSection from '@/components/HeroSection';
import StatCard from '@/components/StatCard';
import LeaderboardCard from '@/components/LeaderboardCard';
import VideoCard from '@/components/VideoCard';
import GamingCard from '@/components/GamingCard';
import NotificationBell from '@/components/notification-bell';

export default function ModernUIPreview() {
  // Mock data for preview
  const mockVideos = [
    {
      id: '1',
      title: 'Epic Gaming Montage 2024',
      description: 'The most insane gaming moments compiled into one epic video!',
      createdAt: '2024-01-15T10:30:00Z',
      views: 15420,
      likes: 892
    },
    {
      id: '2',
      title: 'Pro Tips for Content Creation',
      description: 'Learn the secrets of viral content creation from industry experts.',
      createdAt: '2024-01-14T15:45:00Z',
      views: 8750,
      likes: 456
    },
    {
      id: '3',
      title: 'Behind the Scenes: Studio Setup',
      description: 'Take a tour of my gaming setup and learn how to optimize your streaming space.',
      createdAt: '2024-01-13T09:20:00Z',
      views: 12300,
      likes: 678
    }
  ];

  const mockLeaderboard = [
    { id: '1', name: 'ProGamer2024', points: 15420, level: 12, rank: 1, isCurrentUser: false },
    { id: '2', name: 'VideoMaster', points: 14890, level: 11, rank: 2, isCurrentUser: false },
    { id: '3', name: 'You', points: 12340, level: 10, rank: 3, isCurrentUser: true },
    { id: '4', name: 'ContentKing', points: 11200, level: 9, rank: 4, isCurrentUser: false },
    { id: '5', name: 'StreamQueen', points: 10850, level: 9, rank: 5, isCurrentUser: false },
  ];

  const handlePromoteVideo = (videoId: string) => {
    console.log('Promoting video:', videoId);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <HeroSection 
        title="New World Alliance"
        subtitle="Media Promotion"
        description="Promoting Peace and Prosperity through Visual Media. Share your content, engage with the community, and make an impact."
        userName="Content Creator"
      />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12">
        {/* Stats Section */}
        <section>
          <h2 className="text-3xl font-bold text-white mb-8 font-display">
            <span className="cyber-text">Share Stats</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              title="Points"
              value="12,340"
              icon={<div className="w-6 h-6 text-current">⭐</div>}
              gradient="purple-cyan"
              trend={{ value: 12, isPositive: true }}
              rank={1}
            />
            <StatCard
              title="Level"
              value="10"
              icon={<div className="w-6 h-6 text-current">⚡</div>}
              gradient="rose-amber"
              trend={{ value: 8, isPositive: true }}
              rank={2}
            />
            <StatCard
              title="Rank"
              value="#3"
              icon={<div className="w-6 h-6 text-current">🏆</div>}
              gradient="emerald-lime"
              trend={{ value: 5, isPositive: true }}
              rank={3}
            />
          </div>
        </section>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Videos Section */}
          <div className="lg:col-span-2 space-y-6">
            <h2 className="text-2xl font-bold text-white font-display">
              <span className="cyber-text">Featured Videos</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {mockVideos.map((video) => (
                <VideoCard 
                  key={video.id} 
                  video={video} 
                  onPromote={handlePromoteVideo} 
                />
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Leaderboard */}
            <LeaderboardCard 
              entries={mockLeaderboard}
              currentUserRank={3}
            />
            
            {/* Notification Bell Demo */}
            <GamingCard>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-white font-display">
                  🔔 Notifications
                </h3>
                <NotificationBell />
              </div>
              <p className="text-sm text-gray-400">
                Stay updated with achievements and promotions
              </p>
            </GamingCard>
            
            {/* Quick Actions */}
            <GamingCard>
              <h3 className="text-lg font-bold text-white mb-4 font-display">
                🚀 Quick Actions
              </h3>
              <div className="space-y-3">
                <button className="w-full p-3 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 border border-purple-500/30 rounded-lg text-white font-medium hover:bg-gradient-to-r hover:from-purple-600/30 hover:to-cyan-600/30 transition-all duration-300">
                  📤 Upload New Video
                </button>
                <button className="w-full p-3 bg-gradient-to-r from-emerald-600/20 to-lime-600/20 border border-emerald-500/30 rounded-lg text-white font-medium hover:bg-gradient-to-r hover:from-emerald-600/30 hover:to-lime-600/30 transition-all duration-300">
                  📊 View Analytics
                </button>
                <button className="w-full p-3 bg-gradient-to-r from-rose-600/20 to-amber-600/20 border border-rose-500/30 rounded-lg text-white font-medium hover:bg-gradient-to-r hover:from-rose-600/30 hover:to-amber-600/30 transition-all duration-300">
                  🎯 Boost Campaign
                </button>
              </div>
            </GamingCard>
          </div>
        </div>

        {/* Features Showcase */}
        <section>
          <h2 className="text-3xl font-bold text-white mb-8 font-display text-center">
            <span className="cyber-text">Gaming Features</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { icon: "🎮", title: "Gaming UI", desc: "Modern gaming-inspired interface" },
              { icon: "🏆", title: "Leaderboards", desc: "Compete with other creators" },
              { icon: "⚡", title: "Level System", desc: "Progress through experience levels" },
              { icon: "🎯", title: "Achievements", desc: "Unlock badges and rewards" },
              { icon: "🚀", title: "Boost System", desc: "Amplify your content reach" },
              { icon: "📊", title: "Real-time Stats", desc: "Track performance instantly" }
            ].map((feature, index) => (
              <GamingCard key={index} floating={index % 2 === 0}>
                <div className="text-center space-y-4">
                  <div className="text-4xl">{feature.icon}</div>
                  <h3 className="text-lg font-bold text-white">{feature.title}</h3>
                  <p className="text-gray-400 text-sm">{feature.desc}</p>
                </div>
              </GamingCard>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}