@import "tailwindcss";

:root {
  --background: #0F0F23;
  --foreground: #FFFFFF;
  --primary-purple: #8B5CF6;
  --primary-cyan: #06B6D4;
  --primary-emerald: #10B981;
  --primary-amber: #F59E0B;
  --primary-rose: #EC4899;
  --primary-indigo: #6366F1;
  --accent-lime: #84CC16;
  --dark-bg: #1F2937;
  --card-bg: rgba(255, 255, 255, 0.05);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary-purple: var(--primary-purple);
  --color-primary-cyan: var(--primary-cyan);
  --color-primary-emerald: var(--primary-emerald);
  --color-primary-amber: var(--primary-amber);
  --color-primary-rose: var(--primary-rose);
  --color-primary-indigo: var(--primary-indigo);
  --color-accent-lime: var(--accent-lime);
  --color-dark-bg: var(--dark-bg);
  --color-card-bg: var(--card-bg);
  --color-glass-bg: var(--glass-bg);
  --color-glass-border: var(--glass-border);
  --font-sans: var(--font-geist-sans), system-ui, sans-serif;
  --font-display: var(--font-geist-sans), system-ui, sans-serif;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #FAFAFA;
    --foreground: #1F2937;
    --card-bg: rgba(255, 255, 255, 0.8);
    --glass-bg: rgba(255, 255, 255, 0.6);
    --glass-border: rgba(255, 255, 255, 0.3);
  }
}

@utility gaming-gradient {
  background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-cyan) 50%, var(--primary-emerald) 100%);
}

@utility gaming-gradient-alt {
  background: linear-gradient(135deg, var(--primary-rose) 0%, var(--primary-amber) 50%, var(--accent-lime) 100%);
}

@utility glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
}

@utility neon-glow {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(139, 92, 246, 0.1);
}

@utility cyber-text {
  background: linear-gradient(135deg, var(--primary-cyan), var(--primary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@utility pulse-animation {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.6), 0 0 60px rgba(6, 182, 212, 0.3);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@utility float-animation {
  animation: float 3s ease-in-out infinite;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  margin: auto;
}

.gaming-bg {
  background: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);
}

.leaderboard-item {
  transition: all 0.3s ease;
}

.leaderboard-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }
  
  .hover\:scale-105:active {
    transform: scale(0.95);
  }
  
  button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

/* Improved touch targets for mobile */
@utility touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Line clamping utilities */
@utility line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

@utility line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

@utility line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Smooth scrolling for mobile */
@media (max-width: 768px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .overflow-x-auto::-webkit-scrollbar {
    display: none;
  }
}

/* Mobile-first responsive grid improvements */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-2.mobile-2-cols {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Accessibility utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode support */
.theme-high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --primary-purple: #ffffff;
  --primary-cyan: #ffffff;
  --primary-emerald: #ffffff;
  --primary-amber: #ffff00;
  --primary-rose: #ffffff;
  --primary-indigo: #ffffff;
  --accent-lime: #ffff00;
  --dark-bg: #000000;
  --card-bg: #000000;
  --glass-bg: #000000;
  --glass-border: #ffffff;
}

.theme-high-contrast .bg-gray-800,
.theme-high-contrast .bg-gray-900 {
  background-color: #000000 !important;
  border: 2px solid #ffffff !important;
}

.theme-high-contrast .bg-white\/10,
.theme-high-contrast .bg-white\/20 {
  background-color: #000000 !important;
  border: 2px solid #ffffff !important;
}

.theme-high-contrast .text-gray-400,
.theme-high-contrast .text-gray-500,
.theme-high-contrast .text-gray-300 {
  color: #ffffff !important;
}

.theme-high-contrast .text-white {
  color: #ffffff !important;
}

.theme-high-contrast button,
.theme-high-contrast .button,
.theme-high-contrast a[role="button"] {
  border: 2px solid #ffffff !important;
  background-color: #000000 !important;
  color: #ffffff !important;
}

.theme-high-contrast button:hover,
.theme-high-contrast .button:hover,
.theme-high-contrast a[role="button"]:hover {
  background-color: #ffffff !important;
  color: #000000 !important;
}

.theme-high-contrast button:focus,
.theme-high-contrast .button:focus,
.theme-high-contrast a:focus,
.theme-high-contrast input:focus,
.theme-high-contrast textarea:focus,
.theme-high-contrast select:focus {
  outline: 3px solid #ffff00 !important;
  outline-offset: 2px !important;
}

.theme-high-contrast .glass-card {
  background: #000000 !important;
  border: 2px solid #ffffff !important;
  backdrop-filter: none !important;
}

.theme-high-contrast .border-gray-800,
.theme-high-contrast .border-gray-700,
.theme-high-contrast .border-white\/10 {
  border-color: #ffffff !important;
}

.theme-high-contrast .divide-gray-800 > :not([hidden]) ~ :not([hidden]) {
  border-color: #ffffff !important;
}

.theme-high-contrast .bg-gradient-to-r,
.theme-high-contrast .bg-gradient-to-br,
.theme-high-contrast .gaming-gradient,
.theme-high-contrast .gaming-gradient-alt {
  background: #000000 !important;
  border: 2px solid #ffffff !important;
}

.theme-high-contrast .cyber-text,
.theme-high-contrast .bg-gradient-to-r.from-purple-400.via-cyan-400.to-purple-400.bg-clip-text.text-transparent {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
  color: #ffffff !important;
}

.theme-high-contrast .shadow-lg,
.theme-high-contrast .neon-glow,
.theme-high-contrast .pulse-animation {
  box-shadow: none !important;
}

/* Ensure proper contrast for interactive elements */
.theme-high-contrast input,
.theme-high-contrast textarea,
.theme-high-contrast select {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #ffffff !important;
}

.theme-high-contrast input::placeholder,
.theme-high-contrast textarea::placeholder {
  color: #cccccc !important;
}

/* High contrast mode for media queries */
@media (prefers-contrast: high) {
  :root:not(.theme-default) {
    --background: #000000;
    --foreground: #ffffff;
    --card-bg: #000000;
    --glass-bg: #000000;
    --glass-border: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-spin,
  .animate-pulse,
  .float-animation,
  .pulse-animation {
    animation: none !important;
  }
}

/* Focus indicators */
.focus-visible:focus-visible,
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Skip link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000000;
  color: #ffffff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}