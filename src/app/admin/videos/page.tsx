import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import Sidebar from '@/components/Sidebar';

import Image from 'next/image';
import FeatureToggleButton from '@/components/FeatureToggleButton';

async function getAllVideos() {
  const videos = await prisma.video.findMany({
    orderBy: { createdAt: 'desc' },
    include: { user: { select: { name: true } } },
  });
  return videos;
}

export default async function AdminVideosPage() {
  const session = await getServerSession(authOptions);
  const videos = await getAllVideos();

  return (
    <div className="min-h-screen bg-background flex">
      <Sidebar session={session} />
      <div className="flex-1 overflow-auto">
        <header className="bg-gray-900 border-b border-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <h1 className="text-2xl font-bold text-white">Admin: Manage Videos</h1>
          </div>
        </header>
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full text-left divide-y divide-gray-800">
                <thead className="bg-gray-800/50">
                  <tr>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Video</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Author</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Platform</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Featured</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {videos.map((video) => (
                    <tr key={video.id} className="hover:bg-gray-800/50 transition-colors">
                      <td className="px-6 py-4 text-sm font-medium text-white flex items-center">
                        {video.thumbnailUrl && <Image src={video.thumbnailUrl} alt={video.title} width={80} height={45} className="rounded-md object-cover mr-4"/>}
                        {video.title}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-300">{video.user.name}</td>
                      <td className="px-6 py-4 text-sm text-gray-300">{video.platform}</td>
                      <td className="px-6 py-4 text-sm text-gray-300">
                        <FeatureToggleButton videoId={video.id} isFeatured={video.isFeatured} />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
