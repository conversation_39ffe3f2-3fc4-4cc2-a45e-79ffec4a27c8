import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import AdminAuditLogs from '@/components/AdminAuditLogs';

export default async function AdminAuditLogsPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== 'ADMIN') {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminAuditLogs />
    </div>
  );
}

export const metadata = {
  title: 'Audit Logs - Admin Dashboard',
  description: 'View system audit logs and user activity',
};