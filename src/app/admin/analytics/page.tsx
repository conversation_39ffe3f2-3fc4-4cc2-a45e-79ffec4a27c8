'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Sidebar from '@/components/Sidebar';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';

interface PlatformMetrics {
  platform: string;
  totalLikes: number;
  totalShares: number;
  totalCompletions: number;
  totalEngagements: number;
  uniqueUsers: number;
}

interface UserBehaviorMetrics {
  totalVideos: number;
  totalCompletions: number;
  totalSkips: number;
  completionRate: number;
  skipRate: number;
  averageEngagementsPerVideo: number;
}

interface EngagementTrend {
  date: string;
  likes: number;
  shares: number;
  completions: number;
}

interface TopVideo {
  id: string;
  title: string;
  totalEngagements: number;
  likes: number;
  shares: number;
  completions: number;
}

interface AnalyticsData {
  platformMetrics: PlatformMetrics[];
  userBehavior: UserBehaviorMetrics;
  engagementTrends: EngagementTrend[];
  topVideos: TopVideo[];
  totalUsers: number;
  activeUsers: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function AdminAnalyticsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState(30);

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/admin/analytics?days=${timeRange}`);
      
      if (!response.ok) {
        if (response.status === 403) {
          setError('Access denied. Admin privileges required.');
          return;
        }
        throw new Error('Failed to fetch analytics data');
      }
      
      const data = await response.json();
      setAnalyticsData(data);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    fetchAnalyticsData();
  }, [session, status, router, fetchAnalyticsData]);

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-background flex">
        <Sidebar session={session} />
        <div className="flex-1 overflow-auto">
          <header className="bg-gray-900 border-b border-gray-800">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <h1 className="text-2xl font-bold text-white">Analytics Dashboard</h1>
            </div>
          </header>
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-white">Loading analytics...</div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex">
        <Sidebar session={session} />
        <div className="flex-1 overflow-auto">
          <header className="bg-gray-900 border-b border-gray-800">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <h1 className="text-2xl font-bold text-white">Analytics Dashboard</h1>
            </div>
          </header>
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-4">
              <div className="text-red-400">{error}</div>
              <button
                onClick={fetchAnalyticsData}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return null;
  }

  // Prepare data for charts
  const platformChartData = analyticsData.platformMetrics.map(platform => ({
    name: platform.platform.charAt(0).toUpperCase() + platform.platform.slice(1),
    likes: platform.totalLikes,
    shares: platform.totalShares,
    completions: platform.totalCompletions,
    users: platform.uniqueUsers,
  }));

  const behaviorPieData = [
    { name: 'Completions', value: analyticsData.userBehavior.totalCompletions },
    { name: 'Skips', value: analyticsData.userBehavior.totalSkips },
  ];

  const engagementTrendData = analyticsData.engagementTrends.map(trend => ({
    date: new Date(trend.date).toLocaleDateString(),
    likes: trend.likes,
    shares: trend.shares,
    completions: trend.completions,
  }));

  return (
    <div className="min-h-screen bg-background flex">
      <Sidebar session={session} />
      <div className="flex-1 overflow-auto">
        <header className="bg-gray-900 border-b border-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-white">Analytics Dashboard</h1>
              <div className="flex items-center space-x-4">
                <label className="text-white text-sm">Time Range:</label>
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(Number(e.target.value))}
                  className="bg-gray-800 text-white border border-gray-700 rounded px-3 py-1"
                >
                  <option value={7}>Last 7 days</option>
                  <option value={30}>Last 30 days</option>
                  <option value={90}>Last 90 days</option>
                </select>
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Total Users</h3>
              <p className="text-3xl font-bold text-blue-400">{analyticsData.totalUsers}</p>
              <p className="text-sm text-gray-400">
                {analyticsData.activeUsers} active in last {timeRange} days
              </p>
            </div>
            
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Total Videos</h3>
              <p className="text-3xl font-bold text-green-400">{analyticsData.userBehavior.totalVideos}</p>
              <p className="text-sm text-gray-400">Published videos</p>
            </div>
            
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Completion Rate</h3>
              <p className="text-3xl font-bold text-yellow-400">
                {analyticsData.userBehavior.completionRate.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-400">
                {analyticsData.userBehavior.totalCompletions} completions
              </p>
            </div>
            
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Avg Engagements</h3>
              <p className="text-3xl font-bold text-purple-400">
                {analyticsData.userBehavior.averageEngagementsPerVideo.toFixed(1)}
              </p>
              <p className="text-sm text-gray-400">Per video</p>
            </div>
          </div>

          {/* Platform Performance Chart */}
          <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 mb-8">
            <h2 className="text-xl font-bold text-white mb-4">Platform Performance Comparison</h2>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={platformChartData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="name" stroke="#9CA3AF" />
                  <YAxis stroke="#9CA3AF" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1F2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Bar dataKey="likes" fill="#3B82F6" name="Likes" />
                  <Bar dataKey="shares" fill="#10B981" name="Shares" />
                  <Bar dataKey="completions" fill="#F59E0B" name="Completions" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* User Behavior Pie Chart */}
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
              <h2 className="text-xl font-bold text-white mb-4">User Behavior (Completion vs Skip)</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={behaviorPieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${percent !== undefined ? (percent * 100).toFixed(0) : 'N/A'}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {behaviorPieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1F2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Platform Users Chart */}
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
              <h2 className="text-xl font-bold text-white mb-4">Unique Users by Platform</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={platformChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="name" stroke="#9CA3AF" />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1F2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                    <Bar dataKey="users" fill="#8B5CF6" name="Unique Users" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Engagement Trends */}
          <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 mb-8">
            <h2 className="text-xl font-bold text-white mb-4">Engagement Trends Over Time</h2>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={engagementTrendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="date" stroke="#9CA3AF" />
                  <YAxis stroke="#9CA3AF" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1F2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="likes" stroke="#3B82F6" name="Likes" strokeWidth={2} />
                  <Line type="monotone" dataKey="shares" stroke="#10B981" name="Shares" strokeWidth={2} />
                  <Line type="monotone" dataKey="completions" stroke="#F59E0B" name="Completions" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Top Videos Table */}
          <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
            <h2 className="text-xl font-bold text-white mb-4">Top Performing Videos</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full text-left divide-y divide-gray-800">
                <thead className="bg-gray-800/50">
                  <tr>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Video Title</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Total Engagements</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Likes</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Shares</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Completions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {analyticsData.topVideos.map((video, index) => (
                    <tr key={video.id} className="hover:bg-gray-800/50 transition-colors">
                      <td className="px-6 py-4 text-sm font-medium text-white">
                        <div className="flex items-center">
                          <span className="mr-2 text-gray-400">#{index + 1}</span>
                          {video.title}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-300 font-semibold">
                        {video.totalEngagements}
                      </td>
                      <td className="px-6 py-4 text-sm text-blue-400">{video.likes}</td>
                      <td className="px-6 py-4 text-sm text-green-400">{video.shares}</td>
                      <td className="px-6 py-4 text-sm text-yellow-400">{video.completions}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}