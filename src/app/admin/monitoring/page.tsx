import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import AdminMonitoringDashboard from '@/components/AdminMonitoringDashboard';

export default async function AdminMonitoringPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== 'ADMIN') {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminMonitoringDashboard />
    </div>
  );
}

export const metadata = {
  title: 'System Monitoring - Admin Dashboard',
  description: 'Monitor system health, performance metrics, and alerts',
};