import { prisma } from '@/lib/prisma';
import { Users } from 'lucide-react';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import Sidebar from '@/components/Sidebar';
import CreateUserButton from '@/components/CreateUserButton';
import UserActions from '@/components/UserActions';

async function getUsers() {
  const users = await prisma.user.findMany({
    orderBy: {
      createdAt: 'desc',
    },
  });
  return users;
}

export default async function AdminUsersPage() {
  const session = await getServerSession(authOptions);
  const users = await getUsers();

  return (
    <div className="min-h-screen bg-background flex">
      <Sidebar session={session} />
      <div className="flex-1 overflow-auto">
        <header className="bg-gray-900 border-b border-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <h1 className="text-2xl font-bold text-white">Admin: Manage Users</h1>
          </div>
        </header>
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
            <div className="p-6 border-b border-gray-800">
              <h2 className="text-xl font-bold text-white flex items-center">
                <Users className="mr-3 h-6 w-6" />
                All Users ({users.length})
              </h2>
<div className="mt-[19px]">
  <CreateUserButton />
</div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full text-left divide-y divide-gray-800">
                <thead className="bg-gray-800/50">
                  <tr>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Name</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Email</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Role</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Joined</th>
                    <th className="px-6 py-3 text-xs font-bold text-gray-400">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-800/50 transition-colors">
                      <td className="px-6 py-4 text-sm font-medium text-white">{user.name || 'N/A'}</td>
                      <td className="px-6 py-4 text-sm text-gray-300">{user.email}</td>
                      <td className="px-6 py-4 text-sm text-gray-300">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${user.role === 'ADMIN' ? 'bg-purple-500/20 text-purple-300' : 'bg-gray-700 text-gray-300'}`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-400">{new Date(user.createdAt).toLocaleDateString()}</td>
                      <td className="px-6 py-4 text-sm">
                        <UserActions
                          userId={user.id}
                          userName={user.name ?? undefined}
                          userEmail={user.email}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
