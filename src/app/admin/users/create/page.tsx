'use client';
import { useState } from 'react';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export default function CreateUserPage() {
  const searchParams = useSearchParams();
  const editId = searchParams.get('edit');
  const [form, setForm] = useState({ name: '', email: '', password: '', role: 'USER' });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (editId) {
      fetch(`/api/admin/users/${editId}`)
        .then(res => res.json())
        .then(data => {
          setForm({
            name: data.name || '',
            email: data.email || '',
            password: '',
            role: data.role || 'USER'
          });
        })
        .catch(() => setError('Failed to load user data'));
    }
  }, [editId]);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError('');
    setSuccess('');
    try {
      let res, data;
      if (editId) {
        res = await fetch(`/api/admin/users/${editId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...form, password: undefined }),
        });
        data = await res.json();
        if (res.ok) {
          setSuccess('User updated successfully!');
        } else {
          setError(data.message || 'Error updating user');
        }
      } else {
        res = await fetch('/api/admin/users', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(form),
        });
        data = await res.json();
        if (res.ok) {
          setSuccess('User created successfully!');
          setForm({ name: '', email: '', password: '', role: 'USER' });
        } else {
          setError(data.message || 'Error creating user');
        }
      }
    } catch (_err) {
      setError('Network error');
    }
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <form className="bg-gray-900 p-8 rounded-lg shadow-lg w-full max-w-md" onSubmit={handleSubmit}>
        <h2 className="text-2xl font-bold text-white mb-6">{editId ? 'Edit User' : 'Create User'}</h2>
        {error && <div className="text-red-500 mb-4">{error}</div>}
        {success && <div className="text-green-500 mb-4">{success}</div>}
        <input
          type="text"
          placeholder="Name"
          className="w-full mb-4 px-4 py-2 rounded bg-gray-800 text-white"
          value={form.name}
          onChange={e => setForm({ ...form, name: e.target.value })}
          required
        />
        <input
          type="email"
          placeholder="Email"
          className="w-full mb-4 px-4 py-2 rounded bg-gray-800 text-white"
          value={form.email}
          onChange={e => setForm({ ...form, email: e.target.value })}
          required
        />
        <input
          type="password"
          placeholder="Password"
          className="w-full mb-4 px-4 py-2 rounded bg-gray-800 text-white"
          value={form.password}
          onChange={e => setForm({ ...form, password: e.target.value })}
          required
        />
        <select
          className="w-full mb-6 px-4 py-2 rounded bg-gray-800 text-white"
          value={form.role}
          onChange={e => setForm({ ...form, role: e.target.value })}
        >
          <option value="USER">User</option>
          <option value="ADMIN">Admin</option>
        </select>
        <button
          type="submit"
          className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded w-full"
        >
          {editId ? 'Update User' : 'Create User'}
        </button>
      </form>
    </div>
  );
}