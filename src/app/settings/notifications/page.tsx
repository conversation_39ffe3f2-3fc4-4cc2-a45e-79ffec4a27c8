'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import NotificationSettings from '@/components/NotificationSettings';
import { DashboardErrorBoundary } from '@/components/ErrorBoundary';

export default function NotificationSettingsPage() {
  const { status } = useSession();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return null; // Will be redirected
  }

  return (
    <DashboardErrorBoundary>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Settings</h1>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <NotificationSettings />
        </div>
      </div>
    </DashboardErrorBoundary>
  );
}