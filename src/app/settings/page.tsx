'use client';

import { Ch<PERSON>ronR<PERSON>, <PERSON> } from 'lucide-react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import Sidebar from '@/components/Sidebar';
import ThemeToggle from '@/components/ThemeToggle';

const settingsOptions = [
  {
    name: 'Notifications',
    description: 'Manage your notification preferences',
    href: '/settings/notifications',
    icon: Bell,
  },
  // Add more settings options here in the future
];

export default function SettingsPage() {
  const { data: session } = useSession();

  return (
    <div className="min-h-screen bg-background flex">
      <Sidebar session={session} />
      <div className="flex-1 overflow-auto">
        <header className="bg-gray-900 border-b border-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <h1 className="text-2xl font-bold text-white">Settings</h1>
          </div>
        </header>
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-6">
            {/* Theme Settings */}
            <div>
              <h2 className="text-lg font-medium text-white mb-4">Appearance</h2>
              <ThemeToggle />
            </div>

            {/* Other Settings */}
            <div>
              <h2 className="text-lg font-medium text-white mb-4">General Settings</h2>
              <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
                <ul className="divide-y divide-gray-800">
                  {settingsOptions.map((option) => (
                    <li key={option.name}>
                      <Link href={option.href} className="block hover:bg-gray-800/50 transition-colors">
                        <div className="flex items-center p-4 sm:p-6">
                          <div className="flex-shrink-0">
                            <option.icon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                          </div>
                          <div className="flex-1 min-w-0 ml-4">
                            <p className="text-base font-medium text-white truncate">{option.name}</p>
                            <p className="text-sm text-gray-400 truncate">{option.description}</p>
                          </div>
                          <div className="ml-4 flex-shrink-0">
                            <ChevronRight className="h-5 w-5 text-gray-400" aria-hidden="true" />
                          </div>
                        </div>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
