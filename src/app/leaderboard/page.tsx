'use client';

import { useEffect, useState } from 'react';

interface LeaderboardEntry {
  id: string;
  name: string;
  points: number;
  level: number;
  rank: number;
  isCurrentUser: boolean;
}

export default function LeaderboardPage() {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchLeaderboard() {
      setLoading(true);
      try {
        const res = await fetch('/api/leaderboard');
        if (!res.ok) throw new Error('Failed to fetch leaderboard');
        const data = await res.json();
        setLeaderboard(data.leaderboard || []);
        setError(null);
      } catch (_e) {
        setError('Could not load leaderboard');
      } finally {
        setLoading(false);
      }
    }
    fetchLeaderboard();
  }, []);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full shadow-lg">
            <span className="text-lg">🥇</span>
          </div>
        );
      case 2:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-gray-300 to-gray-500 rounded-full shadow-lg">
            <span className="text-lg">🥈</span>
          </div>
        );
      case 3:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-amber-500 to-amber-700 rounded-full shadow-lg">
            <span className="text-lg">🥉</span>
          </div>
        );
      case 4:
        return (
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-orange-600 to-red-700 rounded-full shadow-lg">
            <span className="text-lg">🏅</span>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 text-center text-sm font-bold text-gray-400 flex items-center justify-center">
            #{rank}
          </div>
        );
    }
  };

  const getRankClass = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-l-4 border-yellow-500';
      case 2:
        return 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-l-4 border-gray-400';
      case 3:
        return 'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border-l-4 border-amber-500';
      case 4:
        return 'bg-gradient-to-r from-orange-500/20 to-red-600/20 border-l-4 border-orange-500';
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
      {/* Skip Link */}
      <a 
        href="#main-content" 
        className="skip-link"
        onFocus={(e) => e.target.style.top = '6px'}
        onBlur={(e) => e.target.style.top = '-40px'}
      >
        Skip to main content
      </a>
      
      <div className="max-w-7xl mx-auto">
        <header className="text-center mb-12">
          <h1 
            id="page-title"
            className="text-4xl md:text-5xl font-bold mb-4 font-display"
          >
            <span className="cyber-text">Leaderboard</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Top content creators and media professionals in our community
          </p>
        </header>
        
        <main 
          id="main-content"
          className="max-w-4xl mx-auto bg-gray-900 rounded-lg border border-gray-800 overflow-hidden mb-8"
          role="main"
          aria-labelledby="leaderboard-table-title"
        >
          <header className="px-6 py-4 border-b border-gray-800 flex justify-between items-center">
            <h2 
              id="leaderboard-table-title"
              className="text-xl font-bold text-white"
            >
              Leaderboard
            </h2>
          </header>
          {loading ? (
            <div 
              className="p-6 text-center text-gray-400"
              role="status"
              aria-live="polite"
            >
              Loading leaderboard...
            </div>
          ) : error ? (
            <div 
              className="p-6 text-center text-red-400"
              role="alert"
              aria-live="assertive"
            >
              {error}
            </div>
          ) : leaderboard.length > 0 ? (
            <div className="overflow-x-auto">
              <table 
                className="min-w-full text-left divide-y divide-gray-800"
                role="table"
                aria-label="Community leaderboard showing top content creators"
              >
                <thead>
                  <tr>
                    <th 
                      className="px-6 py-3 text-xs font-bold text-gray-400"
                      scope="col"
                      aria-sort="ascending"
                    >
                      Rank
                    </th>
                    <th 
                      className="px-6 py-3 text-xs font-bold text-gray-400"
                      scope="col"
                    >
                      Name
                    </th>
                    <th 
                      className="px-6 py-3 text-xs font-bold text-gray-400"
                      scope="col"
                      aria-sort="descending"
                    >
                      Points
                    </th>
                    <th 
                      className="px-6 py-3 text-xs font-bold text-gray-400"
                      scope="col"
                    >
                      Level
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {leaderboard.map((entry) => (
                    <tr 
                      key={entry.id} 
                      className={`${getRankClass(entry.rank)} ${
                        entry.isCurrentUser 
                          ? 'bg-gradient-to-r from-purple-900/30 to-cyan-900/30 font-bold border-l-4 border-purple-500 shadow-lg' 
                          : ''
                      } transition-all duration-300 hover:bg-white/5`}
                      aria-label={`${entry.name}, rank ${entry.rank}, ${entry.points.toLocaleString()} points, level ${entry.level}${entry.isCurrentUser ? ' (your position)' : ''}`}
                    >
                      <td 
                        className="px-6 py-4 flex items-center"
                        aria-label={`Rank ${entry.rank}`}
                      >
                        {getRankIcon(entry.rank)}
                      </td>
                      <td className="px-6 py-4">
                        <span className={entry.isCurrentUser ? 'text-cyan-300' : 'text-white'}>
                          {entry.name}
                        </span>
                        {entry.isCurrentUser && (
                          <span 
                            className="ml-2 px-2 py-1 bg-purple-600 text-xs rounded-full"
                            aria-label="Your position"
                          >
                            You
                          </span>
                        )}
                      </td>
                      <td 
                        className="px-6 py-4 font-semibold"
                        aria-label={`${entry.points.toLocaleString()} points`}
                      >
                        {entry.points.toLocaleString()}
                      </td>
                      <td 
                        className="px-6 py-4"
                        aria-label={`Level ${entry.level}`}
                      >
                        {entry.level}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div 
              className="p-6 text-center text-gray-400"
              role="status"
            >
              No leaderboard data.
            </div>
          )}
        </main>
        
        <footer className="mt-12 text-center">
          <p className="text-gray-400 mb-4">
            Want to climb the ranks? Engage with videos by liking, sharing, and watching content!
          </p>
          <p className="text-sm text-gray-500">
            Earn points through video engagement and build your community presence.
          </p>
        </footer>
      </div>
    </div>
  );
}
