'use client';

import React from 'react';
import MultiPlatformVideoCard from '@/components/MultiPlatformVideoCard';
import { useVideoInteractions } from '@/hooks/useVideoInteractions';

const demoVideos = [
  {
    id: 'demo-1',
    title: 'Multi-Platform Demo Video 1',
    description: 'This is a demo video with YouTube, TikTok, and Rumble links to test the multi-platform functionality.',
    platforms: {
      youtube: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      tiktok: 'https://www.tiktok.com/@user/video/123456789',
      rumble: 'https://rumble.com/demo-video',
    },
    thumbnailUrl: 'https://via.placeholder.com/400x225/6366f1/ffffff?text=Demo+Video+1',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 'demo-2',
    title: 'YouTube Only Demo',
    description: 'This demo video only has a YouTube link to test single platform functionality.',
    platforms: {
      youtube: 'https://www.youtube.com/watch?v=jNQXAC9IVRw',
    },
    thumbnailUrl: 'https://via.placeholder.com/400x225/10b981/ffffff?text=YouTube+Only',
    createdAt: '2024-01-14T15:45:00Z',
  },
  {
    id: 'demo-3',
    title: 'TikTok and Rumble Demo',
    description: 'This demo video has TikTok and Rumble links but no YouTube link.',
    platforms: {
      tiktok: 'https://www.tiktok.com/@user/video/987654321',
      rumble: 'https://rumble.com/another-demo-video',
    },
    thumbnailUrl: 'https://via.placeholder.com/400x225/f59e0b/ffffff?text=TikTok+%26+Rumble',
    createdAt: '2024-01-13T09:15:00Z',
  },
  {
    id: 'demo-4',
    title: 'No Platform Links Demo',
    description: 'This demo video has no platform links to test the fallback message.',
    platforms: {},
    thumbnailUrl: 'https://via.placeholder.com/400x225/ef4444/ffffff?text=No+Platforms',
    createdAt: '2024-01-12T14:20:00Z',
  },
];

export default function DemoVideoCardPage() {
  const videoIds = demoVideos.map(video => video.id);
  const { userInteractions, handlePlatformClick, isLoading, error } = useVideoInteractions(videoIds);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Multi-Platform Video Card Demo
          </h1>
          <p className="text-xl text-gray-300 mb-2">
            Testing the new MultiPlatformVideoCard component with different platform combinations
          </p>
          <p className="text-sm text-gray-400">
            Click the like and share buttons to test platform-specific interactions
          </p>
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-8 text-center">
            <p className="text-red-300">Error loading interactions: {error}</p>
          </div>
        )}

        {isLoading && (
          <div className="text-center mb-8">
            <div className="inline-flex items-center space-x-2 text-gray-300">
              <div className="w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
              <span>Loading user interactions...</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {demoVideos.map((video) => (
            <div key={video.id} className="transform transition-all duration-300 hover:scale-105">
              <MultiPlatformVideoCard
                video={video}
                onPlatformClick={(platform, action) => handlePlatformClick(video.id, platform, action)}
                userInteractions={userInteractions[video.id] || {}}
              />
            </div>
          ))}
        </div>

        <div className="mt-16 bg-gray-800/50 rounded-lg p-8 border border-gray-700">
          <h2 className="text-2xl font-bold text-white mb-4">Component Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-300">
            <div>
              <h3 className="text-lg font-semibold text-purple-300 mb-2">Platform Support</h3>
              <ul className="space-y-1 text-sm">
                <li>• YouTube with custom share URLs</li>
                <li>• TikTok with platform-specific styling</li>
                <li>• Rumble with proper link handling</li>
                <li>• Graceful fallback for missing platforms</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-purple-300 mb-2">User Experience</h3>
              <ul className="space-y-1 text-sm">
                <li>• Button state management (disabled after click)</li>
                <li>• Loading states with spinners</li>
                <li>• Platform-specific icons and colors</li>
                <li>• Opens links in new tabs</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-purple-300 mb-2">Technical Features</h3>
              <ul className="space-y-1 text-sm">
                <li>• Optimistic UI updates</li>
                <li>• Error handling with graceful degradation</li>
                <li>• Engagement tracking via API</li>
                <li>• Responsive design</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-purple-300 mb-2">Requirements Met</h3>
              <ul className="space-y-1 text-sm">
                <li>• ✅ Platform-specific like/share buttons</li>
                <li>• ✅ Button state management</li>
                <li>• ✅ Platform icons and styling</li>
                <li>• ✅ Opens URLs in new tabs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}