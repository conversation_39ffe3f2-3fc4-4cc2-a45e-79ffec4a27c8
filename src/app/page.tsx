
import HeroSection from "@/components/HeroSection";
import GamingCard from "@/components/GamingCard";
import Image from "next/image";

export default async function Home() {
  

  // Allow both authenticated and non-authenticated users to view homepage

  // Helper function to extract YouTube video ID
  const getYouTubeThumbnail = (url: string) => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    const videoId = (match && match[2].length === 11) ? match[2] : null;
    return videoId ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg` : '/placeholder-thumbnail.jpg';
  };

  // Define video content as a constant to ensure consistent server/client rendering
  const videoContent = [
    // YouTube Videos
    {
      id: "yt1",
      icon: "▶️",
      title: "YouTube Trending #1",
      description: "Top trending video on YouTube this week - Viral content that's taking over the platform",
      gradient: "from-red-600 to-red-400",
      link: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      platform: "YouTube",
      thumbnail: getYouTubeThumbnail("https://www.youtube.com/watch?v=dQw4w9WgXcQ"),
      duration: "12:34"
    },
    {
      id: "yt2",
      icon: "🔥",
      title: "YouTube Creator Spotlight",
      description: "Amazing content from one of our featured creators - see what makes it special",
      gradient: "from-red-600 to-orange-500",
      link: "https://www.youtube.com/watch?v=9bZkp7q19f0",
      platform: "YouTube",
      thumbnail: getYouTubeThumbnail("https://www.youtube.com/watch?v=9bZkp7q19f0"),
      duration: "8:45"
    },
    {
      id: "yt3",
      icon: "🚀",
      title: "YouTube Shorts Hit",
      description: "This short video went viral with over 10M views in just 3 days",
      gradient: "from-red-500 to-pink-500",
      link: "https://www.youtube.com/shorts/3tmd-ClpJxA",
      platform: "YouTube",
      thumbnail: getYouTubeThumbnail("https://www.youtube.com/watch?v=3tmd-ClpJxA"),
      duration: "0:45"
    },
    // Rumble Videos
    {
      id: "rm1",
      icon: "▶️",
      title: "Rumble Top Pick",
      description: "Breaking news coverage that's trending on Rumble right now",
      gradient: "from-blue-600 to-cyan-500",
      link: "https://rumble.com/v4example1",
      platform: "Rumble",
      thumbnail: "https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg", // Placeholder
      duration: "15:20"
    },
    {
      id: "rm2",
      icon: "💎",
      title: "Rumble Exclusive",
      description: "Content you won't find anywhere else - Rumble exclusive creator",
      gradient: "from-blue-700 to-indigo-600",
      link: "https://rumble.com/v4example2",
      platform: "Rumble",
      thumbnail: "https://i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg", // Placeholder
      duration: "22:10"
    },
    {
      id: "rm3",
      icon: "🌟",
      title: "Rumble Rising Star",
      description: "New creator making waves with fresh, engaging content",
      gradient: "from-indigo-600 to-purple-600",
      link: "https://rumble.com/v4example3",
      platform: "Rumble",
      thumbnail: "https://i.ytimg.com/vi/3tmd-ClpJxA/maxresdefault.jpg", // Placeholder
      duration: "18:35"
    },
    // TikTok Videos
    {
      id: "tk1",
      icon: "🎵",
      title: "TikTok Viral Sound",
      description: "This sound is trending with over 500K videos made with it",
      gradient: "from-black to-gray-800",
      link: "https://www.tiktok.com/@example/video/1234567890",
      platform: "TikTok",
      thumbnail: "https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg", // Placeholder
      duration: "0:32"
    },
    {
      id: "tk2",
      icon: "💃",
      title: "Dance Challenge",
      description: "Join the latest dance craze sweeping TikTok",
      gradient: "from-pink-600 to-rose-500",
      link: "https://www.tiktok.com/@example/video/1234567891",
      platform: "TikTok",
      thumbnail: "https://i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg", // Placeholder
      duration: "0:45"
    },
    {
      id: "tk3",
      icon: "😂",
      title: "Comedy Gold",
      description: "Hilarious clip that's been shared millions of times",
      gradient: "from-cyan-400 to-blue-500",
      link: "https://www.tiktok.com/@example/video/1234567892",
      platform: "TikTok",
      thumbnail: "https://i.ytimg.com/vi/3tmd-ClpJxA/maxresdefault.jpg", // Placeholder
      duration: "0:28"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <HeroSection 
        title="New World Alliance"
        subtitle="Promoting Peace Prosperity through Media Promotion"
        description="Join our community of content creators and promote peace through media"
        userName="Content Creator"
      />
      
      {/* Features Grid */}
      <section className="py-20 px-4 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-display">
              <span className="cyber-text">Leader Board</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Share and like to climb up the leader board.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {videoContent.map((video) => (
              <GamingCard 
                key={video.id} 
                floating={video.id === "yt1" || video.id === "rm2" || video.id === "tk3"}
                className="group hover:scale-105 transition-all duration-500 h-full flex flex-col overflow-hidden"
              >
                {/* Thumbnail with play button overlay */}
                <div className="relative h-40 bg-gray-800 overflow-hidden">
                  <Image 
                    src={video.thumbnail} 
                    alt={video.title}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                      <div className="w-0 h-0 border-t-8 border-b-8 border-l-16 border-t-transparent border-b-transparent border-l-white ml-1"></div>
                    </div>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                    {video.duration}
                  </div>
                </div>
                
                <div className="flex-grow text-center space-y-4 p-6">
                  <div className="flex justify-center items-center space-x-2">
                    <div className={`w-10 h-10 rounded-xl bg-gradient-to-br ${video.gradient} flex items-center justify-center text-lg`}>
                      {video.icon}
                    </div>
                    <div className="bg-black/30 px-3 py-1 rounded-lg">
                      <span className={`text-xs font-semibold ${
                        video.platform === 'YouTube' ? 'text-red-400' :
                        video.platform === 'Rumble' ? 'text-blue-400' :
                        'text-white'
                      }`}>
                        {video.platform}
                      </span>
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-bold text-white font-display group-hover:text-cyan-300 transition-colors line-clamp-2">
                    {video.title}
                  </h3>
                  <p className="text-gray-400 leading-relaxed text-sm line-clamp-2">
                    {video.description}
                  </p>
                </div>
                
                <div className="p-4 pt-0">
                  <a 
                    href={video.link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="block w-full px-4 py-2 text-center rounded-lg bg-gradient-to-r from-purple-600 to-cyan-600 text-white font-medium hover:opacity-90 transition-opacity"
                  >
                    Watch on {video.platform} →
                  </a>
                </div>
                
                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />
              </GamingCard>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 px-4 relative">
        <div className="max-w-4xl mx-auto text-center">
          <GamingCard glowing className="relative overflow-hidden">
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold font-display">
                <span className="cyber-text">Ready to Level Up?</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-4xl mx-auto">
                Log in, share and like videos, and climb the leaderboard.
                Join creators maximizing their reach and aiming for the top spot.
                Every action helps raise awarreness of the New World Alliances Global movement.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <a 
                  href="/auth/signin" 
                  className="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-xl font-semibold text-white transition-all duration-300 hover:scale-105 neon-glow inline-flex items-center justify-center space-x-2"
                >
                  <span>🚀</span>
                  <span>Start Your Journey</span>
                </a>
                <a 
                  href="/auth/signin" 
                  className="group px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl font-semibold text-white transition-all duration-300 hover:bg-white/20 hover:scale-105 inline-flex items-center justify-center space-x-2"
                >
                  <span>👤</span>
                  <span>Create Account</span>
                </a>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-cyan-500/20 rounded-full blur-2xl" />
            <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-gradient-to-br from-rose-500/20 to-amber-500/20 rounded-full blur-2xl" />
          </GamingCard>
        </div>
      </section>
    </div>
  );
}