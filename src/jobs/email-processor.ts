import { prisma } from '@/lib/prisma';

// In a real application, you would use a proper queue system like Bull or Agenda
// For now, we'll simulate a simple email sending process

/**
 * Process pending email notifications
 * This function should be run periodically (e.g., via a cron job)
 */
export async function processEmailQueue() {
  try {
    // Get pending emails (limit to 10 at a time to avoid overwhelming the system)
    const pendingEmails = await prisma.emailQueue.findMany({
      where: {
        status: 'pending',
        scheduledAt: {
          lte: new Date()
        }
      },
      take: 10,
      orderBy: {
        scheduledAt: 'asc'
      }
    });

    if (pendingEmails.length === 0) {
      console.log('No pending emails to process');
      return;
    }

    console.log(`Processing ${pendingEmails.length} emails`);

    // Process each email
    for (const email of pendingEmails) {
      try {
        // In a real application, you would integrate with an email service like SendGrid or AWS SES
        // For now, we'll just log the email and mark it as sent
        console.log(`Sending email to ${email.emailAddress}: ${email.subject}`);
        
        // Mark as sent
        await prisma.emailQueue.update({
          where: { id: email.id },
          data: {
            status: 'sent',
            sentAt: new Date(),
            attempts: {
              increment: 1
            }
          }
        });
      } catch (error) {
        console.error(`Error processing email ${email.id}:`, error);
        
        // Update attempt count and handle failures
        const updatedAttempts = email.attempts + 1;
        
        if (updatedAttempts >= email.maxAttempts) {
          // Mark as failed after max attempts
          await prisma.emailQueue.update({
            where: { id: email.id },
            data: {
              status: 'failed',
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              attempts: updatedAttempts
            }
          });
        } else {
          // Increment attempts and keep as pending
          await prisma.emailQueue.update({
            where: { id: email.id },
            data: {
              attempts: updatedAttempts
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('Error processing email queue:', error);
  }
}

/**
 * Schedule this function to run periodically
 * In a real application, you would use a cron job or a dedicated worker process
 */
export function startEmailProcessor() {
  // Run every 5 minutes
  setInterval(processEmailQueue, 5 * 60 * 1000);
  
  // Run immediately on startup
  processEmailQueue();
}