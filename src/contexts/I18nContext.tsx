'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { I18nManager, Locale, i18n } from '@/lib/i18n';

interface I18nContextType {
  locale: Locale;
  changeLocale: (locale: Locale) => Promise<void>;
  t: (key: string, options?: Record<string, unknown>) => string;
  isLoading: boolean;
  i18n: I18nManager;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: React.ReactNode;
  initialLocale?: Locale;
}

export function I18nProvider({ children, initialLocale }: I18nProviderProps) {
  const [locale, setLocale] = useState<Locale>(initialLocale || 'en');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeI18n = async () => {
      try {
        // Detect locale if not provided
        const detectedLocale = initialLocale || i18n.detectLocale();
        
        // Initialize i18n
        await i18n.init(detectedLocale);
        setLocale(detectedLocale);
      } catch (error) {
        console.error('Failed to initialize i18n:', error);
        // Fallback to English
        await i18n.init('en');
        setLocale('en');
      } finally {
        setIsLoading(false);
      }
    };

    initializeI18n();
  }, [initialLocale]);

  useEffect(() => {
    // Listen for locale changes
    const handleLocaleChange = (event: CustomEvent) => {
      setLocale(event.detail.locale);
    };

    window.addEventListener('localeChanged', handleLocaleChange as EventListener);
    return () => {
      window.removeEventListener('localeChanged', handleLocaleChange as EventListener);
    };
  }, []);

  const changeLocale = async (newLocale: Locale) => {
    setIsLoading(true);
    try {
      await i18n.changeLocale(newLocale);
      setLocale(newLocale);
    } finally {
      setIsLoading(false);
    }
  };

  const t = (key: string, options?: Record<string, unknown>) => {
    return i18n.t(key, options);
  };

  const value: I18nContextType = {
    locale,
    changeLocale,
    t,
    isLoading,
    i18n
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
}

export function useI18n(): I18nContextType {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}