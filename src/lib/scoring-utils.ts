import { prisma } from '@/lib/prisma';

// Points awarded for different actions
export const SCORING_CONFIG = {
  CREATE_USER_VIDEO: 10,
  LIKE_VIDEO: 1,
  SHARE_VIDEO: 2,
  COMPLETE_VIDEO: 1,
} as const;

/**
 * Award points to a user for a specific action
 */
export async function awardPoints(userId: string, points: number, reason: string) {
  try {
    // Update user's total points and score
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        totalPoints: {
          increment: points,
        },
        score: {
          increment: points,
        },
        experiencePoints: {
          increment: points,
        },
      },
      select: {
        id: true,
        name: true,
        totalPoints: true,
        level: true,
      },
    });

    // Calculate new level based on total points
    const newLevel = calculateLevel(updatedUser.totalPoints);
    
    // Update level if it changed
    if (newLevel > updatedUser.level) {
      await prisma.user.update({
        where: { id: userId },
        data: { level: newLevel },
      });
    }

    // Update or create leaderboard entry
    await updateLeaderboardEntry(userId, updatedUser.name, updatedUser.totalPoints, newLevel);

    console.log(`Awarded ${points} points to user ${userId} for: ${reason}`);
    
    return {
      pointsAwarded: points,
      totalPoints: updatedUser.totalPoints,
      newLevel,
      levelUp: newLevel > updatedUser.level,
    };
  } catch (error) {
    console.error('Error awarding points:', error);
    throw error;
  }
}

/**
 * Calculate user level based on total points
 */
export function calculateLevel(totalPoints: number): number {
  // Level calculation: every 100 points = 1 level, starting at level 1
  return Math.floor(totalPoints / 100) + 1;
}

/**
 * Update or create a leaderboard entry for a user
 */
export async function updateLeaderboardEntry(
  userId: string, 
  userName: string | null, 
  totalPoints: number, 
  level: number
) {
  try {
    // First, get the user's current rank by counting users with higher points
    const higherRankUsers = await prisma.user.count({
      where: {
        totalPoints: {
          gt: totalPoints,
        },
      },
    });
    
    const rank = higherRankUsers + 1;

    // Update or create leaderboard entry
    await prisma.leaderboardEntry.upsert({
      where: { id: userId },
      update: {
        name: userName || 'Anonymous',
        points: totalPoints,
        level,
        rank,
      },
      create: {
        id: userId,
        name: userName || 'Anonymous',
        points: totalPoints,
        level,
        rank,
      },
    });

    // Update ranks for all users (this could be optimized with a batch operation)
    await recalculateAllRanks();
    
  } catch (error) {
    console.error('Error updating leaderboard entry:', error);
    throw error;
  }
}

/**
 * Recalculate ranks for all users in the leaderboard
 */
export async function recalculateAllRanks() {
  try {
    // Get all users ordered by total points (descending)
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        totalPoints: true,
        level: true,
      },
      orderBy: {
        totalPoints: 'desc',
      },
    });

    // Update leaderboard entries with correct ranks
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      const rank = i + 1;

      await prisma.leaderboardEntry.upsert({
        where: { id: user.id },
        update: {
          name: user.name || 'Anonymous',
          points: user.totalPoints,
          level: user.level,
          rank,
        },
        create: {
          id: user.id,
          name: user.name || 'Anonymous',
          points: user.totalPoints,
          level: user.level,
          rank,
        },
      });
    }
  } catch (error) {
    console.error('Error recalculating ranks:', error);
    throw error;
  }
}

/**
 * Get user's current leaderboard position
 */
export async function getUserLeaderboardPosition(userId: string): Promise<number> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { totalPoints: true },
    });

    if (!user) return 1;

    const higherRankUsers = await prisma.user.count({
      where: {
        totalPoints: {
          gt: user.totalPoints,
        },
      },
    });

    return higherRankUsers + 1;
  } catch (error) {
    console.error('Error getting user leaderboard position:', error);
    return 1;
  }
}