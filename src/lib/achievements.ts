export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  category: 'engagement' | 'platform' | 'streak' | 'milestone' | 'special';
  requirements?: {
    engagements?: number;
    platforms?: string[];
    streak?: number;
    verified?: boolean;
  };
}

export const ACHIEVEMENTS: Record<string, Achievement> = {
  // First-time achievements
  FIRST_LIKE: {
    id: 'first_like',
    title: 'First Like',
    description: 'Liked your first video',
    icon: '👍',
    points: 5,
    category: 'milestone',
    requirements: { engagements: 1 }
  },

  FIRST_SHARE: {
    id: 'first_share',
    title: 'Content Sharer',
    description: 'Shared your first video',
    icon: '🔗',
    points: 10,
    category: 'milestone',
    requirements: { engagements: 1 }
  },

  // Platform explorer achievements
  PLATFORM_EXPLORER: {
    id: 'platform_explorer',
    title: 'Platform Explorer',
    description: 'Engaged on all 3 platforms',
    icon: '🌟',
    points: 50,
    category: 'platform',
    requirements: { platforms: ['youtube', 'tiktok', 'rumble'] }
  },

  YOUTUBE_SPECIALIST: {
    id: 'youtube_specialist',
    title: 'YouTube Specialist',
    description: 'Engaged with 10 videos on YouTube',
    icon: '📺',
    points: 25,
    category: 'platform'
  },

  TIKTOK_SPECIALIST: {
    id: 'tiktok_specialist',
    title: 'TikTok Specialist',
    description: 'Engaged with 10 videos on TikTok',
    icon: '🎵',
    points: 25,
    category: 'platform'
  },

  RUMBLE_SPECIALIST: {
    id: 'rumble_specialist',
    title: 'Rumble Specialist',
    description: 'Engaged with 10 videos on Rumble',
    icon: '⚡',
    points: 25,
    category: 'platform'
  },

  // Engagement milestones
  ENGAGEMENT_ROOKIE: {
    id: 'engagement_rookie',
    title: 'Engagement Rookie',
    description: 'Completed 10 total engagements',
    icon: '🎯',
    points: 20,
    category: 'engagement',
    requirements: { engagements: 10 }
  },

  ENGAGEMENT_PRO: {
    id: 'engagement_pro',
    title: 'Engagement Pro',
    description: 'Completed 50 total engagements',
    icon: '🏆',
    points: 100,
    category: 'engagement',
    requirements: { engagements: 50 }
  },

  ENGAGEMENT_MASTER: {
    id: 'engagement_master',
    title: 'Engagement Master',
    description: 'Completed 100 total engagements',
    icon: '👑',
    points: 200,
    category: 'engagement',
    requirements: { engagements: 100 }
  },

  // Streak achievements
  STREAK_3: {
    id: 'streak_3',
    title: '3-Day Streak',
    description: '3 consecutive days of engagement',
    icon: '🔥',
    points: 15,
    category: 'streak',
    requirements: { streak: 3 }
  },

  STREAK_7: {
    id: 'streak_7',
    title: 'Weekly Warrior',
    description: '7 consecutive days of engagement',
    icon: '🔥',
    points: 50,
    category: 'streak',
    requirements: { streak: 7 }
  },

  STREAK_14: {
    id: 'streak_14',
    title: 'Two Week Champion',
    description: '14 consecutive days of engagement',
    icon: '🔥',
    points: 100,
    category: 'streak',
    requirements: { streak: 14 }
  },

  STREAK_30: {
    id: 'streak_30',
    title: 'Monthly Legend',
    description: '30 consecutive days of engagement',
    icon: '🔥',
    points: 250,
    category: 'streak',
    requirements: { streak: 30 }
  },

  // Verification achievements
  HONEST_ENGAGER: {
    id: 'honest_engager',
    title: 'Honest Engager',
    description: 'Confirmed 10 engagements',
    icon: '✅',
    points: 30,
    category: 'engagement',
    requirements: { verified: true }
  },

  TRUSTED_MEMBER: {
    id: 'trusted_member',
    title: 'Trusted Member',
    description: 'Confirmed 50 engagements',
    icon: '🛡️',
    points: 100,
    category: 'engagement',
    requirements: { verified: true }
  },

  // Special achievements
  EARLY_ADOPTER: {
    id: 'early_adopter',
    title: 'Early Adopter',
    description: 'One of the first 100 users',
    icon: '🚀',
    points: 100,
    category: 'special'
  },

  COMMUNITY_CHAMPION: {
    id: 'community_champion',
    title: 'Community Champion',
    description: 'High engagement rate with confirmations',
    icon: '🏅',
    points: 150,
    category: 'special'
  }
};

export const getAchievementsByCategory = (category: Achievement['category']) => {
  return Object.values(ACHIEVEMENTS).filter(achievement => achievement.category === category);
};

export const calculateAchievementProgress = (
  achievementId: string,
  userStats: {
    totalLikes: number;
    totalShares: number;
    totalCompletions: number;
    totalConfirmed: number;
    currentStreak: number;
    platformsUsed: string[];
    platformStats: Record<string, { likes: number; shares: number; completions: number }>;
  }
): { completed: boolean; progress: number; total: number } => {
  const achievement = ACHIEVEMENTS[achievementId];
  if (!achievement) return { completed: false, progress: 0, total: 1 };

  const totalEngagements = userStats.totalLikes + userStats.totalShares + userStats.totalCompletions;

  switch (achievementId) {
    case 'FIRST_LIKE':
      return {
        completed: userStats.totalLikes >= 1,
        progress: Math.min(userStats.totalLikes, 1),
        total: 1
      };

    case 'FIRST_SHARE':
      return {
        completed: userStats.totalShares >= 1,
        progress: Math.min(userStats.totalShares, 1),
        total: 1
      };

    case 'PLATFORM_EXPLORER':
      const requiredPlatforms = ['youtube', 'tiktok', 'rumble'];
      const completedPlatforms = requiredPlatforms.filter(platform => 
        userStats.platformsUsed.includes(platform)
      );
      return {
        completed: completedPlatforms.length >= 3,
        progress: completedPlatforms.length,
        total: 3
      };

    case 'YOUTUBE_SPECIALIST':
      const youtubeEngagements = userStats.platformStats.youtube ? 
        Object.values(userStats.platformStats.youtube).reduce((sum, val) => sum + val, 0) : 0;
      return {
        completed: youtubeEngagements >= 10,
        progress: Math.min(youtubeEngagements, 10),
        total: 10
      };

    case 'TIKTOK_SPECIALIST':
      const tiktokEngagements = userStats.platformStats.tiktok ? 
        Object.values(userStats.platformStats.tiktok).reduce((sum, val) => sum + val, 0) : 0;
      return {
        completed: tiktokEngagements >= 10,
        progress: Math.min(tiktokEngagements, 10),
        total: 10
      };

    case 'RUMBLE_SPECIALIST':
      const rumbleEngagements = userStats.platformStats.rumble ? 
        Object.values(userStats.platformStats.rumble).reduce((sum, val) => sum + val, 0) : 0;
      return {
        completed: rumbleEngagements >= 10,
        progress: Math.min(rumbleEngagements, 10),
        total: 10
      };

    case 'ENGAGEMENT_ROOKIE':
      return {
        completed: totalEngagements >= 10,
        progress: Math.min(totalEngagements, 10),
        total: 10
      };

    case 'ENGAGEMENT_PRO':
      return {
        completed: totalEngagements >= 50,
        progress: Math.min(totalEngagements, 50),
        total: 50
      };

    case 'ENGAGEMENT_MASTER':
      return {
        completed: totalEngagements >= 100,
        progress: Math.min(totalEngagements, 100),
        total: 100
      };

    case 'STREAK_3':
      return {
        completed: userStats.currentStreak >= 3,
        progress: Math.min(userStats.currentStreak, 3),
        total: 3
      };

    case 'STREAK_7':
      return {
        completed: userStats.currentStreak >= 7,
        progress: Math.min(userStats.currentStreak, 7),
        total: 7
      };

    case 'STREAK_14':
      return {
        completed: userStats.currentStreak >= 14,
        progress: Math.min(userStats.currentStreak, 14),
        total: 14
      };

    case 'STREAK_30':
      return {
        completed: userStats.currentStreak >= 30,
        progress: Math.min(userStats.currentStreak, 30),
        total: 30
      };

    case 'HONEST_ENGAGER':
      return {
        completed: userStats.totalConfirmed >= 10,
        progress: Math.min(userStats.totalConfirmed, 10),
        total: 10
      };

    case 'TRUSTED_MEMBER':
      return {
        completed: userStats.totalConfirmed >= 50,
        progress: Math.min(userStats.totalConfirmed, 50),
        total: 50
      };

    default:
      return { completed: false, progress: 0, total: 1 };
  }
};

export const checkForNewAchievements = (
  currentAchievements: string[],
  userStats: Parameters<typeof calculateAchievementProgress>[1]
): Achievement[] => {
  const newAchievements: Achievement[] = [];

  Object.keys(ACHIEVEMENTS).forEach(achievementId => {
    // Skip if user already has this achievement
    if (currentAchievements.includes(achievementId)) return;

    const progress = calculateAchievementProgress(achievementId, userStats);
    if (progress.completed) {
      newAchievements.push(ACHIEVEMENTS[achievementId]);
    }
  });

  return newAchievements;
};
