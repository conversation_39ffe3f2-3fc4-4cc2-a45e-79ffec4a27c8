import { cacheService } from './cache-service';

/**
 * Cache invalidation strategies for different types of data changes
 */
export class CacheInvalidation {
  
  /**
   * Invalidate caches when a video is created, updated, or deleted
   */
  static async onVideoChange(_videoId?: string, _userId?: string) {
    await Promise.all([
      cacheService.invalidateVideoRelatedCaches(),
      cacheService.invalidateDashboardStats(),
      // If it's an NWA video, invalidate all user-specific NWA video caches
      cacheService.invalidateNWAVideos(),
    ]);
  }

  /**
   * Invalidate caches when a user's engagement changes (likes, shares, completions)
   */
  static async onEngagementChange(userId: string, videoId?: string) {
    await Promise.all([
      cacheService.invalidateUserStats(userId),
      cacheService.invalidateLeaderboard(),
      cacheService.invalidateDashboardStats(),
      cacheService.invalidateAnalytics(),
      // If user completed a video, invalidate their NWA videos cache
      videoId ? cacheService.invalidateNWAVideos() : Promise.resolve(),
    ]);
  }

  /**
   * Invalidate caches when user data changes (points, level, etc.)
   */
  static async onUserDataChange(userId: string) {
    await Promise.all([
      cacheService.invalidateUserStats(userId),
      cacheService.invalidateLeaderboard(),
      cacheService.invalidateDashboardStats(),
    ]);
  }

  /**
   * Invalidate caches when a video link is created, updated, or deleted
   */
  static async onVideoLinkChange(userId?: string) {
    await Promise.all([
      cacheService.invalidateUserVideoLinks(),
      cacheService.invalidateDashboardStats(),
      userId ? cacheService.invalidateUserStats(userId) : Promise.resolve(),
    ]);
  }

  /**
   * Invalidate caches when a notification is sent
   */
  static async onNotificationSent(_videoId: string) {
    // Notifications don't directly affect cached data, but we might want to
    // track notification status in the future
    await Promise.resolve();
  }

  /**
   * Invalidate caches when leaderboard is manually recalculated
   */
  static async onLeaderboardRecalculation() {
    await Promise.all([
      cacheService.invalidateLeaderboard(),
      cacheService.invalidateAllUserStats(),
    ]);
  }

  /**
   * Invalidate all caches (nuclear option for major data changes)
   */
  static async invalidateAll() {
    await Promise.all([
      cacheService.invalidateLeaderboard(),
      cacheService.invalidateDashboardStats(),
      cacheService.invalidateAllUserStats(),
      cacheService.invalidateAnalytics(),
      cacheService.invalidateVideoRelatedCaches(),
    ]);
  }

  /**
   * Invalidate caches when a user completes a video
   */
  static async onVideoCompletion(userId: string, _videoId: string) {
    await Promise.all([
      cacheService.invalidateUserStats(userId),
      cacheService.invalidateNWAVideos(), // User-specific NWA videos will change
      cacheService.invalidateLeaderboard(),
      cacheService.invalidateAnalytics(),
    ]);
  }

  /**
   * Invalidate caches when featured videos change
   */
  static async onFeaturedVideoChange() {
    await Promise.all([
      cacheService.invalidateVideoRelatedCaches(),
      cacheService.invalidateNWAVideos(), // Featured status affects NWA video ordering
    ]);
  }
}

/**
 * Utility function to safely invalidate cache with error handling
 */
export async function safeInvalidateCache(invalidationFn: () => Promise<void>, context: string) {
  try {
    await invalidationFn();
  } catch (error) {
    console.error(`Cache invalidation failed for ${context}:`, error);
    // Don't throw - cache invalidation failures shouldn't break the main operation
  }
}