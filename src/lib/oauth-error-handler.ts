export class OAuthErrorHandler {
  static handleCallbackError(error: Error, provider: string) {
    console.error(`OAuth callback error for provider ${provider}:`, error);

    // Handle specific OAuth errors based on error message
    if (error.message.includes('server_error')) {
      return {
        error: 'server_error',
        error_description: 'Internal server error during OAuth authentication. Please try again.',
        provider
      };
    }

    if (error.message.includes('access_denied')) {
      return {
        error: 'access_denied',
        error_description: 'Access denied by OAuth provider.',
        provider
      };
    }

    if (error.message.includes('invalid_client')) {
      return {
        error: 'configuration_error',
        error_description: 'OAuth client configuration error.',
        provider
      };
    }

    if (error.message.includes('invalid_grant')) {
      return {
        error: 'invalid_grant',
        error_description: 'Invalid authorization code. Please try signing in again.',
        provider
      };
    }

    // Handle generic errors
    return {
      error: 'authentication_error',
      error_description: 'An error occurred during authentication. Please try again.',
      provider
    };
  }

  static logAuthEvent(event: string, data: Record<string, unknown>) {
    console.log(`[OAuth Event] ${event}:`, {
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  static validateOAuthConfig(provider: string) {
    const requiredEnvVars = [
      'MEMBER_PORTAL_URL',
      'CLIENT_ID',
      'CLIENT_SECRET'
    ];

    const missing = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
      console.error(`OAuth configuration error for ${provider}: Missing environment variables:`, missing);
      return {
        valid: false,
        error: `Missing required environment variables: ${missing.join(', ')}`
      };
    }

    return { valid: true };
  }
}