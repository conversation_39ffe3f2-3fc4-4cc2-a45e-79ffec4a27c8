import { prisma } from './prisma';
import type { Prisma } from '@/generated/prisma';

/**
 * Query optimization utilities to prevent N+1 queries and improve performance
 */

// Type definitions for optimized queries
export interface OptimizedVideo {
  id: string;
  title: string;
  description: string | null;
  url: string;
  youtubeUrl: string | null;
  tiktokUrl: string | null;
  rumbleUrl: string | null;
  thumbnailUrl: string | null;
  platform: string;
  duration: number | null;
  status: string;
  isFeatured: boolean;
  notificationSentAt: Date | null;
  views: number;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string | null;
    role: string;
  };
  _count: {
    likes: number;
    shares: number;
    engagements: number;
    completions: number;
  };
  userInteractions?: {
    liked: boolean;
    shared: boolean;
    completed: boolean;
  };
}

export interface OptimizedVideoLink {
  id: string;
  userId: string;
  videoId: string;
  platform: string;
  linkUrl: string;
  createdAt: Date;
  status: string;
  User: {
    id: string;
    name: string | null;
    role: string;
  };
  Video: {
    id: string;
    title: string;
    description: string | null;
    youtubeUrl: string | null;
    tiktokUrl: string | null;
    rumbleUrl: string | null;
    thumbnailUrl: string | null;
    status: string;
    isFeatured: boolean;
    createdAt: Date;
  };
  _count: {
    likes: number;
    shares: number;
  };
  userInteractions?: {
    liked: boolean;
    shared: boolean;
  };
}

/**
 * Optimized video queries with proper includes to prevent N+1 queries
 */
export class VideoQueryOptimizer {
  /**
   * Get videos with all related data in a single query
   */
  static async getVideosWithRelations(
    where: Prisma.VideoWhereInput,
    options: {
      take?: number;
      skip?: number;
      orderBy?: Prisma.VideoOrderByWithRelationInput[];
      userId?: string; // For user-specific interactions
    } = {}
  ): Promise<OptimizedVideo[]> {
    const { take = 10, skip = 0, orderBy = [{ createdAt: 'desc' }], userId } = options;

    const videos = await prisma.video.findMany({
      where,
      take,
      skip,
      orderBy,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        _count: {
          select: {
            likes: true,
            shares: true,
            engagements: true,
            completions: true,
          },
        },
        // Include user-specific interactions if userId provided
        ...(userId && {
          likes: {
            where: { userId },
            select: { id: true },
          },
          shares: {
            where: { userId },
            select: { id: true },
          },
          completions: {
            where: { userId },
            select: { id: true },
          },
          engagements: {
            where: { userId },
            select: { platform: true, action: true },
          },
        }),
      },
    });

    // Transform to include user interactions if userId provided
    return videos.map(video => ({
      ...video,
      userInteractions: userId ? {
        liked: Array.isArray(video.likes) && video.likes.length > 0,
        shared: Array.isArray(video.shares) && video.shares.length > 0,
        completed: Array.isArray(video.completions) && video.completions.length > 0,
      } : undefined,
    })) as OptimizedVideo[];
  }

  /**
   * Get NWA videos for dashboard with user-specific data
   */
  static async getNWAVideosForUser(userId: string, limit = 5): Promise<OptimizedVideo[]> {
    return this.getVideosWithRelations(
      {
        user: {
          role: { in: ['ADMIN', 'NWA_TEAM'] },
        },
        status: 'published',
        completions: {
          none: { userId },
        },
      },
      {
        take: limit,
        orderBy: [
          { isFeatured: 'desc' },
          { createdAt: 'asc' }, // Oldest first as per requirement 6.4
        ],
        userId,
      }
    );
  }

  /**
   * Get featured videos with counts
   */
  static async getFeaturedVideos(limit = 10): Promise<OptimizedVideo[]> {
    return this.getVideosWithRelations(
      {
        isFeatured: true,
        status: 'published',
      },
      {
        take: limit,
        orderBy: [{ createdAt: 'desc' }],
      }
    );
  }

  /**
   * Search videos with optimized query
   */
  static async searchVideos(
    searchQuery: string,
    filters: {
      type?: 'nwa' | 'user' | 'all';
      platform?: string;
      userId?: string;
    } = {},
    pagination: { take?: number; skip?: number } = {}
  ): Promise<{ videos: OptimizedVideo[]; total: number }> {
    const { type = 'all', platform, userId: filterUserId } = filters;
    const { take = 10, skip = 0 } = pagination;

    // Build where clause
    const whereClause: Prisma.VideoWhereInput = {
      status: 'published',
      OR: [
        {
          title: {
            contains: searchQuery,
          },
        },
        {
          description: {
            contains: searchQuery,
          },
        },
      ],
    };

    // Add type filter
    if (type === 'nwa') {
      whereClause.user = {
        role: { in: ['ADMIN', 'NWA_TEAM'] },
      };
    } else if (type === 'user') {
      whereClause.user = {
        role: 'USER',
      };
    }

    // Add user filter
    if (filterUserId) {
      whereClause.userId = filterUserId;
    }

    // Add platform filter
    if (platform) {
      switch (platform.toLowerCase()) {
        case 'youtube':
          whereClause.youtubeUrl = { not: null };
          break;
        case 'tiktok':
          whereClause.tiktokUrl = { not: null };
          break;
        case 'rumble':
          whereClause.rumbleUrl = { not: null };
          break;
      }
    }

    const [videos, total] = await Promise.all([
      this.getVideosWithRelations(whereClause, { take, skip }),
      prisma.video.count({ where: whereClause }),
    ]);

    return { videos, total };
  }
}

/**
 * Optimized video link queries
 */
export class VideoLinkQueryOptimizer {
  /**
   * Get video links with all related data in a single query
   */
  static async getVideoLinksWithRelations(
    where: Prisma.VideoLinkWhereInput = {},
    options: {
      take?: number;
      skip?: number;
      orderBy?: Prisma.VideoLinkOrderByWithRelationInput[];
      userId?: string; // For user-specific interactions
    } = {}
  ): Promise<OptimizedVideoLink[]> {
    const { take = 50, skip = 0, orderBy = [{ createdAt: 'desc' }], userId } = options;

    const videoLinks = await prisma.videoLink.findMany({
      where,
      take,
      skip,
      orderBy,
      include: {
        User: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        Video: {
          select: {
            id: true,
            title: true,
            description: true,
            youtubeUrl: true,
            tiktokUrl: true,
            rumbleUrl: true,
            thumbnailUrl: true,
            status: true,
            isFeatured: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            likes: true,
            shares: true,
          },
        },
        // Include user-specific interactions if userId provided
        ...(userId && {
          likes: {
            where: { userId },
            select: { id: true },
          },
          shares: {
            where: { userId },
            select: { id: true },
          },
        }),
      },
    });

    // Transform to include user interactions if userId provided
    return videoLinks.map(link => ({
      ...link,
      userInteractions: userId ? {
        liked: Array.isArray(link.likes) && link.likes.length > 0,
        shared: Array.isArray(link.shares) && link.shares.length > 0,
      } : undefined,
    })) as OptimizedVideoLink[];
  }

  /**
   * Get user video links for dashboard
   */
  static async getUserVideoLinksForDashboard(
    userId?: string,
    limit = 50
  ): Promise<OptimizedVideoLink[]> {
    return this.getVideoLinksWithRelations(
      {},
      {
        take: limit,
        orderBy: [{ createdAt: 'desc' }],
        userId,
      }
    );
  }
}

/**
 * Optimized user and engagement queries
 */
export class UserQueryOptimizer {
  /**
   * Get user stats with optimized queries
   */
  static async getUserStatsOptimized(userId: string) {
    const [user, engagementStats, leaderboardPosition] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          score: true,
          level: true,
          totalPoints: true,
          experiencePoints: true,
          role: true,
        },
      }),
      prisma.userEngagement.groupBy({
        by: ['action'],
        where: { userId },
        _count: { action: true },
      }),
      this.getUserLeaderboardPosition(userId),
    ]);

    const engagementCounts = engagementStats.reduce((acc, stat) => {
      acc[stat.action] = stat._count.action;
      return acc;
    }, {} as Record<string, number>);

    return {
      user,
      stats: {
        totalLikes: engagementCounts.like || 0,
        totalShares: engagementCounts.share || 0,
        totalCompletions: engagementCounts.complete || 0,
        leaderboardPosition,
      },
    };
  }

  /**
   * Get user leaderboard position efficiently
   */
  static async getUserLeaderboardPosition(userId: string): Promise<number> {
    try {
      const result = await prisma.$queryRaw<Array<{ position: bigint }>>`
        SELECT COUNT(*) + 1 as position
        FROM User
        WHERE totalPoints > (SELECT totalPoints FROM User WHERE id = ${userId})
      `;
      
      return result && result[0] ? Number(result[0].position) : 1;
    } catch (error) {
      console.error('Error calculating leaderboard position:', error);
      return 1;
    }
  }

  /**
   * Get leaderboard with optimized query
   */
  static async getLeaderboardOptimized(limit = 50) {
    return prisma.user.findMany({
      take: limit,
      orderBy: [
        { totalPoints: 'desc' },
        { score: 'desc' },
        { createdAt: 'asc' }, // Tie-breaker
      ],
      select: {
        id: true,
        name: true,
        totalPoints: true,
        score: true,
        level: true,
      },
    });
  }
}

/**
 * Dashboard query optimizer - combines multiple queries efficiently
 */
export class DashboardQueryOptimizer {
  /**
   * Get all dashboard data in optimized queries
   */
  static async getDashboardData(userId: string) {
    const [
      user,
      dashboardStats,
      nwaVideos,
      userVideoLinks,
      userStats,
    ] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          score: true,
          level: true,
          totalPoints: true,
          experiencePoints: true,
          role: true,
        },
      }),
      this.getDashboardStats(),
      VideoQueryOptimizer.getNWAVideosForUser(userId, 5),
      VideoLinkQueryOptimizer.getUserVideoLinksForDashboard(userId, 50),
      UserQueryOptimizer.getUserStatsOptimized(userId),
    ]);

    return {
      user,
      dashboardStats,
      nwaVideos,
      userVideoLinks,
      userStats: userStats.stats,
    };
  }

  /**
   * Get dashboard statistics efficiently
   */
  private static async getDashboardStats() {
    const [videoStats, userCount] = await Promise.all([
      prisma.video.aggregate({
        _count: { id: true },
        _sum: { views: true },
        where: { status: 'published' },
      }),
      prisma.user.count(),
    ]);

    return {
      totalVideos: videoStats._count.id,
      totalUsers: userCount,
      totalViews: videoStats._sum.views || 0,
    };
  }
}