import { prisma } from './prisma';

export interface AuditLogEntry {
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface AuditLogFilter {
  userId?: string;
  action?: string;
  resource?: string;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

export class AuditLogger {
  /**
   * Log an audit event
   */
  static async log(entry: AuditLogEntry): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId,
          details: entry.details ? JSON.stringify(entry.details) : null,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          sessionId: entry.sessionId,
          timestamp: new Date(),
        },
      });
    } catch (error) {
      // Don't throw errors for audit logging failures to avoid breaking main functionality
      console.error('Failed to log audit event:', error);
    }
  }

  /**
   * Log admin actions
   */
  static async logAdminAction(
    userId: string,
    action: string,
    resource: string,
    resourceId?: string,
    details?: Record<string, unknown>,
    metadata?: { ipAddress?: string; userAgent?: string; sessionId?: string }
  ): Promise<void> {
    await this.log({
      userId,
      action: `ADMIN_${action.toUpperCase()}`,
      resource,
      resourceId,
      details,
      ...metadata,
    });
  }

  /**
   * Log user engagement activities
   */
  static async logUserEngagement(
    userId: string,
    action: string,
    videoId?: string,
    videoLinkId?: string,
    details?: Record<string, unknown>
  ): Promise<void> {
    await this.log({
      userId,
      action: `USER_${action.toUpperCase()}`,
      resource: videoId ? 'video' : 'video_link',
      resourceId: videoId || videoLinkId,
      details,
    });
  }

  /**
   * Log authentication events
   */
  static async logAuthEvent(
    action: string,
    userId?: string,
    details?: Record<string, unknown>,
    metadata?: { ipAddress?: string; userAgent?: string; sessionId?: string }
  ): Promise<void> {
    await this.log({
      userId,
      action: `AUTH_${action.toUpperCase()}`,
      resource: 'authentication',
      details,
      ...metadata,
    });
  }

  /**
   * Log content moderation events
   */
  static async logModerationAction(
    moderatorId: string,
    action: string,
    reportId: string,
    details?: Record<string, unknown>,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.log({
      userId: moderatorId,
      action: `MODERATION_${action.toUpperCase()}`,
      resource: 'content_report',
      resourceId: reportId,
      details,
      ...metadata,
    });
  }

  /**
   * Get audit logs with filtering
   */
  static async getLogs(filter: AuditLogFilter = {}) {
    const {
      userId,
      action,
      resource,
      startDate,
      endDate,
      page = 1,
      limit = 50,
    } = filter;

    const skip = (page - 1) * limit;

    const where: {
      userId?: string;
      action?: { contains: string; mode: 'insensitive' };
      resource?: string;
      timestamp?: { gte?: Date; lte?: Date };
    } = {};

    if (userId) where.userId = userId;
    if (action) where.action = { contains: action, mode: 'insensitive' };
    if (resource) where.resource = resource;
    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: { timestamp: 'desc' },
        skip,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return {
      logs: logs.map(log => ({
        ...log,
        details: log.details ? JSON.parse(log.details) : null,
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get audit statistics
   */
  static async getStats(days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [
      totalLogs,
      adminActions,
      userActions,
      authEvents,
      moderationActions,
      recentActivity,
    ] = await Promise.all([
      prisma.auditLog.count({
        where: { timestamp: { gte: startDate } },
      }),
      prisma.auditLog.count({
        where: {
          timestamp: { gte: startDate },
          action: { startsWith: 'ADMIN_' },
        },
      }),
      prisma.auditLog.count({
        where: {
          timestamp: { gte: startDate },
          action: { startsWith: 'USER_' },
        },
      }),
      prisma.auditLog.count({
        where: {
          timestamp: { gte: startDate },
          action: { startsWith: 'AUTH_' },
        },
      }),
      prisma.auditLog.count({
        where: {
          timestamp: { gte: startDate },
          action: { startsWith: 'MODERATION_' },
        },
      }),
      prisma.auditLog.findMany({
        where: { timestamp: { gte: startDate } },
        include: {
          user: {
            select: { name: true, role: true },
          },
        },
        orderBy: { timestamp: 'desc' },
        take: 10,
      }),
    ]);

    return {
      totalLogs,
      adminActions,
      userActions,
      authEvents,
      moderationActions,
      recentActivity: recentActivity.map(log => ({
        ...log,
        details: log.details ? JSON.parse(log.details) : null,
      })),
    };
  }

  /**
   * Clean up old audit logs (for log rotation)
   */
  static async cleanupOldLogs(retentionDays: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await prisma.auditLog.deleteMany({
      where: {
        timestamp: { lt: cutoffDate },
      },
    });

    return result.count;
  }
}

// Audit action constants
export const AUDIT_ACTIONS = {
  // Admin actions
  ADMIN_VIDEO_CREATE: 'ADMIN_VIDEO_CREATE',
  ADMIN_VIDEO_UPDATE: 'ADMIN_VIDEO_UPDATE',
  ADMIN_VIDEO_DELETE: 'ADMIN_VIDEO_DELETE',
  ADMIN_VIDEO_FEATURE: 'ADMIN_VIDEO_FEATURE',
  ADMIN_NOTIFICATION_SEND: 'ADMIN_NOTIFICATION_SEND',
  ADMIN_USER_UPDATE: 'ADMIN_USER_UPDATE',
  ADMIN_USER_DELETE: 'ADMIN_USER_DELETE',
  ADMIN_USER_ROLE_CHANGE: 'ADMIN_USER_ROLE_CHANGE',
  
  // User engagement
  USER_VIDEO_LIKE: 'USER_VIDEO_LIKE',
  USER_VIDEO_SHARE: 'USER_VIDEO_SHARE',
  USER_VIDEO_COMPLETE: 'USER_VIDEO_COMPLETE',
  USER_VIDEO_CREATE: 'USER_VIDEO_CREATE',
  USER_PROFILE_UPDATE: 'USER_PROFILE_UPDATE',
  
  // Authentication
  AUTH_LOGIN: 'AUTH_LOGIN',
  AUTH_LOGOUT: 'AUTH_LOGOUT',
  AUTH_REGISTER: 'AUTH_REGISTER',
  AUTH_PASSWORD_CHANGE: 'AUTH_PASSWORD_CHANGE',
  AUTH_FAILED_LOGIN: 'AUTH_FAILED_LOGIN',
  
  // Content moderation
  MODERATION_REPORT_CREATE: 'MODERATION_REPORT_CREATE',
  MODERATION_REPORT_APPROVE: 'MODERATION_REPORT_APPROVE',
  MODERATION_REPORT_REJECT: 'MODERATION_REPORT_REJECT',
  MODERATION_CONTENT_HIDE: 'MODERATION_CONTENT_HIDE',
  MODERATION_CONTENT_DELETE: 'MODERATION_CONTENT_DELETE',
} as const;