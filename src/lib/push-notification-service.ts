import webpush from 'web-push';
import { prisma } from './prisma';

// Configure web-push with VAPID keys
if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
  webpush.setVapidDetails(
    'mailto:<EMAIL>',
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );
}

export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: Record<string, unknown>;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

export class PushNotificationService {
  /**
   * Send push notification to a specific user
   */
  static async sendToUser(userId: string, payload: PushNotificationPayload): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { pushSubscription: true }
      });

      if (!user?.pushSubscription) {
        console.log(`User ${userId} has no push subscription`);
        return false;
      }

      const subscription = JSON.parse(user.pushSubscription);
      await webpush.sendNotification(subscription, JSON.stringify(payload));
      
      console.log(`Push notification sent to user ${userId}`);
      return true;
    } catch (error) {
      console.error(`Failed to send push notification to user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Send push notification to multiple users
   */
  static async sendToUsers(userIds: string[], payload: PushNotificationPayload): Promise<{ success: number; failed: number }> {
    const results = await Promise.allSettled(
      userIds.map(userId => this.sendToUser(userId, payload))
    );

    const success = results.filter(result => result.status === 'fulfilled' && result.value).length;
    const failed = results.length - success;

    console.log(`Push notifications sent: ${success} success, ${failed} failed`);
    return { success, failed };
  }

  /**
   * Send push notification to all users with push subscriptions
   */
  static async sendToAllUsers(payload: PushNotificationPayload): Promise<{ success: number; failed: number }> {
    try {
      const users = await prisma.user.findMany({
        where: {
          pushSubscription: {
            not: null
          }
        },
        select: { id: true }
      });

      const userIds = users.map(user => user.id);
      return await this.sendToUsers(userIds, payload);
    } catch (error) {
      console.error('Failed to send push notifications to all users:', error);
      return { success: 0, failed: 0 };
    }
  }

  /**
   * Send push notification for new NWA video
   */
  static async sendNewVideoNotification(videoId: string, videoTitle: string): Promise<{ success: number; failed: number }> {
    try {
      // Get users who have NWA video notifications enabled and push subscriptions
      const users = await prisma.user.findMany({
        where: {
          nwaVideoNotifications: true,
          pushSubscription: {
            not: null
          }
        },
        select: { id: true }
      });

      const payload: PushNotificationPayload = {
        title: 'New NWA Video Available!',
        body: `Check out the new video: ${videoTitle}`,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        data: {
          videoId,
          type: 'new-video',
          url: '/dashboard'
        },
        actions: [
          {
            action: 'view',
            title: 'View Video',
            icon: '/icons/icon-96x96.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      const userIds = users.map(user => user.id);
      return await this.sendToUsers(userIds, payload);
    } catch (error) {
      console.error('Failed to send new video notification:', error);
      return { success: 0, failed: 0 };
    }
  }

  /**
   * Send push notification for user video link
   */
  static async sendUserVideoNotification(videoId: string, videoTitle: string, userName: string): Promise<{ success: number; failed: number }> {
    try {
      // Get users who have user video notifications enabled and push subscriptions
      const users = await prisma.user.findMany({
        where: {
          userVideoNotifications: true,
          pushSubscription: {
            not: null
          }
        },
        select: { id: true }
      });

      const payload: PushNotificationPayload = {
        title: 'New User Video Shared!',
        body: `${userName} shared: ${videoTitle}`,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        data: {
          videoId,
          type: 'user-video',
          url: '/dashboard'
        },
        actions: [
          {
            action: 'view',
            title: 'View Video',
            icon: '/icons/icon-96x96.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      };

      const userIds = users.map(user => user.id);
      return await this.sendToUsers(userIds, payload);
    } catch (error) {
      console.error('Failed to send user video notification:', error);
      return { success: 0, failed: 0 };
    }
  }

  /**
   * Clean up expired push subscriptions
   */
  static async cleanupExpiredSubscriptions(): Promise<number> {
    try {
      const users = await prisma.user.findMany({
        where: {
          pushSubscription: {
            not: null
          }
        },
        select: { id: true, pushSubscription: true }
      });

      let cleanedCount = 0;

      for (const user of users) {
        try {
          const subscription = JSON.parse(user.pushSubscription!);
          
          // Try to send a test notification to check if subscription is still valid
          await webpush.sendNotification(subscription, JSON.stringify({
            title: 'Test',
            body: 'Test notification',
            silent: true
          }));
        } catch (error: unknown) {
          // If subscription is invalid, remove it
          const webPushError = error as { statusCode?: number };
          if (webPushError.statusCode === 410 || webPushError.statusCode === 404) {
            await prisma.user.update({
              where: { id: user.id },
              data: {
                pushSubscription: null,
                pushSubscriptionEndpoint: null
              }
            });
            cleanedCount++;
            console.log(`Cleaned up expired subscription for user ${user.id}`);
          }
        }
      }

      return cleanedCount;
    } catch (error) {
      console.error('Failed to cleanup expired subscriptions:', error);
      return 0;
    }
  }
}