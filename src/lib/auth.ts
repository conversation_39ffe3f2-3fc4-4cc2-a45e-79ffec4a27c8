import NextAuth from 'next-auth';
import { createAuthOptions } from './auth-config';

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      role?: string;
    };
  }
  // Extend the user object to include the role property
  interface User {
    role?: string;
  }
}

export default NextAuth(createAuthOptions());

// Export authOptions for backward compatibility
export const authOptions = createAuthOptions();