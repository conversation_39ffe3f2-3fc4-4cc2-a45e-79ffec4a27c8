/**
 * Session Management Utility
 *
 * Enhanced session management with activity tracking, security monitoring,
 * and automatic cleanup of expired sessions.
 */

import { logger } from './logger';
import { tokenRefreshManager } from './token-refresh';

export interface SessionInfo {
  userId: string;
  email: string;
  role: string;
  createdAt: number;
  lastActivity: number;
  expiresAt: number;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
}

export interface SessionConfig {
  maxSessionAge: number; // Maximum session age in milliseconds
  inactivityTimeout: number; // Inactivity timeout in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  maxSessionsPerUser: number; // Maximum concurrent sessions per user
}

export class SessionManager {
  private static instance: SessionManager;
  private sessions = new Map<string, SessionInfo>();
  private userSessions = new Map<string, Set<string>>();
  private config: SessionConfig;
  private cleanupTimer?: NodeJS.Timeout;

  private defaultConfig: SessionConfig = {
    maxSessionAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    inactivityTimeout: 24 * 60 * 60 * 1000, // 24 hours
    cleanupInterval: 60 * 60 * 1000, // 1 hour
    maxSessionsPerUser: 5,
  };

  private constructor(config?: Partial<SessionConfig>) {
    this.config = { ...this.defaultConfig, ...config };
    this.startCleanupTimer();
  }

  static getInstance(config?: Partial<SessionConfig>): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager(config);
    }
    return SessionManager.instance;
  }

  /**
   * Create a new session
   */
  createSession(
    userId: string,
    email: string,
    role: string,
    ipAddress: string,
    userAgent: string,
    expiresAt: number
  ): SessionInfo {
    const sessionId = this.generateSessionId();
    const now = Date.now();

    const session: SessionInfo = {
      userId,
      email,
      role,
      createdAt: now,
      lastActivity: now,
      expiresAt,
      ipAddress,
      userAgent,
      sessionId,
    };

    // Store session
    this.sessions.set(sessionId, session);

    // Track user sessions
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set());
    }
    const userSessionSet = this.userSessions.get(userId)!;
    userSessionSet.add(sessionId);

    // Enforce max sessions per user
    this.enforceMaxSessionsPerUser(userId);

    logger.info('Session created', {
      userId,
      email,
      sessionId,
      ipAddress,
      userAgent: userAgent.substring(0, 100), // Truncate for logging
    });

    return session;
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): SessionInfo | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    // Check if session is expired
    if (this.isSessionExpired(session)) {
      this.destroySession(sessionId);
      return null;
    }

    // Update last activity
    session.lastActivity = Date.now();
    this.sessions.set(sessionId, session);

    return session;
  }

  /**
   * Update session activity
   */
  updateActivity(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    session.lastActivity = Date.now();
    this.sessions.set(sessionId, session);
    return true;
  }

  /**
   * Destroy a session
   */
  destroySession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Remove from main sessions map
    this.sessions.delete(sessionId);

    // Remove from user sessions
    const userSessionSet = this.userSessions.get(session.userId);
    if (userSessionSet) {
      userSessionSet.delete(sessionId);
      if (userSessionSet.size === 0) {
        this.userSessions.delete(session.userId);
      }
    }

    // Clean up token refresh state
    tokenRefreshManager.cleanup(session.userId);

    logger.info('Session destroyed', {
      userId: session.userId,
      email: session.email,
      sessionId,
      reason: 'explicit_logout',
    });
  }

  /**
   * Destroy all sessions for a user
   */
  destroyUserSessions(userId: string): void {
    const userSessionSet = this.userSessions.get(userId);
    if (!userSessionSet) return;

    const sessionIds = Array.from(userSessionSet);
    sessionIds.forEach(sessionId => this.destroySession(sessionId));

    logger.info('All user sessions destroyed', {
      userId,
      sessionCount: sessionIds.length,
    });
  }

  /**
   * Get all active sessions for a user
   */
  getUserSessions(userId: string): SessionInfo[] {
    const userSessionSet = this.userSessions.get(userId);
    if (!userSessionSet) return [];

    return Array.from(userSessionSet)
      .map(sessionId => this.sessions.get(sessionId))
      .filter((session): session is SessionInfo => session !== undefined);
  }

  /**
   * Check if session is expired
   */
  private isSessionExpired(session: SessionInfo): boolean {
    const now = Date.now();
    const ageExpired = now > session.expiresAt;
    const inactivityExpired = (now - session.lastActivity) > this.config.inactivityTimeout;

    return ageExpired || inactivityExpired;
  }

  /**
   * Enforce maximum sessions per user
   */
  private enforceMaxSessionsPerUser(userId: string): void {
    const userSessionSet = this.userSessions.get(userId);
    if (!userSessionSet) return;

    if (userSessionSet.size <= this.config.maxSessionsPerUser) return;

    // Remove oldest sessions
    const sessions = Array.from(userSessionSet)
      .map(sessionId => this.sessions.get(sessionId))
      .filter((session): session is SessionInfo => session !== undefined)
      .sort((a, b) => a.lastActivity - b.lastActivity);

    const sessionsToRemove = sessions.slice(0, userSessionSet.size - this.config.maxSessionsPerUser);
    sessionsToRemove.forEach(session => this.destroySession(session.sessionId));

    if (sessionsToRemove.length > 0) {
      logger.warn('Enforced max sessions per user', {
        userId,
        removedCount: sessionsToRemove.length,
        maxSessions: this.config.maxSessionsPerUser,
      });
    }
  }

  /**
   * Generate a secure session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Perform cleanup of expired sessions
   */
  private performCleanup(): void {
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (this.isSessionExpired(session)) {
        this.destroySession(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info('Session cleanup completed', {
        cleanedCount,
        remainingSessions: this.sessions.size,
      });
    }
  }

  /**
   * Get session statistics
   */
  getStats(): {
    totalSessions: number;
    uniqueUsers: number;
    averageSessionsPerUser: number;
  } {
    const totalSessions = this.sessions.size;
    const uniqueUsers = this.userSessions.size;
    const averageSessionsPerUser = uniqueUsers > 0 ? totalSessions / uniqueUsers : 0;

    return {
      totalSessions,
      uniqueUsers,
      averageSessionsPerUser,
    };
  }

  /**
   * Stop cleanup timer (for testing)
   */
  stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }
}

// Export singleton instance
export const sessionManager = SessionManager.getInstance();