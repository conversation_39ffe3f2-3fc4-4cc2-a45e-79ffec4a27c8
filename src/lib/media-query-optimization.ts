import { prisma } from './prisma';
import type { Prisma } from '@/generated/prisma';

/**
 * Media query optimization utilities for the unified Media model
 * Supports both videos and ebooks with proper relationship loading
 */

// Type definitions for optimized media queries
export interface OptimizedMedia {
  id: string;
  type: string; // 'video' or 'ebook'
  title: string;
  description: string | null;
  videoUrl: string | null; // For videos
  fileUrl: string | null; // For ebooks (PDFs)
  releaseTime: Date | null; // For scheduled ebooks
  published: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  user: {
    id: string;
    name: string | null;
    role: string;
  };
  _count: {
    likes: number;
    shares: number;
    completions: number;
  };
  userInteractions?: {
    liked: boolean;
    shared: boolean;
    completed: boolean;
  };
}

/**
 * Optimized media queries with proper includes to prevent N+1 queries
 */
export class MediaQueryOptimizer {
  /**
   * Get media items with all related data in a single query
   */
  static async getMediaWithRelations(
    where: Prisma.MediaWhereInput,
    options: {
      take?: number;
      skip?: number;
      orderBy?: Prisma.MediaOrderByWithRelationInput[];
      userId?: string; // For user-specific interactions
    } = {}
  ): Promise<OptimizedMedia[]> {
    const { take = 10, skip = 0, orderBy = [{ createdAt: 'desc' }], userId } = options;

    const media = await prisma.media.findMany({
      where,
      take,
      skip,
      orderBy,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        _count: {
          select: {
            likes: true,
            shares: true,
            completions: true,
          },
        },
        // Include user-specific interactions if userId provided
        ...(userId && {
          likes: {
            where: { userId },
            select: { id: true },
          },
          shares: {
            where: { userId },
            select: { id: true },
          },
          completions: {
            where: { userId },
            select: { id: true },
          },
        }),
      },
    });

    // Transform to include user interactions if userId provided
    return media.map(item => ({
      ...item,
      userInteractions: userId ? {
        liked: Array.isArray(item.likes) && item.likes.length > 0,
        shared: Array.isArray(item.shares) && item.shares.length > 0,
        completed: Array.isArray(item.completions) && item.completions.length > 0,
      } : undefined,
    })) as OptimizedMedia[];
  }

  /**
   * Get NWA media items (both videos and ebooks) for dashboard
   */
  static async getNWAMediaForUser(userId: string, limit = 10): Promise<OptimizedMedia[]> {
    return this.getMediaWithRelations(
      {
        user: {
          role: { in: ['ADMIN', 'NWA_TEAM'] },
        },
        published: true,
        // Only show items not completed by the user
        completions: {
          none: { userId },
        },
        // For ebooks, only show released ones
        OR: [
          { type: 'video' },
          { 
            type: 'ebook',
            OR: [
              { releaseTime: null },
              { releaseTime: { lte: new Date() } }
            ]
          }
        ]
      },
      {
        take: limit,
        orderBy: [
          { createdAt: 'asc' }, // Oldest first as per requirement
        ],
        userId,
      }
    );
  }

  /**
   * Get NWA videos specifically for dashboard (backwards compatibility)
   */
  static async getNWAVideosForUser(userId: string, limit = 5): Promise<OptimizedMedia[]> {
    return this.getMediaWithRelations(
      {
        type: 'video',
        user: {
          role: { in: ['ADMIN', 'NWA_TEAM'] },
        },
        published: true,
        completions: {
          none: { userId },
        },
      },
      {
        take: limit,
        orderBy: [
          { createdAt: 'asc' }, // Oldest first as per requirement
        ],
        userId,
      }
    );
  }

  /**
   * Get NWA ebooks for dashboard
   */
  static async getNWAEbooksForUser(userId: string, limit = 5): Promise<OptimizedMedia[]> {
    return this.getMediaWithRelations(
      {
        type: 'ebook',
        user: {
          role: { in: ['ADMIN', 'NWA_TEAM'] },
        },
        published: true,
        // Only show released ebooks
        OR: [
          { releaseTime: null },
          { releaseTime: { lte: new Date() } }
        ],
        completions: {
          none: { userId },
        },
      },
      {
        take: limit,
        orderBy: [
          { createdAt: 'desc' },
        ],
        userId,
      }
    );
  }

  /**
   * Search media items with optimized query
   */
  static async searchMedia(
    searchQuery: string,
    filters: {
      type?: 'video' | 'ebook' | 'all';
      platform?: string;
      userId?: string;
      published?: string;
      userRole?: string;
    } = {},
    pagination: { take?: number; skip?: number } = {}
  ): Promise<{ media: OptimizedMedia[]; total: number }> {
    const { type = 'all', platform, userId: filterUserId, published = 'published', userRole } = filters;
    const { take = 10, skip = 0 } = pagination;

    // Build where clause
    const whereClause: Prisma.MediaWhereInput = {
      OR: [
        {
          title: {
            contains: searchQuery,
          },
        },
        {
          description: {
            contains: searchQuery,
          },
        },
      ],
    };

    // Handle published filter based on user role
    if (published === 'published') {
      whereClause.published = true;
    } else if (published === 'unpublished') {
      if (!userRole || !['ADMIN', 'NWA_TEAM'].includes(userRole)) {
        whereClause.published = true; // Force published for non-admins
      } else {
        whereClause.published = false;
      }
    } else if (published === 'all') {
      if (!userRole || !['ADMIN', 'NWA_TEAM'].includes(userRole)) {
        whereClause.published = true; // Force published for non-admins
      }
    } else {
      // Default: only published content for non-admins
      if (!userRole || !['ADMIN', 'NWA_TEAM'].includes(userRole)) {
        whereClause.published = true;
      }
    }

    // Add type filter
    if (type === 'video') {
      whereClause.type = 'video';
    } else if (type === 'ebook') {
      whereClause.type = 'ebook';
    }

    // Add user filter
    if (filterUserId) {
      whereClause.userId = filterUserId;
    }

    // Add platform filter for videos
    if (type === 'video' && platform) {
      switch (platform.toLowerCase()) {
        case 'youtube':
          whereClause.videoUrl = { contains: 'youtube.com' };
          break;
        case 'tiktok':
          whereClause.videoUrl = { contains: 'tiktok.com' };
          break;
        case 'rumble':
          whereClause.videoUrl = { contains: 'rumble.com' };
          break;
      }
    }

    // Handle scheduled release for ebooks
    if (type === 'ebook' || type === 'all') {
      if (!userRole || !['ADMIN', 'NWA_TEAM'].includes(userRole)) {
        // Add condition to only show released ebooks for non-admins
        if (type === 'ebook') {
          whereClause.OR = [
            ...(whereClause.OR || []),
            { 
              AND: [
                { type: 'ebook' },
                { 
                  OR: [
                    { releaseTime: null },
                    { releaseTime: { lte: new Date() } }
                  ]
                }
              ]
            }
          ];
        } else {
          // For 'all' type, modify the where clause to handle both videos and ebooks properly
          const originalOR = whereClause.OR;
          whereClause.AND = [
            { OR: originalOR },
            {
              OR: [
                { type: 'video' },
                {
                  AND: [
                    { type: 'ebook' },
                    {
                      OR: [
                        { releaseTime: null },
                        { releaseTime: { lte: new Date() } }
                      ]
                    }
                  ]
                }
              ]
            }
          ];
          delete whereClause.OR;
        }
      }
    }

    const [media, total] = await Promise.all([
      this.getMediaWithRelations(whereClause, { take, skip }),
      prisma.media.count({ where: whereClause }),
    ]);

    return { media, total };
  }

  /**
   * Get user stats for media interactions
   */
  static async getUserMediaStats(userId: string) {
    const [totalLikes, totalShares, totalCompletions] = await Promise.all([
      prisma.like.count({ where: { userId } }),
      prisma.share.count({ where: { userId } }),
      prisma.completedLink.count({ where: { userId } }),
    ]);

    return {
      totalLikes,
      totalShares,
      totalCompletions,
    };
  }

  /**
   * Get dashboard statistics for media
   */
  static async getMediaDashboardStats() {
    const [videoStats, ebookStats, totalUsers] = await Promise.all([
      prisma.media.aggregate({
        _count: { id: true },
        where: { 
          type: 'video',
          published: true 
        },
      }),
      prisma.media.aggregate({
        _count: { id: true },
        where: { 
          type: 'ebook',
          published: true 
        },
      }),
      prisma.user.count(),
    ]);

    return {
      totalVideos: videoStats._count.id,
      totalEbooks: ebookStats._count.id,
      totalMedia: videoStats._count.id + ebookStats._count.id,
      totalUsers,
    };
  }
}
