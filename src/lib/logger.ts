/**
 * Structured Logging Utility
 *
 * Provides consistent logging levels and structured output for the application.
 * Supports different log levels and structured data formatting.
 */

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

export interface LogContext {
  userId?: string;
  email?: string;
  sessionId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  [key: string]: unknown;
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  private formatLogEntry(entry: LogEntry): string {
    const { timestamp, level, message, context, error } = entry;

    if (this.isDevelopment) {
      // Development: Pretty-printed with colors
      const levelColors = {
        [LogLevel.ERROR]: '\x1b[31m', // Red
        [LogLevel.WARN]: '\x1b[33m',  // Yellow
        [LogLevel.INFO]: '\x1b[36m',  // Cyan
        [LogLevel.DEBUG]: '\x1b[35m', // Magenta
      };
      const resetColor = '\x1b[0m';

      let output = `${levelColors[level]}[${level.toUpperCase()}]${resetColor} ${timestamp} ${message}`;

      if (context && Object.keys(context).length > 0) {
        output += `\n${levelColors[level]}Context:${resetColor} ${JSON.stringify(context, null, 2)}`;
      }

      if (error) {
        output += `\n${levelColors[level]}Error:${resetColor} ${error.name}: ${error.message}`;
        if (error.stack) {
          output += `\n${levelColors[level]}Stack:${resetColor}\n${error.stack}`;
        }
      }

      return output;
    } else {
      // Production: JSON format for log aggregation
      return JSON.stringify(entry);
    }
  }

  private log(level: LogLevel, message: string, context?: LogContext, error?: Error): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: this.isDevelopment ? error.stack : undefined,
      };
    }

    const formattedEntry = this.formatLogEntry(entry);

    switch (level) {
      case LogLevel.ERROR:
        console.error(formattedEntry);
        break;
      case LogLevel.WARN:
        console.warn(formattedEntry);
        break;
      case LogLevel.INFO:
        console.info(formattedEntry);
        break;
      case LogLevel.DEBUG:
        if (this.isDevelopment) {
          console.debug(formattedEntry);
        }
        break;
    }
  }

  error(message: string, context?: LogContext, error?: Error): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: LogContext): void {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  // Specialized logging methods for common use cases
  auth = {
    signIn: (email: string, provider: string, context?: LogContext) =>
      this.info('User signed in', { ...context, email, provider, action: 'sign_in' }),

    signOut: (email: string, context?: LogContext) =>
      this.info('User signed out', { ...context, email, action: 'sign_out' }),

    tokenExpired: (email: string, context?: LogContext) =>
      this.warn('Token expired', { ...context, email, action: 'token_expired' }),

    oauthError: (error: string, context?: LogContext) =>
      this.error('OAuth error', { ...context, error, action: 'oauth_error' }),
  };

  security = {
    rateLimitExceeded: (identifier: string, context?: LogContext) =>
      this.warn('Rate limit exceeded', { ...context, identifier, action: 'rate_limit_exceeded' }),

    csrfViolation: (path: string, context?: LogContext) =>
      this.error('CSRF violation blocked', { ...context, path, action: 'csrf_violation' }),

    invalidToken: (context?: LogContext) =>
      this.warn('Invalid token', { ...context, action: 'invalid_token' }),
  };

  middleware = {
    authSuccess: (email: string, path: string, context?: LogContext) =>
      this.info('Authentication successful', { ...context, email, path, action: 'auth_success' }),

    authFailure: (path: string, reason: string, context?: LogContext) =>
      this.warn('Authentication failed', { ...context, path, reason, action: 'auth_failure' }),

    redirect: (from: string, to: string, context?: LogContext) =>
      this.debug('Redirect', { ...context, from, to, action: 'redirect' }),
  };
}

export const logger = new Logger();