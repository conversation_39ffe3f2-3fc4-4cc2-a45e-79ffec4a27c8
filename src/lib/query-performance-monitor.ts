import { prisma } from './prisma';

/**
 * Query Performance Monitoring Utility
 * Tracks and logs slow queries, provides performance metrics
 */

interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: Date;
  userId?: string;
  endpoint?: string;
  params?: Record<string, unknown>;
}

interface PerformanceThresholds {
  slow: number; // milliseconds
  critical: number; // milliseconds
}

class QueryPerformanceMonitor {
  private static instance: QueryPerformanceMonitor;
  private metrics: QueryMetrics[] = [];
  private thresholds: PerformanceThresholds = {
    slow: 1000, // 1 second
    critical: 3000, // 3 seconds
  };

  private constructor() {}

  static getInstance(): QueryPerformanceMonitor {
    if (!QueryPerformanceMonitor.instance) {
      QueryPerformanceMonitor.instance = new QueryPerformanceMonitor();
    }
    return QueryPerformanceMonitor.instance;
  }

  /**
   * Monitor a database query execution time
   */
  async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    context?: {
      userId?: string;
      endpoint?: string;
      params?: Record<string, unknown>;
    }
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      
      this.recordMetric({
        query: queryName,
        duration,
        timestamp: new Date(),
        ...context,
      });

      // Log slow queries
      if (duration > this.thresholds.slow) {
        this.logSlowQuery(queryName, duration, context);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.recordMetric({
        query: `${queryName} (ERROR)`,
        duration,
        timestamp: new Date(),
        ...context,
      });

      console.error(`Query ${queryName} failed after ${duration}ms:`, error);
      throw error;
    }
  }

  /**
   * Record query metrics
   */
  private recordMetric(metric: QueryMetrics) {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Log slow queries for monitoring
   */
  private logSlowQuery(
    queryName: string,
    duration: number,
    context?: {
      userId?: string;
      endpoint?: string;
      params?: Record<string, unknown>;
    }
  ) {
    const level = duration > this.thresholds.critical ? 'CRITICAL' : 'SLOW';
    
    console.warn(`[${level} QUERY] ${queryName} took ${duration}ms`, {
      duration,
      queryName,
      ...context,
      timestamp: new Date().toISOString(),
    });

    // In production, you might want to send this to a monitoring service
    // like DataDog, New Relic, or custom analytics
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(timeWindow?: number): {
    totalQueries: number;
    averageDuration: number;
    slowQueries: number;
    criticalQueries: number;
    topSlowQueries: Array<{ query: string; avgDuration: number; count: number }>;
  } {
    const cutoffTime = timeWindow ? Date.now() - timeWindow : 0;
    const relevantMetrics = this.metrics.filter(
      m => m.timestamp.getTime() > cutoffTime
    );

    if (relevantMetrics.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        criticalQueries: 0,
        topSlowQueries: [],
      };
    }

    const totalDuration = relevantMetrics.reduce((sum, m) => sum + m.duration, 0);
    const slowQueries = relevantMetrics.filter(m => m.duration > this.thresholds.slow);
    const criticalQueries = relevantMetrics.filter(m => m.duration > this.thresholds.critical);

    // Group by query name and calculate averages
    const queryGroups = relevantMetrics.reduce((groups, metric) => {
      const key = metric.query;
      if (!groups[key]) {
        groups[key] = { totalDuration: 0, count: 0 };
      }
      groups[key].totalDuration += metric.duration;
      groups[key].count += 1;
      return groups;
    }, {} as Record<string, { totalDuration: number; count: number }>);

    const topSlowQueries = Object.entries(queryGroups)
      .map(([query, stats]) => ({
        query,
        avgDuration: stats.totalDuration / stats.count,
        count: stats.count,
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    return {
      totalQueries: relevantMetrics.length,
      averageDuration: totalDuration / relevantMetrics.length,
      slowQueries: slowQueries.length,
      criticalQueries: criticalQueries.length,
      topSlowQueries,
    };
  }

  /**
   * Clear metrics (useful for testing or memory management)
   */
  clearMetrics() {
    this.metrics = [];
  }

  /**
   * Update performance thresholds
   */
  setThresholds(thresholds: Partial<PerformanceThresholds>) {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }
}

// Singleton instance
export const queryPerformanceMonitor = QueryPerformanceMonitor.getInstance();

/**
 * Decorator function to monitor query performance
 */
export function monitorQuery(queryName: string, context?: {
  userId?: string;
  endpoint?: string;
  params?: Record<string, unknown>;
}) {
  return function <T extends (...args: unknown[]) => Promise<unknown>>(
    target: unknown,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;
    
    descriptor.value = (async function (this: unknown, ...args: unknown[]) {
      // Get class name safely
      const className = target && typeof target === 'object' && target.constructor && target.constructor.name
        ? target.constructor.name
        : 'UnknownClass';
        
      return queryPerformanceMonitor.monitorQuery(
        `${className}.${propertyName}`,
        () => method.apply(this, args),
        context
      );
    }) as T;
  };
}

/**
 * Database health check utilities
 */
export class DatabaseHealthMonitor {
  /**
   * Check database connection and performance
   */
  static async healthCheck(): Promise<{
    connected: boolean;
    responseTime: number;
    slowQueries: number;
    criticalQueries: number;
  }> {
    const startTime = Date.now();
    
    try {
      // Simple query to test connection
      await prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;
      
      // Get performance stats from the last hour
      const stats = queryPerformanceMonitor.getPerformanceStats(60 * 60 * 1000);
      
      return {
        connected: true,
        responseTime,
        slowQueries: stats.slowQueries,
        criticalQueries: stats.criticalQueries,
      };
    } catch (error) {
      console.error('Database health check failed:', error);
      return {
        connected: false,
        responseTime: Date.now() - startTime,
        slowQueries: 0,
        criticalQueries: 0,
      };
    }
  }

  /**
   * Get database performance metrics
   */
  static async getPerformanceMetrics() {
    try {
      // Get table sizes and row counts
      const [videoCount, userCount, engagementCount] = await Promise.all([
        prisma.video.count(),
        prisma.user.count(),
        prisma.userEngagement.count(),
      ]);

      // Get recent query performance
      const queryStats = queryPerformanceMonitor.getPerformanceStats(60 * 60 * 1000); // Last hour

      return {
        tableSizes: {
          videos: videoCount,
          users: userCount,
          engagements: engagementCount,
        },
        queryPerformance: queryStats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      throw error;
    }
  }
}

/**
 * Utility functions for common monitored queries
 */
export const monitoredQueries = {
  /**
   * Monitor video queries
   */
  async getVideos<T>(queryFn: () => Promise<T>, context?: { userId?: string; filters?: unknown }): Promise<T> {
    return queryPerformanceMonitor.monitorQuery(
      'getVideos',
      queryFn,
      { endpoint: '/api/videos', ...context }
    );
  },

  /**
   * Monitor dashboard queries
   */
  async getDashboard<T>(queryFn: () => Promise<T>, userId: string): Promise<T> {
    return queryPerformanceMonitor.monitorQuery(
      'getDashboard',
      queryFn,
      { endpoint: '/api/dashboard', userId }
    );
  },

  /**
   * Monitor leaderboard queries
   */
  async getLeaderboard<T>(queryFn: () => Promise<T>): Promise<T> {
    return queryPerformanceMonitor.monitorQuery(
      'getLeaderboard',
      queryFn,
      { endpoint: '/api/leaderboard' }
    );
  },

  /**
   * Monitor engagement tracking
   */
  async trackEngagement<T>(queryFn: () => Promise<T>, context: { userId: string; videoId: string; action: string }): Promise<T> {
    return queryPerformanceMonitor.monitorQuery(
      'trackEngagement',
      queryFn,
      { endpoint: '/api/engagement/track', ...context }
    );
  },
};