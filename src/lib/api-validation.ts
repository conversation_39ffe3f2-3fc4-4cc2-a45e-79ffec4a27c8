import { NextRequest } from 'next/server';
import { createApiError } from './api-errors';
import { InputSanitizer, SQLInjectionPrevention, ContentSecurity } from './security';

// Validation result interface
export interface ValidationResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

// Field validation rules
export interface FieldRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'url';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: string[];
  custom?: (value: unknown) => string | null; // Return error message or null if valid
}

export interface ValidationSchema {
  [fieldName: string]: FieldRule;
}

// Validate a single field
function validateField(
  fieldName: string,
  value: unknown,
  rule: FieldRule
): ValidationError | null {
  // Check required
  if (rule.required && (value === undefined || value === null || value === '')) {
    return {
      field: fieldName,
      message: `${fieldName} is required`,
      code: 'REQUIRED',
      value,
    };
  }

  // Skip other validations if field is not required and empty
  if (!rule.required && (value === undefined || value === null || value === '')) {
    return null;
  }

  // Type validation
  if (rule.type) {
    switch (rule.type) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field: fieldName,
            message: `${fieldName} must be a string`,
            code: 'INVALID_TYPE',
            value,
          };
        }
        break;
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a number`,
            code: 'INVALID_TYPE',
            value,
          };
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          return {
            field: fieldName,
            message: `${fieldName} must be a boolean`,
            code: 'INVALID_TYPE',
            value,
          };
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be an array`,
            code: 'INVALID_TYPE',
            value,
          };
        }
        break;
      case 'object':
        if (typeof value !== 'object' || Array.isArray(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be an object`,
            code: 'INVALID_TYPE',
            value,
          };
        }
        break;
      case 'email':
        if (typeof value !== 'string' || !isValidEmail(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a valid email address`,
            code: 'INVALID_EMAIL',
            value,
          };
        }
        break;
      case 'url':
        if (typeof value !== 'string' || !isValidUrl(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a valid URL`,
            code: 'INVALID_URL',
            value,
          };
        }
        break;
    }
  }

  // String length validation
  if (typeof value === 'string') {
    if (rule.minLength && value.length < rule.minLength) {
      return {
        field: fieldName,
        message: `${fieldName} must be at least ${rule.minLength} characters long`,
        code: 'MIN_LENGTH',
        value,
      };
    }
    if (rule.maxLength && value.length > rule.maxLength) {
      return {
        field: fieldName,
        message: `${fieldName} must be no more than ${rule.maxLength} characters long`,
        code: 'MAX_LENGTH',
        value,
      };
    }
  }

  // Number range validation
  if (typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      return {
        field: fieldName,
        message: `${fieldName} must be at least ${rule.min}`,
        code: 'MIN_VALUE',
        value,
      };
    }
    if (rule.max !== undefined && value > rule.max) {
      return {
        field: fieldName,
        message: `${fieldName} must be no more than ${rule.max}`,
        code: 'MAX_VALUE',
        value,
      };
    }
  }

  // Pattern validation
  if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
    return {
      field: fieldName,
      message: `${fieldName} format is invalid`,
      code: 'INVALID_PATTERN',
      value,
    };
  }

  // Enum validation
  if (rule.enum && typeof value === 'string' && !rule.enum.includes(value)) {
    return {
      field: fieldName,
      message: `${fieldName} must be one of: ${rule.enum.join(', ')}`,
      code: 'INVALID_ENUM',
      value,
    };
  }

  // Custom validation
  if (rule.custom) {
    const customError = rule.custom(value);
    if (customError) {
      return {
        field: fieldName,
        message: customError,
        code: 'CUSTOM_VALIDATION',
        value,
      };
    }
  }

  return null;
}

// Helper functions
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Main validation function
export function validateData<T = unknown>(
  data: unknown,
  schema: ValidationSchema
): ValidationResult<T> {
  const errors: ValidationError[] = [];

  // Validate each field in the schema
  for (const [fieldName, rule] of Object.entries(schema)) {
    const value = (typeof data === 'object' && data !== null) ? (data as Record<string, unknown>)[fieldName] : undefined;
    const error = validateField(fieldName, value, rule);
    if (error) {
      errors.push(error);
    }
  }

  if (errors.length > 0) {
    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: data as T,
  };
}

// Parse and validate JSON request body with security checks
export async function parseAndValidateBody<T = unknown>(
  request: NextRequest,
  schema: ValidationSchema
): Promise<T> {
  let body: unknown;

  try {
    const rawBody = await request.text();
    
    // Check for excessively large payloads (DoS protection)
    if (rawBody.length > 1024 * 1024) { // 1MB limit
      throw createApiError.validation('Request body too large');
    }

    // Parse JSON
    body = JSON.parse(rawBody);
    
    // Check for deeply nested objects (DoS protection)
    if (getObjectDepth(body) > 10) {
      throw createApiError.validation('Request body structure too complex');
    }

  } catch (_error) {
    if (_error instanceof SyntaxError) {
      throw createApiError.validation('Invalid JSON in request body');
    }
    throw _error;
  }

  const validation = validateData<T>(body, schema);

  if (!validation.success) {
    throw createApiError.validation('Request validation failed', validation.errors);
  }

  return validation.data!;
}

// Helper function to calculate object depth
function getObjectDepth(obj: unknown, depth = 0): number {
  if (depth > 20) return depth; // Prevent stack overflow
  
  if (obj === null || typeof obj !== 'object') {
    return depth;
  }

  if (Array.isArray(obj)) {
    return Math.max(depth, ...obj.map(item => getObjectDepth(item, depth + 1)));
  }

  const values = Object.values(obj);
  if (values.length === 0) {
    return depth;
  }

  return Math.max(depth, ...values.map(value => getObjectDepth(value, depth + 1)));
}

// Validate query parameters
export function validateQueryParams(
  request: NextRequest,
  schema: ValidationSchema
): ValidationResult {
  const { searchParams } = new URL(request.url);
  const queryData: Record<string, unknown> = {};

  // Convert URLSearchParams to object
  searchParams.forEach((value, key) => {
    queryData[key] = value;
  });

  return validateData(queryData, schema);
}

// Common validation schemas
export const commonSchemas = {
  // Pagination parameters
  pagination: {
    page: {
      type: 'string' as const,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return 'Page must be a valid number';
        const num = parseInt(value, 10);
        if (isNaN(num)) return 'Page must be a valid number';
        if (num < 1) return 'Page must be at least 1';
        return null;
      },
    },
    limit: {
      type: 'string' as const,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return 'Limit must be a valid number';
        const num = parseInt(value, 10);
        if (isNaN(num)) return 'Limit must be a valid number';
        if (num < 1) return 'Limit must be at least 1';
        if (num > 100) return 'Limit must be no more than 100';
        return null;
      },
    },
    offset: {
      type: 'string' as const,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return 'Offset must be a valid number';
        const num = parseInt(value, 10);
        if (isNaN(num)) return 'Offset must be a valid number';
        if (num < 0) return 'Offset must be at least 0';
        return null;
      },
    },
  },

  // Video creation
  createVideo: {
    title: {
      required: true,
      type: 'string' as const,
      minLength: 1,
      maxLength: 200,
    },
    description: {
      type: 'string' as const,
      maxLength: 1000,
    },
    platforms: {
      required: true,
      type: 'object' as const,
      custom: (value: unknown) => {
        if (!value || typeof value !== 'object') {
          return 'Platforms must be an object';
        }
        
        if (typeof value === 'object' && value !== null) {
          const { youtubeUrl, tiktokUrl, rumbleUrl } = value as { youtubeUrl?: string; tiktokUrl?: string; rumbleUrl?: string };
          const hasAnyUrl = youtubeUrl || tiktokUrl || rumbleUrl;
        
          if (!hasAnyUrl) {
            return 'At least one platform URL is required';
          }
        } else {
          return 'Platforms must be an object';
        }

        // Validate URL formats
        if (typeof value === 'object' && value !== null) {
          const { youtubeUrl, tiktokUrl, rumbleUrl } = value as { youtubeUrl?: string; tiktokUrl?: string; rumbleUrl?: string };
          const hasAnyUrl = youtubeUrl || tiktokUrl || rumbleUrl;
          if (!hasAnyUrl) {
            return 'At least one platform URL is required';
          }
          if (youtubeUrl && !isValidUrl(youtubeUrl)) {
            return 'YouTube URL is invalid';
          }
          if (tiktokUrl && !isValidUrl(tiktokUrl)) {
            return 'TikTok URL is invalid';
          }
          if (rumbleUrl && !isValidUrl(rumbleUrl)) {
            return 'Rumble URL is invalid';
          }
        } else {
          return 'Platforms must be an object';
        }

        return null;
      },
    },
    thumbnailUrl: {
      type: 'url' as const,
    },
    duration: {
      type: 'number' as const,
      min: 0,
    },
    type: {
      type: 'string' as const,
      enum: ['nwa', 'user'],
    },
  },

  // Engagement tracking
  trackEngagement: {
    videoId: {
      required: true,
      type: 'string' as const,
      minLength: 1,
    },
    platform: {
      required: true,
      type: 'string' as const,
      enum: ['youtube', 'tiktok', 'rumble'],
    },
    action: {
      required: true,
      type: 'string' as const,
      enum: ['like', 'share', 'complete'],
    },
  },

  // Notification sending
  sendNotification: {
    videoIds: {
      required: true,
      type: 'array' as const,
      custom: (value: unknown) => {
        if (!Array.isArray(value) || value.length === 0) {
          return 'videoIds must be a non-empty array';
        }
        if (value.some(id => typeof id !== 'string' || !id.trim())) {
          return 'All video IDs must be non-empty strings';
        }
        return null;
      },
    },
    type: {
      type: 'string' as const,
      enum: ['nwa', 'user'],
    },
  },

  // Search parameters
  search: {
    query: {
      type: 'string' as const,
      minLength: 1,
      maxLength: 100,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return null;
        
        // Sanitize search query to prevent SQL injection
        try {
          const sanitized = SQLInjectionPrevention.sanitizeSearchQuery(value);
          if (sanitized !== value) {
            return 'Search query contains invalid characters';
          }
        } catch (_error) {
          return 'Invalid search query format';
        }
        
        return null;
      },
    },
    type: {
      type: 'string' as const,
      enum: ['nwa', 'user', 'all'],
    },
    platform: {
      type: 'string' as const,
      enum: ['youtube', 'tiktok', 'rumble'],
    },
    status: {
      type: 'string' as const,
      enum: ['draft', 'published', 'archived'],
    },
    featured: {
      type: 'string' as const,
      custom: (value: unknown) => {
        // If value is undefined, null, or empty string, it's valid (optional)
        if (value === undefined || value === null || value === '') return null;
        if (value === 'true') return null;
        if (value === 'false') return null;
        if (typeof value === 'boolean') return null;
        return 'Featured must be true or false';
      },
    },
  },

  // Secure video creation with enhanced validation
  secureCreateVideo: {
    title: {
      required: true,
      type: 'string' as const,
      minLength: 1,
      maxLength: 200,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return null;
        
        // Sanitize and validate title
        try {
          const sanitized = InputSanitizer.sanitizeText(value);
          if (sanitized.length === 0) {
            return 'Title cannot be empty after sanitization';
          }
          
          // Check for malicious content
          const threats = ContentSecurity.scanForMaliciousContent(value);
          if (threats.length > 0) {
            return 'Title contains potentially malicious content';
          }
        } catch (_error) {
          return 'Invalid title format';
        }
        
        return null;
      },
    },
    description: {
      type: 'string' as const,
      maxLength: 1000,
      custom: (value: unknown) => {
        if (!value || typeof value !== 'string') return null;
        
        // Sanitize and validate description
        try {
          InputSanitizer.sanitizeHtml(value);
          
          // Check for malicious content
          const threats = ContentSecurity.scanForMaliciousContent(value);
          if (threats.length > 0) {
            return 'Description contains potentially malicious content';
          }
        } catch (_error) {
          return 'Invalid description format';
        }
        
        return null;
      },
    },
    platforms: {
      required: true,
      type: 'object' as const,
      custom: (value: unknown) => {
        if (!value || typeof value !== 'object') {
          return 'Platforms must be an object';
        }
        
        try {
          // Use secure platform URL sanitization
          const sanitized = InputSanitizer.sanitizePlatformUrls(value);
          const { youtubeUrl, tiktokUrl, rumbleUrl } = sanitized as { youtubeUrl?: string; tiktokUrl?: string; rumbleUrl?: string };
          const hasAnyUrl = youtubeUrl || tiktokUrl || rumbleUrl;
          
          if (!hasAnyUrl) {
            return 'At least one platform URL is required';
          }
        } catch (_error) {
          return _error instanceof Error ? _error.message : 'Invalid platform URLs';
        }
        
        return null;
      },
    },
    thumbnailUrl: {
      type: 'string' as const,
      custom: (value: unknown) => {
        if (!value || typeof value !== 'string') return null;
        
        try {
          InputSanitizer.sanitizeUrl(value);
        } catch (_error) {
          return 'Invalid thumbnail URL format';
        }
        
        return null;
      },
    },
    duration: {
      type: 'number' as const,
      min: 0,
      max: 86400, // 24 hours max
    },
    type: {
      type: 'string' as const,
      enum: ['nwa', 'user'],
    },
  },

  // Secure user input validation
  secureUserInput: {
    name: {
      type: 'string' as const,
      minLength: 1,
      maxLength: 100,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return null;
        
        try {
          const sanitized = InputSanitizer.sanitizeText(value);
          if (sanitized !== value) {
            return 'Name contains invalid characters';
          }
        } catch (_error) {
          return 'Invalid name format';
        }
        
        return null;
      },
    },
    email: {
      type: 'string' as const,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return null;
        
        try {
          InputSanitizer.sanitizeEmail(value);
        } catch (_error) {
          return _error instanceof Error ? _error.message : 'Invalid email format';
        }
        
        return null;
      },
    },
  },

  // Database ID validation
  databaseId: {
    id: {
      required: true,
      type: 'string' as const,
      custom: (value: unknown) => {
        if (typeof value !== 'string') return null;
        
        try {
          SQLInjectionPrevention.sanitizeQueryParam(value, 'uuid');
        } catch (_error) {
          return 'Invalid ID format';
        }
        
        return null;
      },
    },
  },
};

// Secure validation function that includes sanitization
export function validateAndSanitizeData<T = unknown>(
  data: unknown,
  schema: ValidationSchema,
  sanitize = true
): ValidationResult<T> {
  // First sanitize the data if requested
  let processedData = data;
  if (sanitize) {
    try {
      processedData = InputSanitizer.sanitizeObject(data);
    } catch (_error) {
      return {
        success: false,
        errors: [{
          field: 'root',
          message: 'Data sanitization failed',
          code: 'SANITIZATION_ERROR',
          value: data,
        }],
      };
    }
  }

  // Then validate the sanitized data
  return validateData<T>(processedData, schema);
}

// Secure query parameter validation
export function validateSecureQueryParams(
  request: NextRequest,
  schema: ValidationSchema
): ValidationResult {
  const { searchParams } = new URL(request.url);
  const queryData: Record<string, unknown> = {};

  // Convert URLSearchParams to object with sanitization
  searchParams.forEach((value, key) => {
    try {
      // Sanitize key and value
      const sanitizedKey = InputSanitizer.sanitizeText(key);
      const sanitizedValue = InputSanitizer.sanitizeText(value);
      queryData[sanitizedKey] = sanitizedValue;
    } catch (_error) {
      // Skip invalid parameters
      console.warn(`Skipping invalid query parameter: ${key}=${value}`);
    }
  });

  return validateData(queryData, schema);
}