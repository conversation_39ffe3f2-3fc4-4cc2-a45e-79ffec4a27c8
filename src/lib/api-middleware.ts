import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { RateLimiter, applyRateLimit } from './rate-limit';
import { createErrorResponse, createApiError, with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './api-errors';
import { ValidationSchema, parseAndValidateBody, validateQueryParams } from './api-validation';
import { InputSanitizer, CSRFProtection, SecurityHeaders } from './security';

// Middleware configuration interface
export interface ApiMiddlewareConfig {
  rateLimiter?: RateLimiter;
  requireAuth?: boolean;
  requireRoles?: string[];
  bodyValidation?: ValidationSchema;
  queryValidation?: ValidationSchema;
  skipRateLimitOnSuccess?: boolean;
  requireCSRF?: boolean;
  sanitizeInput?: boolean;
  securityHeaders?: boolean;
}

// Enhanced request interface with parsed data and route params
export interface EnhancedRequest<TBody = unknown, TQuery = unknown, TParams = unknown> extends NextRequest {
  user?: {
    id: string;
    email: string;
    name?: string;
    role: string;
  };
  validatedBody?: TBody;
  validatedQuery?: TQuery;
  params?: TParams;
}

// Middleware result interface
export interface MiddlewareResult {
  success: boolean;
  request?: EnhancedRequest;
  response?: NextResponse;
  rateLimitHeaders?: Record<string, string>;
}

// Main middleware function
export async function applyApiMiddleware<TBody = unknown, TQuery = unknown, TParams = unknown>(
  request: NextRequest,
  config: ApiMiddlewareConfig = {}
): Promise<MiddlewareResult> {
  const path = new URL(request.url).pathname;

  try {
    // 1. Apply rate limiting
    let rateLimitHeaders: Record<string, string> = {};
    if (config.rateLimiter) {
      const rateLimitResult = await applyRateLimit(request, config.rateLimiter);
      rateLimitHeaders = rateLimitResult.headers;

      if (!rateLimitResult.success) {
        return {
          success: false,
          response: createErrorResponse(
            createApiError.rateLimit(
              rateLimitResult.error!.message,
              rateLimitResult.error!.retryAfter
            ),
            path,
            rateLimitHeaders
          ),
        };
      }
    }

    // 2. Authentication check
    let user: unknown = null;
    if (config.requireAuth || config.requireRoles) {
      const session = await getServerSession(authOptions);
    
      if (!session || !session.user || typeof session.user !== 'object' || !('id' in session.user)) {
        return {
          success: false,
          response: createErrorResponse(
            createApiError.unauthorized(),
            path,
            rateLimitHeaders
          ),
        };
      }
    
      user = session.user;
    
      // 3. Role-based authorization
      if (config.requireRoles && config.requireRoles.length > 0) {
        const typedUser = user as { role?: string };
        if (!typedUser.role || !config.requireRoles.includes(typedUser.role)) {
          return {
            success: false,
            response: createErrorResponse(
              createApiError.forbidden(
                `Required role: ${config.requireRoles.join(' or ')}`
              ),
              path,
              rateLimitHeaders
            ),
          };
        }
      }
    }

    // 3.5. CSRF Protection (for state-changing operations)
    if (config.requireCSRF && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
      const typedUser = user as { id?: string };
      if (!typedUser.id) {
        return {
          success: false,
          response: createErrorResponse(
            createApiError.unauthorized('CSRF protection requires authentication'),
            path,
            rateLimitHeaders
          ),
        };
      }

      const csrfToken = CSRFProtection.extractTokenFromRequest(request);
      if (!csrfToken || !CSRFProtection.verifyToken(csrfToken, (user as { id: string }).id)) {
        return {
          success: false,
          response: createErrorResponse(
            createApiError.forbidden('Invalid or missing CSRF token'),
            path,
            rateLimitHeaders
          ),
        };
      }
    }

    // 4. Request body validation and sanitization
    let validatedBody: TBody | null = null;
    if (config.bodyValidation && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
      try {
        validatedBody = (await parseAndValidateBody(
          request,
          config.bodyValidation
        )) as TBody;
        
        // Sanitize input if configured
        if (config.sanitizeInput) {
          validatedBody = InputSanitizer.sanitizeObject(validatedBody) as TBody;
        }
      } catch (error) {
        return {
          success: false,
          response: createErrorResponse(error as Error, path, rateLimitHeaders),
        };
      }
    }

    // 5. Query parameter validation and sanitization
    let validatedQuery: TQuery | null = null;
    if (config.queryValidation) {
      const queryValidation = validateQueryParams(request, config.queryValidation);
      if (!queryValidation.success) {
        return {
          success: false,
          response: createErrorResponse(
            createApiError.validation('Query parameter validation failed', queryValidation.errors),
            path,
            rateLimitHeaders
          ),
        };
      }
      validatedQuery = queryValidation.data as TQuery;
      
      // Sanitize query parameters if configured
      if (config.sanitizeInput) {
        validatedQuery = InputSanitizer.sanitizeObject(validatedQuery) as TQuery;
      }
    }

    // 6. Create enhanced request object
    const enhancedRequest = request as EnhancedRequest<TBody, TQuery, TParams>;
    if (user) {
      enhancedRequest.user = user as {
        id: string;
        email: string;
        name?: string;
        role: string;
      };
    }
    if (validatedBody) {
      enhancedRequest.validatedBody = validatedBody;
    }
    if (validatedQuery) {
      enhancedRequest.validatedQuery = validatedQuery;
    }

    return {
      success: true,
      request: enhancedRequest,
      rateLimitHeaders,
    };

  } catch (error) {
    console.error('Middleware error:', error);
    return {
      success: false,
      response: createErrorResponse(error as Error, path),
    };
  }
}


// Higher-order function to wrap API handlers with middleware
export function withApiMiddleware<TBody = unknown, TQuery = unknown, TParams = unknown>(
  config: ApiMiddlewareConfig = {}
) {
  return function <T extends unknown[], R>(
    handler: (request: EnhancedRequest<TBody, TQuery, TParams>, ...args: T) => Promise<R>
  ) {
    return withErrorHandler(async (request: NextRequest, ...args: T): Promise<R | NextResponse> => {
      const middlewareResult = await applyApiMiddleware<TBody, TQuery>(request, config);

      if (!middlewareResult.success) {
        return middlewareResult.response!;
      }

      try {
        // Create enhanced request with route params if available
        const enhancedRequest = middlewareResult.request as EnhancedRequest<TBody, TQuery, TParams>;

        // If this is a dynamic route, the params will be in the second argument
        if (args.length > 0 && typeof args[0] === 'object' && args[0] !== null && 'params' in args[0]) {
          enhancedRequest.params = (args[0] as { params: TParams }).params;
        }

        const result = await handler(enhancedRequest, ...args);
        
        // Record successful request for rate limiting if configured
        if (config.rateLimiter && config.skipRateLimitOnSuccess) {
          await config.rateLimiter.recordResult(request, true);
        }

        // Add rate limit headers to successful responses
        if (middlewareResult.rateLimitHeaders && result instanceof NextResponse) {
          Object.entries(middlewareResult.rateLimitHeaders).forEach(([key, value]) => {
            result.headers.set(key, value);
          });
        }

        // Add security headers if configured
        if (config.securityHeaders && result instanceof NextResponse) {
          const securityHeaders = SecurityHeaders.getSecurityHeaders();
          Object.entries(securityHeaders).forEach(([key, value]) => {
            result.headers.set(key, value);
          });
          result.headers.set('Content-Security-Policy', SecurityHeaders.getCSPHeader());
        }

        return result;
      } catch (error) {
        // Record failed request for rate limiting if configured
        if (config.rateLimiter) {
          await config.rateLimiter.recordResult(request, false);
        }
        throw error;
      }
    });
  };
}

// Pre-configured middleware for common use cases
export const middlewarePresets = {
  // Public endpoints with basic rate limiting and security headers
  public: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    sanitizeInput: true,
    securityHeaders: true,
  }),

  // Authenticated endpoints with input sanitization
  authenticated: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    requireAuth: true,
    sanitizeInput: true,
    securityHeaders: true,
  }),

  // Admin-only endpoints with CSRF protection
  adminOnly: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    requireAuth: true,
    requireRoles: ['ADMIN'],
    requireCSRF: true,
    sanitizeInput: true,
    securityHeaders: true,
  }),

  // NWA team endpoints with CSRF protection
  nwaTeam: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    requireAuth: true,
    requireRoles: ['ADMIN', 'NWA_TEAM'],
    requireCSRF: true,
    sanitizeInput: true,
    securityHeaders: true,
  }),

  // Read-only endpoints with relaxed rate limiting
  readOnly: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    requireAuth: true,
    sanitizeInput: true,
    securityHeaders: true,
  }),

  // Write endpoints with strict security
  writeOnly: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    requireAuth: true,
    requireCSRF: true,
    sanitizeInput: true,
    securityHeaders: true,
    skipRateLimitOnSuccess: false,
  }),

  // Secure endpoints for sensitive operations
  secure: (rateLimiter?: RateLimiter) => ({
    rateLimiter,
    requireAuth: true,
    requireCSRF: true,
    sanitizeInput: true,
    securityHeaders: true,
    skipRateLimitOnSuccess: false,
  }),
};