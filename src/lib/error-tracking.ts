/**
 * Error Tracking and Reporting System
 * 
 * This module provides comprehensive error tracking, logging, and reporting
 * capabilities for the application.
 */

import { ApplicationMonitor } from './monitoring';
import { AuditLogger } from './audit-logger';

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  url?: string;
  userAgent?: string;
  ip?: string;
  timestamp?: Date;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  tags?: Record<string, string>;
  extra?: Record<string, unknown>;
}

export interface ErrorReport {
  id: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: ErrorContext;
  fingerprint: string;
  count: number;
  firstSeen: Date;
  lastSeen: Date;
}

export class ErrorTracker {
  private static errors: Map<string, ErrorReport> = new Map();
  private static errorQueue: ErrorReport[] = [];

  /**
   * Track an error with context
   */
  static trackError(error: Error, context: ErrorContext = {}): string {
    const errorId = this.generateErrorId();
    const fingerprint = this.generateFingerprint(error, context);
    const timestamp = new Date();

    // Check if we've seen this error before
    const existingError = this.errors.get(fingerprint);
    
    if (existingError) {
      existingError.count++;
      existingError.lastSeen = timestamp;
      existingError.context = { ...existingError.context, ...context };
    } else {
      const errorReport: ErrorReport = {
        id: errorId,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        context: {
          ...context,
          timestamp,
          severity: context.severity || this.determineSeverity(error)
        },
        fingerprint,
        count: 1,
        firstSeen: timestamp,
        lastSeen: timestamp
      };

      this.errors.set(fingerprint, errorReport);
      this.errorQueue.push(errorReport);
    }

    // Record metrics
    ApplicationMonitor.recordError(error, context);

    // Log for audit trail
    AuditLogger.log({
      action: 'ERROR_OCCURRED',
      resource: 'application',
      resourceId: errorId,
      details: {
        errorName: error.name,
        errorMessage: error.message,
        severity: context.severity || this.determineSeverity(error),
        ...context
      },
      userId: context.userId,
      ipAddress: context.ip,
      userAgent: context.userAgent
    });

    // Send to external services if configured
    this.sendToExternalServices(error, context);

    return errorId;
  }

  /**
   * Track API errors specifically
   */
  static trackApiError(
    error: Error,
    request: {
      method: string;
      url: string;
      headers?: Record<string, string>;
      body?: unknown;
    },
    response?: {
      status: number;
      headers?: Record<string, string>;
    },
    context: ErrorContext = {}
  ): string {
    return this.trackError(error, {
      ...context,
      tags: {
        ...context.tags,
        errorType: 'api_error',
        method: request.method,
        endpoint: request.url,
        statusCode: response?.status?.toString() || 'unknown'
      },
      extra: {
        request: {
          method: request.method,
          url: request.url,
          headers: this.sanitizeHeaders(request.headers),
          body: this.sanitizeBody(request.body)
        },
        response: response ? {
          status: response.status,
          headers: this.sanitizeHeaders(response.headers)
        } : undefined
      }
    });
  }

  /**
   * Track database errors
   */
  static trackDatabaseError(
    error: Error,
    query?: string,
    context: ErrorContext = {}
  ): string {
    return this.trackError(error, {
      ...context,
      tags: {
        ...context.tags,
        errorType: 'database_error',
        queryType: query ? this.extractQueryType(query) : 'unknown'
      },
      extra: {
        query: query ? this.sanitizeQuery(query) : undefined
      }
    });
  }

  /**
   * Track authentication errors
   */
  static trackAuthError(
    error: Error,
    authContext: {
      action: string;
      provider?: string;
      userId?: string;
    },
    context: ErrorContext = {}
  ): string {
    return this.trackError(error, {
      ...context,
      userId: authContext.userId,
      tags: {
        ...context.tags,
        errorType: 'auth_error',
        authAction: authContext.action,
        authProvider: authContext.provider || 'unknown'
      },
      severity: 'high' // Auth errors are always high severity
    });
  }

  /**
   * Get error reports with filtering
   */
  static getErrorReports(filter: {
    severity?: string;
    since?: Date;
    limit?: number;
    tags?: Record<string, string>;
  } = {}): ErrorReport[] {
    let reports = Array.from(this.errors.values());

    // Apply filters
    if (filter.severity) {
      reports = reports.filter(r => r.context.severity === filter.severity);
    }

    if (filter.since) {
      reports = reports.filter(r => r.lastSeen >= filter.since!);
    }

    if (filter.tags) {
      reports = reports.filter(r => {
        return Object.entries(filter.tags!).every(([key, value]) => 
          r.context.tags?.[key] === value
        );
      });
    }

    // Sort by last seen (most recent first)
    reports.sort((a, b) => b.lastSeen.getTime() - a.lastSeen.getTime());

    // Apply limit
    if (filter.limit) {
      reports = reports.slice(0, filter.limit);
    }

    return reports;
  }

  /**
   * Get error statistics
   */
  static getErrorStats(timeframe: number = 24 * 60 * 60 * 1000): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    topErrors: ErrorReport[];
  } {
    const since = new Date(Date.now() - timeframe);
    const recentErrors = this.getErrorReports({ since });

    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    recentErrors.forEach(error => {
      const type = error.context.tags?.errorType || 'unknown';
      const severity = error.context.severity || 'medium';

      byType[type] = (byType[type] || 0) + error.count;
      bySeverity[severity] = (bySeverity[severity] || 0) + error.count;
    });

    const topErrors = recentErrors
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      total: recentErrors.reduce((sum, error) => sum + error.count, 0),
      byType,
      bySeverity,
      topErrors
    };
  }

  /**
   * Clear old errors
   */
  static cleanup(olderThan: Date = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)): number {
    let removed = 0;
    
    for (const [fingerprint, error] of this.errors.entries()) {
      if (error.lastSeen < olderThan) {
        this.errors.delete(fingerprint);
        removed++;
      }
    }

    return removed;
  }

  private static generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static generateFingerprint(error: Error, context: ErrorContext): string {
    const components = [
      error.name,
      error.message,
      context.url || '',
      context.tags?.errorType || ''
    ];
    
    return btoa(components.join('|')).replace(/[^a-zA-Z0-9]/g, '').substr(0, 32);
  }

  private static determineSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const errorName = error.name.toLowerCase();
    const errorMessage = error.message.toLowerCase();

    // Critical errors
    if (errorName.includes('syntax') || 
        errorName.includes('reference') ||
        errorMessage.includes('database') ||
        errorMessage.includes('connection')) {
      return 'critical';
    }

    // High severity errors
    if (errorName.includes('auth') ||
        errorName.includes('permission') ||
        errorMessage.includes('unauthorized') ||
        errorMessage.includes('forbidden')) {
      return 'high';
    }

    // Low severity errors
    if (errorName.includes('validation') ||
        errorMessage.includes('not found') ||
        errorMessage.includes('invalid input')) {
      return 'low';
    }

    return 'medium';
  }

  private static sanitizeHeaders(headers?: Record<string, string>): Record<string, string> {
    if (!headers) return {};
    
    const sanitized = { ...headers };
    
    // Remove sensitive headers
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    
    return sanitized;
  }

  private static sanitizeBody(body: unknown): unknown {
    if (!body) return undefined;
    
    if (typeof body === 'string') {
      try {
        body = JSON.parse(body);
      } catch {
        return '[Non-JSON body]';
      }
    }

    if (typeof body === 'object' && body !== null) {
      const sanitized = { ...body as Record<string, unknown> };
      
      // Remove sensitive fields
      delete sanitized.password;
      delete sanitized.token;
      delete sanitized.secret;
      delete sanitized.apiKey;
      
      return sanitized;
    }

    return body;
  }

  private static sanitizeQuery(query: string): string {
    // Remove potential sensitive data from SQL queries
    return query
      .replace(/('.*?')/g, "'[REDACTED]'")
      .replace(/(".*?")/g, '"[REDACTED]"')
      .substring(0, 500); // Limit length
  }

  private static extractQueryType(query: string): string {
    const match = query.trim().match(/^(\w+)/i);
    return match ? match[1].toUpperCase() : 'UNKNOWN';
  }

  private static async sendToExternalServices(error: Error, context: ErrorContext): Promise<void> {
    // Integration points for external error tracking services
    // e.g., Sentry, Bugsnag, Rollbar, etc.
    
    try {
      // Example: Send to Sentry
      if (process.env.SENTRY_DSN) {
        // await Sentry.captureException(error, { contexts: { custom: context } });
      }

      // Example: Send to custom webhook
      if (process.env.ERROR_WEBHOOK_URL && context.severity === 'critical') {
        await fetch(process.env.ERROR_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack
            },
            context,
            timestamp: new Date().toISOString()
          })
        });
      }
    } catch (_integrationError) {
      console.error('Failed to send error to external service');
    }
  }
}

/**
 * Express/Next.js error handling middleware
 */
export interface ErrorTrackingRequest {
  url?: string;
  headers?: {
    'user-agent'?: string;
    [key: string]: string | undefined;
  };
  ip?: string;
  connection?: {
    remoteAddress?: string;
  };
  user?: {
    id?: string;
  };
  sessionID?: string;
}

export function errorTrackingMiddleware(
  error: Error,
  request: ErrorTrackingRequest,
  context: ErrorContext = {}
): string {
  return ErrorTracker.trackError(error, {
    ...context,
    url: request.url,
    userAgent: request.headers?.['user-agent'],
    ip: request.ip || request.connection?.remoteAddress,
    userId: request.user?.id,
    sessionId: request.sessionID
  });
}

/**
 * React error boundary integration
 */
export function trackReactError(
  error: Error,
  errorInfo: { componentStack: string },
  context: ErrorContext = {}
): string {
  return ErrorTracker.trackError(error, {
    ...context,
    tags: {
      ...context.tags,
      errorType: 'react_error'
    },
    extra: {
      componentStack: errorInfo.componentStack
    },
    severity: 'high'
  });
}