import { NotificationService } from '@/lib/notification-service';
import { prisma } from '@/lib/prisma';

/**
 * Send a notification when a video is promoted
 */
export async function sendVideoPromotedNotification(
  userId: string,
  videoId: string,
  videoTitle: string
) {
  try {
    // Create in-app notification
    await NotificationService.createNotification({
      userId,
      title: 'Video Promoted',
      message: `Your video "${videoTitle}" has been successfully promoted!`,
      type: 'video-promoted',
      sentBy: userId, // The user triggers their own notification
      videoId: videoId,
    });

    // Check if user wants email notifications for this type
    const emailEnabled = await NotificationService.isEmailNotificationEnabled(
      userId,
      'video-promoted'
    );

    if (emailEnabled) {
      // Get user's email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true }
      });

      if (user?.email) {
        // Queue email notification
        await NotificationService.queueEmailNotification(
          userId,
          user.email,
          'Your Video Has Been Promoted!',
          `<p>Great news! Your video "<strong>${videoTitle}</strong>" has been successfully promoted.</p><p>You can view your promotion analytics in your dashboard.</p>`,
          `Great news! Your video "${videoTitle}" has been successfully promoted.\n\nYou can view your promotion analytics in your dashboard.`
        );
      }
    }
  } catch (error) {
    console.error('Error sending video promoted notification:', error);
  }
}

/**
 * Send a notification when a user earns a badge
 */
export async function sendBadgeEarnedNotification(userId: string, badgeName: string) {
  try {
    // Create in-app notification
    await NotificationService.createNotification({
      userId,
      title: 'New Badge Earned!',
      message: `Congratulations! You've earned the "${badgeName}" badge.`,
      type: 'badge-earned',
      sentBy: userId, // User's action triggers the notification
    });

    // Check if user wants email notifications for this type
    const emailEnabled = await NotificationService.isEmailNotificationEnabled(
      userId,
      'badge-earned'
    );

    if (emailEnabled) {
      // Get user's email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true }
      });

      if (user?.email) {
        // Queue email notification
        await NotificationService.queueEmailNotification(
          userId,
          user.email,
          'You\'ve Earned a New Badge!',
          `<p>Congratulations! You've earned the "<strong>${badgeName}</strong>" badge.</p><p>Check your profile to see all your earned badges.</p>`,
          `Congratulations! You've earned the "${badgeName}" badge.\n\nCheck your profile to see all your earned badges.`
        );
      }
    }
  } catch (error) {
    console.error('Error sending badge earned notification:', error);
  }
}

/**
 * Send a notification when a user levels up
 */
export async function sendLevelUpNotification(userId: string, newLevel: number) {
  try {
    // Create in-app notification
    await NotificationService.createNotification({
      userId,
      title: 'Level Up!',
      message: `You've reached level ${newLevel}! Keep up the great work!`,
      type: 'level-up',
      sentBy: userId, // User's action triggers the notification
    });

    // Check if user wants email notifications for this type
    const emailEnabled = await NotificationService.isEmailNotificationEnabled(
      userId,
      'level-up'
    );

    if (emailEnabled) {
      // Get user's email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true }
      });

      if (user?.email) {
        // Queue email notification
        await NotificationService.queueEmailNotification(
          userId,
          user.email,
          'You\'ve Leveled Up!',
          `<p>Congratulations! You've reached level <strong>${newLevel}</strong>!</p><p>Keep up the great work and continue earning points!</p>`,
          `Congratulations! You've reached level ${newLevel}!\n\nKeep up the great work and continue earning points!`
        );
      }
    }
  } catch (error) {
    console.error('Error sending level up notification:', error);
  }
}