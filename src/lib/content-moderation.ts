import { prisma } from './prisma';

// Basic content filtering patterns
const DEFAULT_FILTERS = [
  // Inappropriate content keywords
  { type: 'keyword', pattern: 'spam', severity: 'medium', action: 'flag' },
  { type: 'keyword', pattern: 'scam', severity: 'high', action: 'block' },
  { type: 'keyword', pattern: 'fake', severity: 'medium', action: 'flag' },
  { type: 'keyword', pattern: 'clickbait', severity: 'low', action: 'flag' },
  
  // URL patterns that might be suspicious
  { type: 'url_pattern', pattern: 'bit\\.ly', severity: 'low', action: 'flag' },
  { type: 'url_pattern', pattern: 'tinyurl', severity: 'low', action: 'flag' },
  
  // Regex patterns for common spam indicators
  { type: 'regex', pattern: '\\$\\d+.*per.*hour', severity: 'high', action: 'block' },
  { type: 'regex', pattern: 'make.*money.*fast', severity: 'high', action: 'block' },
  { type: 'regex', pattern: 'click.*here.*now', severity: 'medium', action: 'flag' },
];

export interface ContentFilterResult {
  isBlocked: boolean;
  isFlagged: boolean;
  matchedFilters: Array<{
    type: string;
    pattern: string;
    severity: string;
    action: string;
  }>;
  score: number; // 0-100, higher means more suspicious
}

export async function initializeContentFilters() {
  try {
    // Check if filters already exist
    const existingFilters = await prisma.contentFilter.count();
    
    if (existingFilters === 0) {
      // Create default filters
      await prisma.contentFilter.createMany({
        data: DEFAULT_FILTERS.map(filter => ({
          ...filter,
          description: `Default ${filter.type} filter for ${filter.pattern}`,
        })),
      });
      console.log('Content filters initialized with default patterns');
    }
  } catch (error) {
    console.error('Error initializing content filters:', error);
  }
}

export async function moderateContent(
  title: string,
  description: string | null,
  urls: string[]
): Promise<ContentFilterResult> {
  try {
    // Get active filters from database
    const filters = await prisma.contentFilter.findMany({
      where: { isActive: true },
    });

    const matchedFilters: ContentFilterResult['matchedFilters'] = [];
    let score = 0;

    // Combine all text content for analysis
    const textContent = [title, description, ...urls].filter(Boolean).join(' ').toLowerCase();

    for (const filter of filters) {
      let isMatch = false;

      switch (filter.type) {
        case 'keyword':
          isMatch = textContent.includes(filter.pattern.toLowerCase());
          break;
          
        case 'regex':
          try {
            const regex = new RegExp(filter.pattern, 'i');
            isMatch = regex.test(textContent);
          } catch (_e) {
            console.warn(`Invalid regex pattern: ${filter.pattern}`);
          }
          break;
          
        case 'url_pattern':
          try {
            const regex = new RegExp(filter.pattern, 'i');
            isMatch = urls.some(url => regex.test(url));
          } catch (_e) {
            console.warn(`Invalid URL pattern: ${filter.pattern}`);
          }
          break;
      }

      if (isMatch) {
        matchedFilters.push({
          type: filter.type,
          pattern: filter.pattern,
          severity: filter.severity,
          action: filter.action,
        });

        // Add to score based on severity
        switch (filter.severity) {
          case 'low':
            score += 10;
            break;
          case 'medium':
            score += 25;
            break;
          case 'high':
            score += 50;
            break;
        }
      }
    }

    // Determine if content should be blocked or flagged
    const isBlocked = matchedFilters.some(filter => filter.action === 'block' || filter.action === 'auto_reject');
    const isFlagged = matchedFilters.some(filter => filter.action === 'flag') || score >= 30;

    return {
      isBlocked,
      isFlagged,
      matchedFilters,
      score: Math.min(score, 100), // Cap at 100
    };
  } catch (error) {
    console.error('Error in content moderation:', error);
    // Fail safe - don't block content if moderation fails
    return {
      isBlocked: false,
      isFlagged: false,
      matchedFilters: [],
      score: 0,
    };
  }
}

export async function reportContent(
  reporterId: string,
  videoId: string | null,
  videoLinkId: string | null,
  reason: string,
  description?: string
) {
  try {
    const report = await prisma.contentReport.create({
      data: {
        reporterId,
        videoId,
        videoLinkId,
        reason,
        description,
        status: 'pending',
      },
      include: {
        reporter: {
          select: { id: true, name: true, email: true },
        },
        video: {
          select: { id: true, title: true, description: true },
        },
        videoLink: {
          select: { id: true, linkUrl: true, platform: true },
        },
      },
    });

    return report;
  } catch (error) {
    console.error('Error creating content report:', error);
    throw new Error('Failed to submit report');
  }
}

export async function getModerationQueue(page = 1, limit = 20) {
  try {
    const offset = (page - 1) * limit;

    const [reports, total] = await Promise.all([
      prisma.contentReport.findMany({
        where: {
          status: { in: ['pending', 'reviewed'] },
        },
        include: {
          reporter: {
            select: { id: true, name: true, email: true },
          },
          reviewer: {
            select: { id: true, name: true },
          },
          video: {
            select: { id: true, title: true, description: true, userId: true },
          },
          videoLink: {
            select: { id: true, linkUrl: true, platform: true, userId: true },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.contentReport.count({
        where: {
          status: { in: ['pending', 'reviewed'] },
        },
      }),
    ]);

    return {
      reports,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  } catch (error) {
    console.error('Error fetching moderation queue:', error);
    throw new Error('Failed to fetch moderation queue');
  }
}

export async function moderateReport(
  reportId: string,
  moderatorId: string,
  action: 'approve' | 'reject' | 'hide' | 'delete' | 'warn_user',
  reason?: string
) {
  try {
    const report = await prisma.contentReport.findUnique({
      where: { id: reportId },
      include: { video: true, videoLink: true },
    });

    if (!report) {
      throw new Error('Report not found');
    }

    // Create moderation action record
    await prisma.moderationAction.create({
      data: {
        moderatorId,
        videoId: report.videoId,
        videoLinkId: report.videoLinkId,
        reportId,
        action,
        reason,
      },
    });

    // Update report status
    const updatedReport = await prisma.contentReport.update({
      where: { id: reportId },
      data: {
        status: action === 'approve' ? 'dismissed' : 'resolved',
        reviewedBy: moderatorId,
        reviewedAt: new Date(),
        resolution: reason,
      },
    });

    // Take action on the content if necessary
    if (action === 'hide' || action === 'delete') {
      if (report.videoId) {
        await prisma.video.update({
          where: { id: report.videoId },
          data: {
            status: action === 'delete' ? 'deleted' : 'hidden',
          },
        });
      } else if (report.videoLinkId) {
        await prisma.videoLink.update({
          where: { id: report.videoLinkId },
          data: {
            status: action === 'delete' ? 'deleted' : 'hidden',
          },
        });
      }
    }

    return updatedReport;
  } catch (error) {
    console.error('Error moderating report:', error);
    throw new Error('Failed to moderate report');
  }
}

export async function getModerationStats() {
  try {
    const [
      pendingReports,
      totalReports,
      resolvedReports,
      flaggedContent,
    ] = await Promise.all([
      prisma.contentReport.count({
        where: { status: 'pending' },
      }),
      prisma.contentReport.count(),
      prisma.contentReport.count({
        where: { status: { in: ['resolved', 'dismissed'] } },
      }),
      prisma.contentReport.count({
        where: { status: 'reviewed' },
      }),
    ]);

    return {
      pendingReports,
      totalReports,
      resolvedReports,
      flaggedContent,
      resolutionRate: totalReports > 0 ? (resolvedReports / totalReports) * 100 : 0,
    };
  } catch (error) {
    console.error('Error fetching moderation stats:', error);
    return {
      pendingReports: 0,
      totalReports: 0,
      resolvedReports: 0,
      flaggedContent: 0,
      resolutionRate: 0,
    };
  }
}