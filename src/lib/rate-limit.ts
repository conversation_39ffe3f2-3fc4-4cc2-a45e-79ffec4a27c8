/**
 * Rate Limiting Implementation
 * Compatible with existing middleware system
 */

import { NextRequest } from 'next/server';

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

interface RateLimitResult {
  success: boolean;
  error?: {
    message: string;
    retryAfter?: number;
    code?: string;
  };
  headers: Record<string, string>;
}

class MemoryRateLimiter {
  private store = new Map<string, RateLimitEntry>();

  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}

  /**
   * Check if request should be rate limited
   */
  async checkLimit(identifier: string): Promise<RateLimitResult> {
    const now = Date.now();
    const entry = this.store.get(identifier);

    if (!entry || now > entry.resetTime) {
      // First request or window expired, allow and initialize
      this.store.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs
      });

      return {
        success: true,
        headers: {
          'X-RateLimit-Limit': this.maxRequests.toString(),
          'X-RateLimit-Remaining': (this.maxRequests - 1).toString(),
          'X-RateLimit-Reset': new Date(now + this.windowMs).toISOString(),
        }
      };
    }

    if (entry.count >= this.maxRequests) {
      // Rate limit exceeded
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);

      return {
        success: false,
        error: {
          message: 'Too many requests',
          retryAfter,
          code: 'RATE_LIMIT_EXCEEDED'
        },
        headers: {
          'X-RateLimit-Limit': this.maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': new Date(entry.resetTime).toISOString(),
          'Retry-After': retryAfter.toString(),
        }
      };
    }

    // Increment counter and allow
    entry.count++;
    const remaining = Math.max(0, this.maxRequests - entry.count);

    return {
      success: true,
      headers: {
        'X-RateLimit-Limit': this.maxRequests.toString(),
        'X-RateLimit-Remaining': remaining.toString(),
        'X-RateLimit-Reset': new Date(entry.resetTime).toISOString(),
      }
    };
  }

  /**
   * Record result for adaptive rate limiting
   */
  async recordResult(request: NextRequest, success: boolean): Promise<void> {
    // For now, just log the result
    // In a more advanced implementation, this could adjust rate limits based on success/failure patterns
    const identifier = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    if (!success) {
      // Could implement more aggressive rate limiting for failed requests
      console.log(`Rate limit: Failed request from ${identifier}`);
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }

  /**
   * Backward compatibility method for existing tests
   */
  isBlocked(identifier: string, maxRequests: number, windowMs: number): boolean {
    // Use jest's fake timers if available, otherwise use Date.now()
    const now = global.jest?.now?.() ?? Date.now();
    const entry = this.store.get(identifier);

    if (!entry || now > entry.resetTime) {
      // First request or window expired, allow and initialize
      this.store.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      });
      return false;
    }

    if (entry.count >= maxRequests) {
      // Rate limit exceeded
      return true;
    }

    // Increment counter and allow
    entry.count++;
    return false;
  }
}

// Create rate limiter instances with different configurations
export const rateLimiters = {
  // General purpose rate limiter
  general: new MemoryRateLimiter(100, 15 * 60 * 1000), // 100 requests per 15 minutes

  // Read operations (more permissive)
  read: new MemoryRateLimiter(200, 15 * 60 * 1000), // 200 requests per 15 minutes

  // Strict rate limiter for sensitive operations
  strict: new MemoryRateLimiter(10, 5 * 60 * 1000), // 10 requests per 5 minutes

  // Video creation (resource intensive)
  videoCreation: new MemoryRateLimiter(20, 10 * 60 * 1000), // 20 requests per 10 minutes

  // Notifications (high frequency)
  notifications: new MemoryRateLimiter(50, 10 * 60 * 1000), // 50 requests per 10 minutes
};

export class RateLimiter extends MemoryRateLimiter {
  constructor(maxRequests?: number, windowMs?: number) {
    super(maxRequests, windowMs);
  }
}

/**
 * Apply rate limiting to a request
 */
export async function applyRateLimit(
  request: NextRequest,
  rateLimiter: RateLimiter
): Promise<RateLimitResult> {
  // Extract identifier from request
  const identifier = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';

  return rateLimiter.checkLimit(identifier);
}

// Export singleton instance for backward compatibility
export const SecurityRateLimit = rateLimiters.general;

// Clean up expired entries every 5 minutes
if (typeof globalThis !== 'undefined') {
  setInterval(() => {
    Object.values(rateLimiters).forEach(limiter => limiter.cleanup());
  }, 5 * 60 * 1000);
}