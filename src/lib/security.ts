import validator from 'validator';
import crypto from 'crypto';

// Simple HTML sanitization without DOMPurify for better test compatibility
function createSimpleHtmlSanitizer() {
  const DANGEROUS_TAGS = ['script', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button', 'iframe', 'frame'];
  const DANGEROUS_ATTRS = ['onload', 'onerror', 'onclick', 'onmouseover', 'onfocus', 'onblur', 'onchange', 'onsubmit'];
  
  return {
    sanitize: (html: string): string => {
      if (!html || typeof html !== 'string') {
        return '';
      }

      let sanitized = html;

      // Remove dangerous tags
      DANGEROUS_TAGS.forEach(tag => {
        const regex = new RegExp(`<${tag}\\b[^<]*(?:(?!<\\/${tag}>)<[^<]*)*<\\/${tag}>`, 'gi');
        sanitized = sanitized.replace(regex, '');
        
        // Also remove self-closing versions
        const selfClosingRegex = new RegExp(`<${tag}\\b[^>]*\\/>`, 'gi');
        sanitized = sanitized.replace(selfClosingRegex, '');
      });

      // Remove dangerous attributes
      DANGEROUS_ATTRS.forEach(attr => {
        const regex = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gi');
        sanitized = sanitized.replace(regex, '');
      });

      // Remove javascript: and data: URLs
      sanitized = sanitized.replace(/javascript:/gi, '');
      sanitized = sanitized.replace(/data:/gi, '');

      return sanitized;
    }
  };
}

const htmlSanitizer = createSimpleHtmlSanitizer();

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  static sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    return htmlSanitizer.sanitize(input);
  }

  /**
   * Sanitize plain text input by removing/escaping dangerous characters
   */
  static sanitizeText(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    // Remove null bytes and control characters
    let sanitized = input.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    
    // Normalize whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim();
    
    // Limit length to prevent DoS
    if (sanitized.length > 10000) {
      sanitized = sanitized.substring(0, 10000);
    }
    
    return sanitized;
  }

  /**
   * Sanitize URL input
   */
  static sanitizeUrl(input: string): string {
    if (!input || typeof input !== 'string') {
      throw new Error('Invalid URL format');
    }

    const sanitized = this.sanitizeText(input);
    
    // Check if sanitized input is empty
    if (!sanitized) {
      throw new Error('Invalid URL format');
    }
    
    // Validate URL format
    if (!validator.isURL(sanitized, {
      protocols: ['http', 'https'],
      require_protocol: true,
      require_valid_protocol: true,
      allow_underscores: false,
      allow_trailing_dot: false,
      allow_protocol_relative_urls: false,
    })) {
      throw new Error('Invalid URL format');
    }

    return sanitized;
  }

  /**
   * Sanitize email input
   */
  static sanitizeEmail(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    const sanitized = this.sanitizeText(input).toLowerCase();
    
    if (!validator.isEmail(sanitized)) {
      throw new Error('Invalid email format');
    }

    return sanitized;
  }

  /**
   * Sanitize numeric input
   */
  static sanitizeNumber(input: unknown, options: { min?: number; max?: number; integer?: boolean } = {}): number {
    const num = Number(input);
    
    if (isNaN(num) || !isFinite(num)) {
      throw new Error('Invalid number format');
    }

    if (options.integer && !Number.isInteger(num)) {
      throw new Error('Number must be an integer');
    }

    if (options.min !== undefined && num < options.min) {
      throw new Error(`Number must be at least ${options.min}`);
    }

    if (options.max !== undefined && num > options.max) {
      throw new Error(`Number must be at most ${options.max}`);
    }

    return num;
  }

  /**
   * Sanitize object by recursively sanitizing all string values
   */
  static sanitizeObject(obj: unknown, depth = 0): unknown {
    if (depth > 10) {
      throw new Error('Object nesting too deep');
    }

    if (obj === null || obj === undefined) {
      return obj;
    }

    if (typeof obj === 'string') {
      return this.sanitizeText(obj);
    }

    if (typeof obj === 'number' || typeof obj === 'boolean') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item, depth + 1));
    }

    if (typeof obj === 'object') {
      const sanitized: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        const sanitizedKey = this.sanitizeText(key);
        sanitized[sanitizedKey] = this.sanitizeObject(value, depth + 1);
      }
      return sanitized;
    }

    return obj;
  }

  /**
   * Sanitize platform-specific URLs
   */
  static sanitizePlatformUrls(platforms: unknown): unknown {
    if (!platforms || typeof platforms !== 'object') {
      return {};
    }

    const platformsObj = platforms as { youtubeUrl?: string; tiktokUrl?: string; rumbleUrl?: string };
    const sanitized: { youtubeUrl?: string; tiktokUrl?: string; rumbleUrl?: string } = {};

    if (platformsObj.youtubeUrl) {
      sanitized.youtubeUrl = this.sanitizeUrl(platformsObj.youtubeUrl);
      if (!this.isValidYouTubeUrl(sanitized.youtubeUrl)) {
        throw new Error('Invalid YouTube URL format');
      }
    }

    if (platformsObj.tiktokUrl) {
      sanitized.tiktokUrl = this.sanitizeUrl(platformsObj.tiktokUrl);
      if (!this.isValidTikTokUrl(sanitized.tiktokUrl)) {
        throw new Error('Invalid TikTok URL format');
      }
    }

    if (platformsObj.rumbleUrl) {
      sanitized.rumbleUrl = this.sanitizeUrl(platformsObj.rumbleUrl);
      if (!this.isValidRumbleUrl(sanitized.rumbleUrl)) {
        throw new Error('Invalid Rumble URL format');
      }
    }

    return sanitized;
  }

  /**
   * Platform URL validators
   */
  private static isValidYouTubeUrl(url: string): boolean {
    const youtubeRegex = /^https:\/\/(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/)|youtu\.be\/)[\w-]+/;
    return youtubeRegex.test(url);
  }

  private static isValidTikTokUrl(url: string): boolean {
    const tiktokRegex = /^https:\/\/(www\.)?tiktok\.com\/@[\w.-]+\/video\/\d+/;
    return tiktokRegex.test(url);
  }

  private static isValidRumbleUrl(url: string): boolean {
    const rumbleRegex = /^https:\/\/(www\.)?rumble\.com\/(v|embed)\/[\w-]+/;
    return rumbleRegex.test(url);
  }
}

/**
 * CSRF Protection utilities
 */
export class CSRFProtection {
  private static readonly SECRET_KEY = process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production';
  private static readonly TOKEN_EXPIRY = 60 * 60 * 1000; // 1 hour

  /**
   * Generate a CSRF token
   */
  static generateToken(sessionId: string): string {
    const timestamp = Date.now();
    const data = `${sessionId}:${timestamp}`;
    const signature = crypto
      .createHmac('sha256', this.SECRET_KEY)
      .update(data)
      .digest('hex');
    
    return Buffer.from(`${data}:${signature}`).toString('base64');
  }

  /**
   * Verify a CSRF token
   */
  static verifyToken(token: string, sessionId: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString('utf8');
      const [receivedSessionId, timestampStr, signature] = decoded.split(':');

      if (receivedSessionId !== sessionId) {
        return false;
      }

      const timestamp = parseInt(timestampStr);
      if (isNaN(timestamp) || Date.now() - timestamp > this.TOKEN_EXPIRY) {
        return false;
      }

      const data = `${receivedSessionId}:${timestampStr}`;
      const expectedSignature = crypto
        .createHmac('sha256', this.SECRET_KEY)
        .update(data)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (_error) {
      return false;
    }
  }

  /**
   * Extract CSRF token from request headers
   */
  static extractTokenFromRequest(request: Request): string | null {
    // Check X-CSRF-Token header first
    const headerToken = request.headers.get('X-CSRF-Token');
    if (headerToken) {
      return headerToken;
    }

    // Check Authorization header for CSRF token
    const authHeader = request.headers.get('Authorization');
    if (authHeader && authHeader.startsWith('CSRF ')) {
      return authHeader.substring(5);
    }

    return null;
  }
}

/**
 * SQL Injection Prevention utilities
 */
export class SQLInjectionPrevention {
  /**
   * Validate and sanitize database query parameters
   */
  static sanitizeQueryParam(param: unknown, type: 'string' | 'number' | 'boolean' | 'uuid'): unknown {
    if (param === null || param === undefined) {
      return param;
    }

    switch (type) {
      case 'string':
        if (typeof param !== 'string') {
          throw new Error('Parameter must be a string');
        }
        return InputSanitizer.sanitizeText(param);

      case 'number':
        return InputSanitizer.sanitizeNumber(param);

      case 'boolean':
        if (typeof param === 'boolean') {
          return param;
        }
        if (param === 'true') return true;
        if (param === 'false') return false;
        throw new Error('Parameter must be a boolean');

      case 'uuid':
        if (typeof param !== 'string') {
          throw new Error('UUID must be a string');
        }
        if (!validator.isUUID(param)) {
          throw new Error('Invalid UUID format');
        }
        return param;

      default:
        throw new Error(`Unsupported parameter type: ${type}`);
    }
  }

  /**
   * Validate Prisma where clause to prevent injection
   */
  static validateWhereClause(whereClause: unknown): unknown {
    if (!whereClause || typeof whereClause !== 'object') {
      return whereClause;
    }

    // Recursively validate all values in the where clause
    const validated: Record<string, unknown> = {};
    
    for (const [key, value] of Object.entries(whereClause)) {
      // Validate field names to prevent injection
      if (!this.isValidFieldName(key)) {
        throw new Error(`Invalid field name: ${key}`);
      }

      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          validated[key] = value.map(item => 
            typeof item === 'string' ? InputSanitizer.sanitizeText(item) : item
          );
        } else {
          validated[key] = this.validateWhereClause(value);
        }
      } else if (typeof value === 'string') {
        validated[key] = InputSanitizer.sanitizeText(value);
      } else {
        validated[key] = value;
      }
    }

    return validated;
  }

  /**
   * Validate field names to prevent injection
   */
  private static isValidFieldName(fieldName: string): boolean {
    // Allow only alphanumeric characters, underscores, and dots for nested fields
    const validFieldRegex = /^[a-zA-Z_][a-zA-Z0-9_.]*$/;
    return validFieldRegex.test(fieldName) && fieldName.length <= 100;
  }

  /**
   * Sanitize search query to prevent injection
   */
  static sanitizeSearchQuery(query: string): string {
    if (!query || typeof query !== 'string') {
      return '';
    }

    // Remove potentially dangerous characters
    let sanitized = query.replace(/['";\\-]/g, '');
    
    // Sanitize as text
    sanitized = InputSanitizer.sanitizeText(sanitized);
    
    // Limit length
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100);
    }

    return sanitized;
  }
}

/**
 * Content Security utilities
 */
export class ContentSecurity {
  /**
   * Validate file upload content
   */
  static validateFileContent(content: Buffer, allowedTypes: string[]): boolean {
    // Check file signatures (magic numbers)
    const signatures: { [key: string]: number[] } = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47],
      'image/gif': [0x47, 0x49, 0x46],
      'image/webp': [0x52, 0x49, 0x46, 0x46],
    };

    for (const [mimeType, signature] of Object.entries(signatures)) {
      if (allowedTypes.includes(mimeType)) {
        const matches = signature.every((byte, index) => content[index] === byte);
        if (matches) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Scan content for malicious patterns
   */
  static scanForMaliciousContent(content: string): string[] {
    const maliciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /onload\s*=/gi,
      /onerror\s*=/gi,
      /onclick\s*=/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi,
      /<iframe\b/gi,
      /<object\b/gi,
      /<embed\b/gi,
    ];

    const threats: string[] = [];
    
    for (const pattern of maliciousPatterns) {
      if (pattern.test(content)) {
        threats.push(`Potential XSS: ${pattern.source}`);
      }
    }

    return threats;
  }
}

/**
 * Rate limiting helpers for security
 */
export class SecurityRateLimit {
  /**
   * Generate rate limit key for security-sensitive operations
   */
  static generateSecurityKey(ip: string, operation: string, userId?: string): string {
    const baseKey = `security:${operation}:${ip}`;
    return userId ? `${baseKey}:${userId}` : baseKey;
  }

  /**
   * Check if operation should be blocked due to security concerns
   */
  static shouldBlockOperation(failureCount: number, operation: string): boolean {
    const thresholds: { [key: string]: number } = {
      'login': 5,
      'password_reset': 3,
      'admin_action': 10,
      'video_creation': 20,
      'notification_send': 5,
    };

    const threshold = thresholds[operation] || 10;
    return failureCount >= threshold;
  }
}

/**
 * Security headers utilities
 */
export class SecurityHeaders {
  /**
   * Get security headers for API responses
   */
  static getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    };
  }

  /**
   * Get Content Security Policy header
   */
  static getCSPHeader(): string {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self'",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ].join('; ');
  }
}