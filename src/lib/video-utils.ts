// Shared utilities for video management across APIs

export interface PlatformUrls {
  youtubeUrl?: string;
  tiktokUrl?: string;
  rumbleUrl?: string;
}

export interface VideoWithPlatforms {
  id: string;
  title: string;
  description?: string | null;
  youtubeUrl?: string | null;
  tiktokUrl?: string | null;
  rumbleUrl?: string | null;
  thumbnailUrl?: string | null;
  status: string;
  isFeatured: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  url: string; // Legacy field
  platform: string; // Legacy field
  duration?: number | null;
  views: number;
  notificationSentAt?: Date | null;
}

export interface TransformedVideo extends VideoWithPlatforms {
  platforms: {
    youtube?: string | null;
    tiktok?: string | null;
    rumble?: string | null;
  };
  availablePlatforms: string[];
}

// Platform URL validation functions
export function validatePlatformUrls(platforms: PlatformUrls): string[] {
  const errors: string[] = [];
  
  if (platforms.youtubeUrl && !isValidYouTubeUrl(platforms.youtubeUrl)) {
    errors.push('Invalid YouTube URL format');
  }
  if (platforms.tiktokUrl && !isValidTikTokUrl(platforms.tiktokUrl)) {
    errors.push('Invalid TikTok URL format');
  }
  if (platforms.rumbleUrl && !isValidRumbleUrl(platforms.rumbleUrl)) {
    errors.push('Invalid Rumble URL format');
  }
  
  return errors;
}

export function isValidYouTubeUrl(url: string): boolean {
  // Require a valid 11-character video ID (alphanumeric, underscore, hyphen)
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/)|youtu\.be\/)([A-Za-z0-9_-]{11})(?:[&?].*)?$/;
  return youtubeRegex.test(url);
}

export function isValidTikTokUrl(url: string): boolean {
  const tiktokRegex = /^(https?:\/\/)?(www\.)?tiktok\.com\/@[\w.-]+\/video\/\d+/;
  return tiktokRegex.test(url);
}

export function isValidRumbleUrl(url: string): boolean {
  const rumbleRegex = /^(https?:\/\/)?(www\.)?rumble\.com\/[\w-]+/;
  return rumbleRegex.test(url);
}

export function validatePlatformUrl(url: string, platform: string): boolean {
  switch (platform.toLowerCase()) {
    case 'youtube':
      return isValidYouTubeUrl(url);
    case 'tiktok':
      return isValidTikTokUrl(url);
    case 'rumble':
      return isValidRumbleUrl(url);
    default:
      return false;
  }
}

// Transform video data to include platforms object and available platforms
export function transformVideoWithPlatforms(video: VideoWithPlatforms): TransformedVideo {
  return {
    ...video,
    platforms: {
      youtube: video.youtubeUrl,
      tiktok: video.tiktokUrl,
      rumble: video.rumbleUrl,
    },
    availablePlatforms: [
      ...(video.youtubeUrl ? ['youtube'] : []),
      ...(video.tiktokUrl ? ['tiktok'] : []),
      ...(video.rumbleUrl ? ['rumble'] : []),
    ],
  };
}

// Determine primary platform and URL for backward compatibility
export function getPrimaryPlatformInfo(platforms: PlatformUrls): { platform: string; url: string } {
  if (platforms.youtubeUrl) {
    return { platform: 'youtube', url: platforms.youtubeUrl };
  } else if (platforms.tiktokUrl) {
    return { platform: 'tiktok', url: platforms.tiktokUrl };
  } else if (platforms.rumbleUrl) {
    return { platform: 'rumble', url: platforms.rumbleUrl };
  }
  return { platform: '', url: '' };
}

// Valid platforms list
export const VALID_PLATFORMS = ['youtube', 'tiktok', 'rumble'] as const;
export type ValidPlatform = typeof VALID_PLATFORMS[number];

// Check if at least one platform URL is provided
export function hasValidPlatformUrl(platforms: PlatformUrls): boolean {
  return !!(platforms.youtubeUrl || platforms.tiktokUrl || platforms.rumbleUrl);
}