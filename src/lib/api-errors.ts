import { NextResponse } from 'next/server';

// Standard error codes
export enum ErrorCode {
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  
  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  
  // Resources
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
}

// Error response interface
export interface ApiErrorResponse {
  error: {
    code: ErrorCode;
    message: string;
    details?: unknown;
    timestamp: string;
    path?: string;
  };
}

// Custom API Error class
export class ApiError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly details?: unknown;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number,
    details?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

// Pre-defined error creators
export const createApiError = {
  unauthorized: (message = 'Authentication required') =>
    new ApiError(ErrorCode.UNAUTHORIZED, message, 401),

  forbidden: (message = 'Insufficient permissions') =>
    new ApiError(ErrorCode.FORBIDDEN, message, 403),

  notFound: (resource = 'Resource', message?: string) =>
    new ApiError(
      ErrorCode.NOT_FOUND,
      message || `${resource} not found`,
      404
    ),

  validation: (message: string, details?: unknown) =>
    new ApiError(ErrorCode.VALIDATION_ERROR, message, 400, details),

  conflict: (message: string) =>
    new ApiError(ErrorCode.RESOURCE_CONFLICT, message, 409),

  rateLimit: (message = 'Too many requests', retryAfter?: number) =>
    new ApiError(ErrorCode.RATE_LIMIT_EXCEEDED, message, 429, { retryAfter }),

  internal: (message = 'Internal server error') =>
    new ApiError(ErrorCode.INTERNAL_SERVER_ERROR, message, 500),
};

// Error response formatter
export function formatErrorResponse(
  error: ApiError | Error,
  path?: string
): ApiErrorResponse {
  if (error instanceof ApiError) {
    return {
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: new Date().toISOString(),
        path,
      },
    };
  }

  // Handle validation errors (if using Zod or similar)
  if (error.name === 'ZodError' && 'errors' in error) {
    const zodError = error as { errors: Array<{ path: string[]; message: string; code?: string }> };
    return {
      error: {
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Validation failed',
        details: zodError.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        })),
        timestamp: new Date().toISOString(),
        path,
      },
    };
  }

  // Handle Prisma errors
  if (
    error.name === 'PrismaClientKnownRequestError' &&
    typeof error === 'object' &&
    error !== null &&
    'code' in error
  ) {
    const prismaError = error as { code: string; meta?: { target?: string[] } };
    switch (prismaError.code) {
      case 'P2002':
        return {
          error: {
            code: ErrorCode.ALREADY_EXISTS,
            message: 'A record with this data already exists',
            details: { fields: prismaError.meta?.target },
            timestamp: new Date().toISOString(),
            path,
          },
        };
      case 'P2025':
        return {
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Record not found',
            timestamp: new Date().toISOString(),
            path,
          },
        };
      default:
        return {
          error: {
            code: ErrorCode.DATABASE_ERROR,
            message: 'Database operation failed',
            details: { code: prismaError.code },
            timestamp: new Date().toISOString(),
            path,
          },
        };
    }
  }

  // Generic error fallback
  return {
    error: {
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message: process.env.NODE_ENV === 'development' 
        ? error.message 
        : 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      path,
    },
  };
}

// Create NextResponse with proper error formatting
export function createErrorResponse(
  error: ApiError | Error,
  path?: string,
  additionalHeaders?: Record<string, string>
): NextResponse {
  const errorResponse = formatErrorResponse(error, path);
  const statusCode = error instanceof ApiError ? error.statusCode : 500;

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...additionalHeaders,
  };

  // Add rate limit headers if it's a rate limit error
  if (error instanceof ApiError && error.code === ErrorCode.RATE_LIMIT_EXCEEDED) {
    const retryAfter = error.details as { retryAfter?: number };
    if (retryAfter?.retryAfter) {
      headers['Retry-After'] = retryAfter.retryAfter.toString();
    }
  }

  return NextResponse.json(errorResponse, {
    status: statusCode,
    headers,
  });
}

// Async error handler wrapper for API routes
export function withErrorHandler<T extends unknown[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);
      
      // Extract path from request if available
      const request = args.find(arg => arg && typeof arg === 'object' && 'url' in arg) as { url?: string } | undefined;
      const path = request?.url ? new URL(request.url).pathname : undefined;
      
      return createErrorResponse(error as Error, path);
    }
  };
}