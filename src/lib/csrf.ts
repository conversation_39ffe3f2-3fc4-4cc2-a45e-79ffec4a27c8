/**
 * CSRF Protection Utilities for Frontend
 */

let csrfToken: string | null = null;

/**
 * Fetch CSRF token from the server
 */
export async function fetchCSRFToken(): Promise<string> {
  try {
    const response = await fetch('/api/csrf-token', {
      method: 'GET',
      credentials: 'include', // Include cookies
    });

    if (!response.ok) {
      throw new Error('Failed to fetch CSRF token');
    }

    const data = await response.json();
    csrfToken = data.csrfToken;

    if (!csrfToken) {
      throw new Error('Received null CSRF token from server');
    }

    return csrfToken;
  } catch (error) {
    console.error('CSRF token fetch error:', error);
    throw error;
  }
}

/**
 * Get the current CSRF token, fetching if necessary
 */
export async function getCSRFToken(): Promise<string> {
  if (!csrfToken) {
    await fetchCSRFToken();
  }

  if (!csrfToken) {
    throw new Error('Failed to obtain CSRF token');
  }

  return csrfToken;
}

/**
 * Enhanced fetch with automatic CSRF token inclusion
 */
export async function secureFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // Skip CSRF for GET, HEAD, OPTIONS
  if (!options.method || ['GET', 'HEAD', 'OPTIONS'].includes(options.method.toUpperCase())) {
    return fetch(url, { ...options, credentials: 'include' });
  }

  // For state-changing operations, include CSRF token
  const token = await getCSRFToken();

  const secureOptions: RequestInit = {
    ...options,
    credentials: 'include',
    headers: {
      ...options.headers,
      'X-CSRF-Token': token,
      'Content-Type': 'application/json',
    },
  };

  return fetch(url, secureOptions);
}

/**
 * Hook for React components to use CSRF-protected requests
 */
export function useSecureFetch() {
  return {
    secureFetch,
    getCSRFToken,
    fetchCSRFToken,
  };
}

/**
 * Initialize CSRF token on app startup
 */
export async function initializeCSRF(): Promise<void> {
  try {
    await fetchCSRFToken();
  } catch (error) {
    console.warn('Failed to initialize CSRF token:', error);
    // Don't throw - allow app to continue without CSRF for now
  }
}