/**
 * Accessibility utilities for WCAG compliance
 */

// ARIA label generators
export const generateAriaLabel = {
  button: (action: string, context?: string) => 
    context ? `${action} ${context}` : action,
  
  link: (destination: string, context?: string) => 
    context ? `${context} - ${destination}` : `Go to ${destination}`,
  
  video: (title: string, platform?: string) => 
    platform ? `${title} on ${platform}` : title,
  
  form: (fieldName: string, required?: boolean) => 
    required ? `${fieldName} (required)` : fieldName,
  
  status: (status: string, context?: string) => 
    context ? `${context}: ${status}` : status,
  
  navigation: (section: string) => `Navigate to ${section}`,
  
  modal: (title: string) => `${title} dialog`,
  
  loading: (context?: string) => 
    context ? `Loading ${context}` : 'Loading content',
  
  error: (context?: string) => 
    context ? `Error in ${context}` : 'Error occurred',
  
  success: (context?: string) => 
    context ? `Success: ${context}` : 'Operation successful'
};

// ARIA describedby generators
export const generateAriaDescribedBy = {
  field: (fieldId: string, hasError?: boolean, hasHelp?: boolean) => {
    const ids = [];
    if (hasError) ids.push(`${fieldId}-error`);
    if (hasHelp) ids.push(`${fieldId}-help`);
    return ids.length > 0 ? ids.join(' ') : undefined;
  },
  
  button: (buttonId: string, hasTooltip?: boolean, hasStatus?: boolean) => {
    const ids = [];
    if (hasTooltip) ids.push(`${buttonId}-tooltip`);
    if (hasStatus) ids.push(`${buttonId}-status`);
    return ids.length > 0 ? ids.join(' ') : undefined;
  }
};

// Focus management utilities
export const focusManagement = {
  trapFocus: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  },

  restoreFocus: (previousElement: HTMLElement | null) => {
    if (previousElement && document.contains(previousElement)) {
      previousElement.focus();
    }
  },

  announceToScreenReader: (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
};

// Keyboard navigation utilities
export const keyboardNavigation = {
  handleArrowKeys: (
    e: KeyboardEvent, 
    items: HTMLElement[], 
    currentIndex: number,
    onIndexChange: (newIndex: number) => void
  ) => {
    let newIndex = currentIndex;
    
    switch (e.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'ArrowUp':
      case 'ArrowLeft':
        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = items.length - 1;
        break;
      default:
        return;
    }
    
    e.preventDefault();
    onIndexChange(newIndex);
    items[newIndex]?.focus();
  },

  handleEscapeKey: (e: KeyboardEvent, onEscape: () => void) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      onEscape();
    }
  },

  handleEnterSpace: (e: KeyboardEvent, onClick: () => void) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  }
};

// Color contrast utilities
export const colorContrast = {
  // High contrast color pairs that meet WCAG AA standards
  highContrast: {
    text: {
      primary: '#ffffff',
      secondary: '#e5e7eb',
      muted: '#9ca3af',
      error: '#fca5a5',
      success: '#86efac',
      warning: '#fcd34d',
      info: '#93c5fd'
    },
    background: {
      primary: '#000000',
      secondary: '#1f2937',
      surface: '#374151',
      error: '#7f1d1d',
      success: '#14532d',
      warning: '#92400e',
      info: '#1e3a8a'
    },
    border: {
      primary: '#6b7280',
      focus: '#3b82f6',
      error: '#dc2626',
      success: '#16a34a',
      warning: '#d97706'
    }
  },

  // Check if colors meet WCAG contrast requirements
  meetsContrastRequirement: (foreground: string, background: string, level: 'AA' | 'AAA' = 'AA') => {
    // This is a simplified check - in production, you'd use a proper color contrast library
    const _requiredRatio = level === 'AAA' ? 7 : 4.5;
    // Implementation would calculate actual contrast ratio
    return true; // Placeholder
  }
};

// Screen reader utilities
export const screenReader = {
  hideFromScreenReader: (element: HTMLElement) => {
    element.setAttribute('aria-hidden', 'true');
  },

  showToScreenReader: (element: HTMLElement) => {
    element.removeAttribute('aria-hidden');
  },

  setScreenReaderOnly: (element: HTMLElement) => {
    element.className += ' sr-only';
  },

  announcePageChange: (pageTitle: string) => {
    focusManagement.announceToScreenReader(`Navigated to ${pageTitle}`, 'assertive');
  },

  announceFormError: (fieldName: string, error: string) => {
    focusManagement.announceToScreenReader(`Error in ${fieldName}: ${error}`, 'assertive');
  },

  announceSuccess: (message: string) => {
    focusManagement.announceToScreenReader(message, 'polite');
  }
};

// Semantic HTML helpers
export const semanticHTML = {
  getHeadingLevel: (level: number): 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' => {
    const clampedLevel = Math.max(1, Math.min(6, level));
    return `h${clampedLevel}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  },

  getLandmarkRole: (section: string): string | undefined => {
    const roles: Record<string, string> = {
      header: 'banner',
      nav: 'navigation',
      main: 'main',
      aside: 'complementary',
      footer: 'contentinfo',
      search: 'search',
      form: 'form'
    };
    return roles[section];
  }
};

// Form accessibility helpers
export const formAccessibility = {
  generateFieldIds: (baseId: string) => ({
    field: baseId,
    label: `${baseId}-label`,
    error: `${baseId}-error`,
    help: `${baseId}-help`,
    description: `${baseId}-description`
  }),

  getFieldAttributes: (
    fieldId: string, 
    options: {
      required?: boolean;
      invalid?: boolean;
      hasError?: boolean;
      hasHelp?: boolean;
      errorMessage?: string;
      helpText?: string;
    } = {}
  ) => {
    const { required, invalid, hasError, hasHelp } = options;
    
    return {
      id: fieldId,
      'aria-required': required ? true : undefined,
      'aria-invalid': invalid ? true : undefined,
      'aria-describedby': generateAriaDescribedBy.field(fieldId, hasError, hasHelp)
    };
  }
};

// Button accessibility helpers
export const buttonAccessibility = {
  getButtonAttributes: (
    options: {
      pressed?: boolean;
      expanded?: boolean;
      disabled?: boolean;
      loading?: boolean;
      controls?: string;
      describedBy?: string;
    } = {}
  ) => {
    const { pressed, expanded, disabled, loading, controls, describedBy } = options;
    
    const attributes: Record<string, unknown> = {
      'aria-pressed': pressed !== undefined ? pressed.toString() : undefined,
      'aria-expanded': expanded !== undefined ? expanded.toString() : undefined,
      'aria-disabled': disabled ? 'true' : undefined,
      'aria-busy': loading ? 'true' : undefined,
      'aria-controls': controls,
      disabled: disabled || loading
    };

    // Only set aria-describedby if explicitly provided
    if (describedBy) {
      attributes['aria-describedby'] = describedBy;
    }

    return attributes;
  }
};

// Modal accessibility helpers
export const modalAccessibility = {
  getModalAttributes: (modalId: string, labelledBy?: string, describedBy?: string) => ({
    role: 'dialog',
    'aria-modal': true,
    'aria-labelledby': labelledBy || `${modalId}-title`,
    'aria-describedby': describedBy || `${modalId}-description`,
    tabIndex: -1
  }),

  setupModalFocus: (modalElement: HTMLElement, previousFocus?: HTMLElement) => {
    const cleanup = focusManagement.trapFocus(modalElement);
    
    return () => {
      cleanup();
      focusManagement.restoreFocus(previousFocus ?? null);
    };
  }
};