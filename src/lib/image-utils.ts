/**
 * Image optimization utilities for video thumbnails
 * Provides thumbnail generation, lazy loading, and WebP format support
 */

export interface ThumbnailOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
}

export interface LazyImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

/**
 * Generate thumbnail URL from video platform URLs
 */
export function generateThumbnailUrl(
  platforms: {
    youtube?: string;
    tiktok?: string;
    rumble?: string;
  },
  options: ThumbnailOptions = {}
): string | null {
  const { width: _width = 480, height: _height = 270, quality: _quality = 75 } = options;

  // Try YouTube first (best thumbnail quality)
  if (platforms.youtube) {
    const videoId = extractYouTubeVideoId(platforms.youtube);
    if (videoId) {
      // Use YouTube's high quality thumbnail
      return `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
    }
  }

  // Try TikTok
  if (platforms.tiktok) {
    // TikTok thumbnails are harder to extract, but we can try
    const videoId = extractTikTokVideoId(platforms.tiktok);
    if (videoId) {
      // TikTok thumbnail format (may not always work due to CORS)
      return `https://p16-sign.tiktokcdn-us.com/obj/tos-maliva-p-0068/oEeNAf8gfDIABjbEjhD9HfEfEeEeEeEe~tplv-photomode-image:720:720.jpeg?x-expires=1640995200&x-signature=placeholder`;
    }
  }

  // Try Rumble
  if (platforms.rumble) {
    // Rumble doesn't have a standard thumbnail API, return null
    return null;
  }

  return null;
}

/**
 * Extract YouTube video ID from various YouTube URL formats
 */
export function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

/**
 * Extract TikTok video ID from TikTok URL
 */
export function extractTikTokVideoId(url: string): string | null {
  const patterns = [
    /tiktok\.com\/@[^/]+\/video\/(\d+)/,
    /tiktok\.com\/v\/(\d+)/,
    /vm\.tiktok\.com\/([^/]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

/**
 * Generate optimized image URL with Next.js Image Optimization API
 */
export function getOptimizedImageUrl(src: string, options: ThumbnailOptions = {}): string {
  const { width = 480, height = 270, quality = 75 } = options;
  
  // If it's already an optimized URL, return as-is
  if (src.includes('/_next/image')) {
    return src;
  }

  // Create Next.js optimized image URL
  const params = new URLSearchParams({
    url: src,
    w: width.toString(),
    h: height.toString(),
    q: quality.toString(),
  });

  return `/_next/image?${params.toString()}`;
}

/**
 * Generate blur placeholder for lazy loading
 */
export function generateBlurDataURL(): string {
  // Create a simple gradient blur placeholder
  const svg = `
    <svg width="10" height="6" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#4c1d95;stop-opacity:0.5" />
          <stop offset="100%" style="stop-color:#0891b2;stop-opacity:0.5" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" />
    </svg>
  `;

  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Check if WebP is supported by the browser
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      // Server-side, assume WebP is supported
      resolve(true);
      return;
    }

    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Preload critical images
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Get responsive image sizes for different breakpoints
 */
export function getResponsiveSizes(): string {
  return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
}

/**
 * Image loading states for better UX
 */
export type ImageLoadingState = 'loading' | 'loaded' | 'error';

/**
 * Hook for managing image loading state
 */
import React from 'react';

export function useImageLoadingState(src: string) {
  const [loadingState, setLoadingState] = React.useState<ImageLoadingState>('loading');

  const retry = React.useCallback(() => {
    setLoadingState('loading');
    preloadImage(src)
      .then(() => setLoadingState('loaded'))
      .catch(() => setLoadingState('error'));
  }, [src]);

  React.useEffect(() => {
    retry();
  }, [retry]);

  return { loadingState, retry };
}