/**
 * Application Performance Monitoring and Alerting System
 * 
 * This module provides comprehensive monitoring capabilities including
 * performance metrics, error tracking, health checks, and alerting.
 */

import { prisma } from './prisma';
import { redisClient as redis } from './redis';
import { ErrorContext } from './error-tracking';

export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  unit?: string;
}

export interface AlertRule {
  id: string;
  name: string;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration: number; // seconds
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  channels: string[]; // email, slack, webhook
}

export interface HealthCheckResult {
   service: string;
   status: 'healthy' | 'degraded' | 'unhealthy';
   responseTime: number;
   message?: string;
   details?: Record<string, unknown>;
}

interface AlertRecord {
  rule: string;
  metric: string;
  value: number;
  threshold: number;
  severity: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

export class ApplicationMonitor {
  private static metrics: Map<string, MetricData[]> = new Map();
  private static alerts: Map<string, AlertRule> = new Map();
  private static healthChecks: Map<string, () => Promise<HealthCheckResult>> = new Map();

  /**
   * Record a metric value
   */
  static recordMetric(data: MetricData): void {
    const key = `${data.name}:${JSON.stringify(data.tags || {})}`;
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const metrics = this.metrics.get(key)!;
    metrics.push(data);
    
    // Keep only last 1000 data points per metric
    if (metrics.length > 1000) {
      metrics.shift();
    }
    
    // Check alert rules
    this.checkAlerts(data);
    
    // Store in Redis for real-time access
    this.storeMetricInRedis(data);
  }

  /**
   * Record API response time
   */
  static recordApiResponseTime(endpoint: string, method: string, statusCode: number, responseTime: number): void {
    this.recordMetric({
      name: 'api_response_time',
      value: responseTime,
      timestamp: new Date(),
      tags: {
        endpoint,
        method,
        status_code: statusCode.toString()
      },
      unit: 'ms'
    });

    // Record error rate
    this.recordMetric({
      name: 'api_error_rate',
      value: statusCode >= 400 ? 1 : 0,
      timestamp: new Date(),
      tags: {
        endpoint,
        method
      }
    });
  }

  /**
   * Record database query performance
   */
  static recordDatabaseQuery(query: string, duration: number, success: boolean): void {
    this.recordMetric({
      name: 'database_query_time',
      value: duration,
      timestamp: new Date(),
      tags: {
        query_type: this.extractQueryType(query),
        success: success.toString()
      },
      unit: 'ms'
    });
  }

  /**
   * Record cache performance
   */
  static recordCacheOperation(operation: 'hit' | 'miss' | 'set' | 'delete', key: string, duration?: number): void {
    this.recordMetric({
      name: 'cache_operation',
      value: duration || 0,
      timestamp: new Date(),
      tags: {
        operation,
        key_pattern: this.extractKeyPattern(key)
      },
      unit: 'ms'
    });
  }

  /**
   * Record error occurrence
   */
  static recordError(error: Error, context?: Record<string, unknown> | ErrorContext): void {
    // Convert context to string values for tags
    const stringContext: Record<string, string> = {};
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Convert non-string values to strings
          if (typeof value === 'object') {
            stringContext[key] = JSON.stringify(value);
          } else {
            stringContext[key] = String(value);
          }
        }
      });
    }
    
    this.recordMetric({
      name: 'application_errors',
      value: 1,
      timestamp: new Date(),
      tags: {
        error_type: error.constructor.name,
        error_message: error.message.substring(0, 100),
        ...stringContext
      }
    });

    // Log error details
    console.error('Application Error:', {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Register an alert rule
   */
  static registerAlert(rule: AlertRule): void {
    this.alerts.set(rule.id, rule);
  }

  /**
   * Register a health check
   */
  static registerHealthCheck(name: string, check: () => Promise<HealthCheckResult>): void {
    this.healthChecks.set(name, check);
  }

  /**
   * Run all health checks
   */
  static async runHealthChecks(): Promise<Record<string, HealthCheckResult>> {
    const results: Record<string, HealthCheckResult> = {};
    
    for (const [name, check] of this.healthChecks.entries()) {
      try {
        const startTime = Date.now();
        const result = await Promise.race([
          check(),
          new Promise<HealthCheckResult>((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), 5000)
          )
        ]);
        
        results[name] = {
          ...result,
          responseTime: Date.now() - startTime
        };
      } catch (error) {
        results[name] = {
          service: name,
          status: 'unhealthy',
          responseTime: Date.now(),
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    return results;
  }

  /**
   * Get metrics for a specific time range
   */
  static getMetrics(name: string, tags?: Record<string, string>, since?: Date): MetricData[] {
    const key = `${name}:${JSON.stringify(tags || {})}`;
    const metrics = this.metrics.get(key) || [];
    
    if (since) {
      return metrics.filter(m => m.timestamp >= since);
    }
    
    return metrics;
  }

  /**
   * Get system health status
   */
  static async getSystemHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, HealthCheckResult>;
    metrics: {
      uptime: number;
      memoryUsage: NodeJS.MemoryUsage;
      cpuUsage: NodeJS.CpuUsage;
    };
  }> {
    const checks = await this.runHealthChecks();
    const overallStatus = this.calculateOverallHealth(checks);
    
    return {
      status: overallStatus,
      checks,
      metrics: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      }
    };
  }

  private static async storeMetricInRedis(data: MetricData): Promise<void> {
    // Avoid Redis side effects during tests to keep the suite deterministic
    if (process.env.NODE_ENV === 'test') return;
    try {
      const client = await redis.getClient();
      const key = `metrics:${data.name}`;
      const value = JSON.stringify({
        ...data,
        timestamp: data.timestamp.toISOString()
      });
      
      await client.lPush(key, value);
      await client.lTrim(key, 0, 999); // Keep last 1000 entries
      await client.expire(key, 3600); // Expire after 1 hour
    } catch (error) {
      console.warn('Failed to store metric in Redis:', error);
    }
  }

  private static checkAlerts(data: MetricData): void {
    for (const [_, rule] of this.alerts.entries()) {
      if (!rule.enabled || rule.metric !== data.name) continue;
      
      const shouldAlert = this.evaluateAlertCondition(rule, data.value);
      
      if (shouldAlert) {
        this.triggerAlert(rule, data);
      }
    }
  }

  private static evaluateAlertCondition(rule: AlertRule, value: number): boolean {
    switch (rule.condition) {
      case 'gt': return value > rule.threshold;
      case 'gte': return value >= rule.threshold;
      case 'lt': return value < rule.threshold;
      case 'lte': return value <= rule.threshold;
      case 'eq': return value === rule.threshold;
      default: return false;
    }
  }

  private static triggerAlert(rule: AlertRule, data: MetricData): void {
    const alert = {
      rule: rule.name,
      metric: data.name,
      value: data.value,
      threshold: rule.threshold,
      severity: rule.severity,
      timestamp: data.timestamp,
      tags: data.tags
    };

    console.warn('ALERT TRIGGERED:', alert);
    
    // Send to configured channels
    rule.channels.forEach(channel => {
      this.sendAlert(channel, alert);
    });
  }

  private static async sendAlert(channel: string, alert: unknown): Promise<void> {
    try {
      switch (channel) {
        case 'email':
          await this.sendEmailAlert(alert);
          break;
        case 'slack':
          await this.sendSlackAlert(alert);
          break;
        case 'webhook':
          await this.sendWebhookAlert(alert);
          break;
        default:
          console.log(`Alert sent to ${channel}:`, alert);
      }
    } catch (error) {
      console.error(`Failed to send alert to ${channel}:`, error);
    }
  }

  private static async sendEmailAlert(alert: unknown): Promise<void> {
    // Implementation would integrate with email service
    console.log('Email alert:', alert);
  }

  private static async sendSlackAlert(alert: unknown): Promise<void> {
    // Implementation would integrate with Slack webhook
    console.log('Slack alert:', alert);
  }

  private static async sendWebhookAlert(alert: unknown): Promise<void> {
    // Implementation would send HTTP POST to webhook URL
    console.log('Webhook alert:', alert);
  }

  private static extractQueryType(query: string): string {
    const match = query.trim().match(/^(\w+)/i);
    return match ? match[1].toUpperCase() : 'UNKNOWN';
  }

  private static createAlertRecord(alert: unknown): AlertRecord {
    if (this.isAlertObject(alert)) {
      return {
        rule: alert.rule,
        metric: alert.metric,
        value: alert.value,
        threshold: alert.threshold,
        severity: alert.severity,
        timestamp: new Date(alert.timestamp),
        tags: alert.tags || {}
      };
    }
    return {
      rule: 'unknown',
      metric: 'unknown',
      value: 0,
      threshold: 0,
      severity: 'low',
      timestamp: new Date(),
      tags: {}
    };
  }

  private static isAlertObject(alert: unknown): alert is AlertRecord {
    const obj = alert as AlertRecord;
    return (
      obj &&
      typeof obj.rule === 'string' &&
      typeof obj.metric === 'string' &&
      typeof obj.value === 'number' &&
      typeof obj.threshold === 'number' &&
      typeof obj.severity === 'string' &&
      obj.timestamp instanceof Date
    );
  }

  private static extractKeyPattern(key: string): string {
    // Extract pattern from cache key (e.g., "user:123" -> "user:*")
    return key.replace(/:\d+/g, ':*').replace(/:[a-f0-9-]{36}/g, ':*');
  }

  private static calculateOverallHealth(checks: Record<string, HealthCheckResult>): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(checks).map(c => c.status);
    
    if (statuses.every(s => s === 'healthy')) return 'healthy';
    if (statuses.some(s => s === 'unhealthy')) return 'unhealthy';
    return 'degraded';
  }
}

// Default health checks
ApplicationMonitor.registerHealthCheck('database', async (): Promise<HealthCheckResult> => {
  try {
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;
    
    return {
      service: 'database',
      status: responseTime < 1000 ? 'healthy' : 'degraded',
      responseTime,
      details: { responseTime }
    };
  } catch (error) {
    return {
      service: 'database',
      status: 'unhealthy',
      responseTime: 0,
      message: error instanceof Error ? error.message : 'Database connection failed'
    };
  }
});

ApplicationMonitor.registerHealthCheck('redis', async (): Promise<HealthCheckResult> => {
  try {
    const startTime = Date.now();
    const client = await redis.getClient();
    await client.ping();
    const responseTime = Date.now() - startTime;
    
    return {
      service: 'redis',
      status: responseTime < 500 ? 'healthy' : 'degraded',
      responseTime,
      details: { responseTime }
    };
  } catch (error) {
    return {
      service: 'redis',
      status: 'unhealthy',
      responseTime: 0,
      message: error instanceof Error ? error.message : 'Redis connection failed'
    };
  }
});

// Default alert rules
ApplicationMonitor.registerAlert({
  id: 'high_api_response_time',
  name: 'High API Response Time',
  metric: 'api_response_time',
  condition: 'gt',
  threshold: 2000, // 2 seconds
  duration: 60,
  severity: 'medium',
  enabled: true,
  channels: ['email', 'slack']
});

ApplicationMonitor.registerAlert({
  id: 'high_error_rate',
  name: 'High Error Rate',
  metric: 'api_error_rate',
  condition: 'gt',
  threshold: 0.1, // 10% error rate
  duration: 300,
  severity: 'high',
  enabled: true,
  channels: ['email', 'slack', 'webhook']
});

ApplicationMonitor.registerAlert({
  id: 'database_slow_queries',
  name: 'Slow Database Queries',
  metric: 'database_query_time',
  condition: 'gt',
  threshold: 5000, // 5 seconds
  duration: 60,
  severity: 'medium',
  enabled: true,
  channels: ['email']
});