import { prisma } from '@/lib/prisma';

export class NotificationService {
  /**
   * Create a new notification for a user
   */
  static async createNotification({
    userId,
    title,
    message,
    type,
    sentBy,
    videoId,
  }: {
    userId: string;
    title: string;
    message: string;
    type: string;
    sentBy: string;
    videoId?: string;
  }) {
    try {
      const notification = await prisma.notification.create({
        data: {
          title,
          message,
          type,
          sentBy,
          videoId,
          recipients: {
            create: {
              userId: userId,
            },
          },
        },
      });

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Create a system-wide announcement
   */
  static async createAnnouncement({
    title,
    message,
    sentBy,
    type = 'announcement',
  }: {
    title: string;
    message: string;
    sentBy: string;
    type?: string;
  }) {
    try {
      const allUserIds = await prisma.user.findMany({
        select: {
          id: true,
        },
      });

      // Create one notification and link it to all users via UserNotification records
      const notification = await prisma.notification.create({
        data: {
          title,
          message,
          type,
          sentBy,
          recipients: {
            create: allUserIds.map(user => ({
              userId: user.id,
            })),
          },
        },
        include: {
          recipients: true,
        },
      });

      return notification;
    } catch (error) {
      console.error('Error creating announcement:', error);
      throw error;
    }
  }

  /**
   * Create video notification respecting user preferences
   */
  static async createVideoNotification({
    videoId,
    title,
    message,
    sentBy,
    type = 'nwa',
  }: {
    videoId: string;
    title: string;
    message: string;
    sentBy: string;
    type?: 'nwa' | 'user';
  }) {
    try {
      // Get eligible users based on notification type and preferences
      const userWhereClause = type === 'nwa' 
        ? { nwaVideoNotifications: true }
        : { userVideoNotifications: true };

      const eligibleUsers = await prisma.user.findMany({
        where: userWhereClause,
        select: { id: true }
      });

      if (eligibleUsers.length === 0) {
        return null; // No users to notify
      }

      const notification = await prisma.notification.create({
        data: {
          title,
          message,
          type: type === 'nwa' ? 'VIDEO_RELEASE' : 'USER_VIDEO',
          sentBy,
          videoId,
          isSent: true,
          sentAt: new Date(),
          recipients: {
            create: eligibleUsers.map(user => ({
              userId: user.id,
            })),
          },
        },
        include: {
          recipients: true,
        },
      });

      return notification;
    } catch (error) {
      console.error('Error creating video notification:', error);
      throw error;
    }
  }

  /**
   * Send bulk video notifications
   */
  static async sendBulkVideoNotifications({
    videoIds,
    sentBy,
    type = 'nwa',
  }: {
    videoIds: string[];
    sentBy: string;
    type?: 'nwa' | 'user';
  }) {
    try {
      // Fetch videos to validate they exist
      const videos = await prisma.video.findMany({
        where: {
          id: { in: videoIds }
        },
        select: {
          id: true,
          title: true,
          notificationSentAt: true
        }
      });

      if (videos.length !== videoIds.length) {
        const foundIds = videos.map(v => v.id);
        const missingIds = videoIds.filter(id => !foundIds.includes(id));
        throw new Error(`Videos not found: ${missingIds.join(', ')}`);
      }

      // Filter out videos that already have notifications sent
      const videosToNotify = videos.filter(v => !v.notificationSentAt);

      if (videosToNotify.length === 0) {
        return {
          success: false,
          message: 'All videos already have notifications sent',
          results: []
        };
      }

      // Get eligible users based on notification type and preferences
      const userWhereClause = type === 'nwa' 
        ? { nwaVideoNotifications: true }
        : { userVideoNotifications: true };

      const eligibleUsers = await prisma.user.findMany({
        where: userWhereClause,
        select: { id: true }
      });

      if (eligibleUsers.length === 0) {
        return {
          success: false,
          message: 'No users have opted in for this notification type',
          results: []
        };
      }

      const results = [];

      // Create notifications for each video
      for (const video of videosToNotify) {
        const notification = await prisma.notification.create({
          data: {
            title: type === 'nwa' ? 'New NWA Video!' : 'New User Video!',
            message: `Check out the new video: "${video.title}"`,
            type: type === 'nwa' ? 'VIDEO_RELEASE' : 'USER_VIDEO',
            sentBy,
            videoId: video.id,
            isSent: true,
            sentAt: new Date(),
            recipients: {
              create: eligibleUsers.map(user => ({
                userId: user.id,
              })),
            },
          },
        });

        // Update video to mark notification as sent
        await prisma.video.update({
          where: { id: video.id },
          data: { notificationSentAt: new Date() },
        });

        results.push({
          videoId: video.id,
          videoTitle: video.title,
          notificationId: notification.id,
          recipientCount: eligibleUsers.length
        });
      }

      return {
        success: true,
        message: `Notifications sent successfully for ${results.length} video(s)`,
        results
      };

    } catch (error) {
      console.error('Error sending bulk video notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notifications as read
   */
  static async markAsRead(userNotificationIds: string[], userId: string) {
    try {
      const result = await prisma.userNotification.updateMany({
        where: {
          id: {
            in: userNotificationIds,
          },
          userId: userId, // Ensure user can only update their own notifications
        },
        data: {
          isRead: true,
        },
      });

      return result;
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   */
  static async getUnreadCount(userId: string) {
    try {
      const count = await prisma.userNotification.count({
        where: {
          userId: userId,
          isRead: false,
        },
      });

      return count;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      throw error;
    }
  }

  /**
   * Queue an email notification
   */
  static async queueEmailNotification(
    userId: string,
    emailAddress: string,
    subject: string,
    bodyHtml: string,
    bodyText: string
  ) {
    try {
      const emailQueueItem = await prisma.emailQueue.create({
        data: {
          userId,
          emailAddress,
          subject,
          bodyHtml,
          bodyText,
          status: 'pending'
        }
      });

      return emailQueueItem;
    } catch (error) {
      console.error('Error queuing email notification:', error);
      throw error;
    }
  }

  /**
   * Check if user has email notifications enabled for a specific type
   */
  static async isEmailNotificationEnabled(userId: string, notificationType: string) {
    try {
      const preference = await prisma.emailPreference.findUnique({
        where: {
          userId_notificationType: {
            userId,
            notificationType
          }
        }
      });

      // If no preference is set, default to enabled
      return preference ? preference.isEnabled : true;
    } catch (error) {
      console.error('Error checking email notification preference:', error);
      throw error;
    }
  }

  /**
   * Update email notification preferences
   */
  static async updateEmailPreferences(
    userId: string,
    preferences: { notificationType: string; isEnabled: boolean }[]
  ) {
    try {
      // Update each preference
      const updates = preferences.map(preference =>
        prisma.emailPreference.upsert({
          where: {
            userId_notificationType: {
              userId,
              notificationType: preference.notificationType
            }
          },
          update: {
            isEnabled: preference.isEnabled
          },
          create: {
            userId,
            notificationType: preference.notificationType,
            isEnabled: preference.isEnabled
          }
        })
      );

      await Promise.all(updates);
      
      return true;
    } catch (error) {
      console.error('Error updating email preferences:', error);
      throw error;
    }
  }
}