/**
 * Internationalization (i18n) Framework
 * 
 * This module provides comprehensive internationalization support
 * including translation management, locale detection, and formatting.
 */

export type Locale = 'en' | 'es' | 'fr' | 'de' | 'pt' | 'zh' | 'ja' | 'ko';

export interface TranslationNamespace {
  [key: string]: string | TranslationNamespace;
}

export interface Translations {
  [locale: string]: {
    [namespace: string]: TranslationNamespace;
  };
}

export interface I18nConfig {
  defaultLocale: Locale;
  supportedLocales: Locale[];
  fallbackLocale: Locale;
  namespaces: string[];
  interpolation: {
    prefix: string;
    suffix: string;
  };
}

export const DEFAULT_I18N_CONFIG: I18nConfig = {
  defaultLocale: 'en',
  supportedLocales: ['en', 'es', 'fr', 'de', 'pt', 'zh', 'ja', 'ko'],
  fallbackLocale: 'en',
  namespaces: ['common', 'navigation', 'auth', 'dashboard', 'admin', 'errors'],
  interpolation: {
    prefix: '{{',
    suffix: '}}'
  }
};

export class I18nManager {
  private static instance: I18nManager;
  private config: I18nConfig;
  private translations: Translations = {};
  private currentLocale: Locale;
  private loadedNamespaces: Set<string> = new Set();

  constructor(config: I18nConfig = DEFAULT_I18N_CONFIG) {
    this.config = config;
    this.currentLocale = config.defaultLocale;
  }

  static getInstance(config?: I18nConfig): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager(config);
    }
    return I18nManager.instance;
  }

  /**
   * Initialize i18n with translations
   */
  async init(locale?: Locale): Promise<void> {
    if (locale && this.config.supportedLocales.includes(locale)) {
      this.currentLocale = locale;
    }

    // Load core namespaces
    await Promise.all([
      this.loadNamespace('common'),
      this.loadNamespace('navigation'),
      this.loadNamespace('errors')
    ]);
  }

  /**
   * Load a translation namespace
   */
  async loadNamespace(namespace: string): Promise<void> {
    if (this.loadedNamespaces.has(namespace)) {
      return;
    }

    try {
      // In a real implementation, this would load from files or API
      const translations = await this.loadTranslationFile(namespace);
      
      for (const [locale, translation] of Object.entries(translations)) {
        if (!this.translations[locale]) {
          this.translations[locale] = {};
        }
        this.translations[locale][namespace] = translation;
      }

      this.loadedNamespaces.add(namespace);
    } catch (error) {
      console.warn(`Failed to load translation namespace: ${namespace}`, error);
    }
  }

  /**
   * Get translation for a key
   */
  t(key: string, options: {
    ns?: string;
    locale?: Locale;
    interpolation?: Record<string, string | number>;
    count?: number;
    defaultValue?: string;
  } = {}): string {
    const {
      ns = 'common',
      locale = this.currentLocale,
      interpolation = {},
      count,
      defaultValue = key
    } = options;

    // Handle pluralization
    const actualKey = count !== undefined ? this.getPluralKey(key, count, locale) : key;
    
    // Get translation
    let translation = this.getTranslation(actualKey, ns, locale);
    
    // Fallback to default locale
    if (!translation && locale !== this.config.fallbackLocale) {
      translation = this.getTranslation(actualKey, ns, this.config.fallbackLocale);
    }
    
    // Use default value if no translation found
    if (!translation) {
      translation = defaultValue;
    }

    // Apply interpolation
    return this.interpolate(translation, interpolation);
  }

  /**
   * Change current locale
   */
  async changeLocale(locale: Locale): Promise<void> {
    if (!this.config.supportedLocales.includes(locale)) {
      throw new Error(`Unsupported locale: ${locale}`);
    }

    this.currentLocale = locale;
    
    // Reload all loaded namespaces for new locale
    const namespacesToReload = Array.from(this.loadedNamespaces);
    this.loadedNamespaces.clear();
    
    await Promise.all(
      namespacesToReload.map(ns => this.loadNamespace(ns))
    );

    // Trigger locale change event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('localeChanged', { 
        detail: { locale } 
      }));
    }
  }

  /**
   * Get current locale
   */
  getCurrentLocale(): Locale {
    return this.currentLocale;
  }

  /**
   * Get supported locales
   */
  getSupportedLocales(): Locale[] {
    return this.config.supportedLocales;
  }

  /**
   * Detect locale from browser or other sources
   */
  detectLocale(): Locale {
    // Browser detection
    if (typeof window !== 'undefined') {
      const browserLang = navigator.language.split('-')[0] as Locale;
      if (this.config.supportedLocales.includes(browserLang)) {
        return browserLang;
      }
    }

    // URL detection
    if (typeof window !== 'undefined') {
      const urlLang = window.location.pathname.split('/')[1] as Locale;
      if (this.config.supportedLocales.includes(urlLang)) {
        return urlLang;
      }
    }

    // Cookie/localStorage detection
    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('locale') as Locale;
      if (storedLang && this.config.supportedLocales.includes(storedLang)) {
        return storedLang;
      }
    }

    return this.config.defaultLocale;
  }

  /**
   * Format date according to locale
   */
  formatDate(date: Date, options: Intl.DateTimeFormatOptions = {}): string {
    return new Intl.DateTimeFormat(this.currentLocale, options).format(date);
  }

  /**
   * Format number according to locale
   */
  formatNumber(number: number, options: Intl.NumberFormatOptions = {}): string {
    return new Intl.NumberFormat(this.currentLocale, options).format(number);
  }

  /**
   * Format currency according to locale
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat(this.currentLocale, {
      style: 'currency',
      currency
    }).format(amount);
  }

  /**
   * Get relative time format
   */
  formatRelativeTime(value: number, unit: Intl.RelativeTimeFormatUnit): string {
    return new Intl.RelativeTimeFormat(this.currentLocale).format(value, unit);
  }

  private async loadTranslationFile(namespace: string): Promise<Record<string, TranslationNamespace>> {
    // This would typically load from JSON files or an API
    // For now, return default translations
    return this.getDefaultTranslations(namespace);
  }

  private getTranslation(key: string, namespace: string, locale: Locale): string | undefined {
    const keys = key.split('.');
    let current: string | TranslationNamespace | undefined = this.translations[locale]?.[namespace];

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return undefined;
      }
    }

    return typeof current === 'string' ? current : undefined;
  }

  private getPluralKey(key: string, count: number, locale: Locale): string {
    // Simple pluralization rules (would be more complex for different languages)
    const rules = this.getPluralRules(locale);
    const rule = rules.select(count);
    
    return `${key}_${rule}`;
  }

  private getPluralRules(locale: Locale): Intl.PluralRules {
    return new Intl.PluralRules(locale);
  }

  private interpolate(text: string, values: Record<string, string | number>): string {
    const { prefix, suffix } = this.config.interpolation;
    
    return text.replace(
      new RegExp(`${this.escapeRegex(prefix)}([^${this.escapeRegex(suffix)}]+)${this.escapeRegex(suffix)}`, 'g'),
      (match, key) => {
        const value = values[key.trim()];
        return value !== undefined ? String(value) : match;
      }
    );
  }

  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private getDefaultTranslations(namespace: string): Record<string, TranslationNamespace> {
    const translations: Record<string, TranslationNamespace> = {};

    // English translations
    translations.en = this.getEnglishTranslations(namespace);
    
    // Spanish translations
    translations.es = this.getSpanishTranslations(namespace);
    
    // French translations
    translations.fr = this.getFrenchTranslations(namespace);

    // Add more languages as needed
    
    return translations;
  }

  private getEnglishTranslations(namespace: string): TranslationNamespace {
    const translations: Record<string, TranslationNamespace> = {
      common: {
        loading: 'Loading...',
        error: 'An error occurred',
        success: 'Success',
        cancel: 'Cancel',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        create: 'Create',
        update: 'Update',
        search: 'Search',
        filter: 'Filter',
        clear: 'Clear',
        submit: 'Submit',
        close: 'Close',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        yes: 'Yes',
        no: 'No'
      },
      navigation: {
        dashboard: 'Dashboard',
        videos: 'Videos',
        leaderboard: 'Leaderboard',
        notifications: 'Notifications',
        settings: 'Settings',
        admin: 'Admin',
        logout: 'Logout',
        profile: 'Profile'
      },
      auth: {
        signin: 'Sign In',
        signup: 'Sign Up',
        signout: 'Sign Out',
        email: 'Email',
        password: 'Password',
        confirmPassword: 'Confirm Password',
        forgotPassword: 'Forgot Password?',
        rememberMe: 'Remember me',
        createAccount: 'Create Account',
        alreadyHaveAccount: 'Already have an account?',
        dontHaveAccount: "Don't have an account?"
      },
      dashboard: {
        welcome: 'Welcome to NWA Promote',
        nwaVideos: 'NWA Videos',
        userVideos: 'User Videos',
        stats: 'Statistics',
        recentActivity: 'Recent Activity'
      },
      admin: {
        userManagement: 'User Management',
        videoManagement: 'Video Management',
        analytics: 'Analytics',
        moderation: 'Content Moderation',
        monitoring: 'System Monitoring',
        auditLogs: 'Audit Logs'
      },
      errors: {
        notFound: 'Page not found',
        unauthorized: 'Unauthorized access',
        forbidden: 'Access forbidden',
        serverError: 'Internal server error',
        networkError: 'Network connection error',
        validationError: 'Validation error'
      }
    };

    return translations[namespace] || {};
  }

  private getSpanishTranslations(namespace: string): TranslationNamespace {
    const translations: Record<string, TranslationNamespace> = {
      common: {
        loading: 'Cargando...',
        error: 'Ocurrió un error',
        success: 'Éxito',
        cancel: 'Cancelar',
        save: 'Guardar',
        delete: 'Eliminar',
        edit: 'Editar',
        create: 'Crear',
        update: 'Actualizar',
        search: 'Buscar',
        filter: 'Filtrar',
        clear: 'Limpiar',
        submit: 'Enviar',
        close: 'Cerrar',
        back: 'Atrás',
        next: 'Siguiente',
        previous: 'Anterior',
        yes: 'Sí',
        no: 'No'
      },
      navigation: {
        dashboard: 'Panel de Control',
        videos: 'Videos',
        leaderboard: 'Tabla de Clasificación',
        notifications: 'Notificaciones',
        settings: 'Configuración',
        admin: 'Administrador',
        logout: 'Cerrar Sesión',
        profile: 'Perfil'
      },
      auth: {
        signin: 'Iniciar Sesión',
        signup: 'Registrarse',
        signout: 'Cerrar Sesión',
        email: 'Correo Electrónico',
        password: 'Contraseña',
        confirmPassword: 'Confirmar Contraseña',
        forgotPassword: '¿Olvidaste tu contraseña?',
        rememberMe: 'Recordarme',
        createAccount: 'Crear Cuenta',
        alreadyHaveAccount: '¿Ya tienes una cuenta?',
        dontHaveAccount: '¿No tienes una cuenta?'
      },
      dashboard: {
        welcome: 'Bienvenido a NWA Promote',
        nwaVideos: 'Videos NWA',
        userVideos: 'Videos de Usuario',
        stats: 'Estadísticas',
        recentActivity: 'Actividad Reciente'
      },
      admin: {
        userManagement: 'Gestión de Usuarios',
        videoManagement: 'Gestión de Videos',
        analytics: 'Analíticas',
        moderation: 'Moderación de Contenido',
        monitoring: 'Monitoreo del Sistema',
        auditLogs: 'Registros de Auditoría'
      },
      errors: {
        notFound: 'Página no encontrada',
        unauthorized: 'Acceso no autorizado',
        forbidden: 'Acceso prohibido',
        serverError: 'Error interno del servidor',
        networkError: 'Error de conexión de red',
        validationError: 'Error de validación'
      }
    };

    return translations[namespace] || {};
  }

  private getFrenchTranslations(namespace: string): TranslationNamespace {
    const translations: Record<string, TranslationNamespace> = {
      common: {
        loading: 'Chargement...',
        error: 'Une erreur est survenue',
        success: 'Succès',
        cancel: 'Annuler',
        save: 'Enregistrer',
        delete: 'Supprimer',
        edit: 'Modifier',
        create: 'Créer',
        update: 'Mettre à jour',
        search: 'Rechercher',
        filter: 'Filtrer',
        clear: 'Effacer',
        submit: 'Soumettre',
        close: 'Fermer',
        back: 'Retour',
        next: 'Suivant',
        previous: 'Précédent',
        yes: 'Oui',
        no: 'Non'
      },
      navigation: {
        dashboard: 'Tableau de Bord',
        videos: 'Vidéos',
        leaderboard: 'Classement',
        notifications: 'Notifications',
        settings: 'Paramètres',
        admin: 'Administrateur',
        logout: 'Déconnexion',
        profile: 'Profil'
      },
      auth: {
        signin: 'Se Connecter',
        signup: "S'inscrire",
        signout: 'Se Déconnecter',
        email: 'Email',
        password: 'Mot de passe',
        confirmPassword: 'Confirmer le mot de passe',
        forgotPassword: 'Mot de passe oublié?',
        rememberMe: 'Se souvenir de moi',
        createAccount: 'Créer un compte',
        alreadyHaveAccount: 'Vous avez déjà un compte?',
        dontHaveAccount: "Vous n'avez pas de compte?"
      },
      dashboard: {
        welcome: 'Bienvenue sur NWA Promote',
        nwaVideos: 'Vidéos NWA',
        userVideos: 'Vidéos Utilisateur',
        stats: 'Statistiques',
        recentActivity: 'Activité Récente'
      },
      admin: {
        userManagement: 'Gestion des Utilisateurs',
        videoManagement: 'Gestion des Vidéos',
        analytics: 'Analytiques',
        moderation: 'Modération de Contenu',
        monitoring: 'Surveillance du Système',
        auditLogs: "Journaux d'Audit"
      },
      errors: {
        notFound: 'Page non trouvée',
        unauthorized: 'Accès non autorisé',
        forbidden: 'Accès interdit',
        serverError: 'Erreur interne du serveur',
        networkError: 'Erreur de connexion réseau',
        validationError: 'Erreur de validation'
      }
    };

    return translations[namespace] || {};
  }
}

// React hook for using translations
export function useTranslation(namespace: string = 'common') {
  const i18n = I18nManager.getInstance();
  
  const t = (key: string, options?: Parameters<typeof i18n.t>[1]) => {
    return i18n.t(key, { ...options, ns: namespace });
  };

  return {
    t,
    i18n,
    locale: i18n.getCurrentLocale(),
    changeLocale: i18n.changeLocale.bind(i18n)
  };
}

// Export singleton instance
export const i18n = I18nManager.getInstance();