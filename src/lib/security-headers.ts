/**
 * Security Headers Configuration
 * 
 * This module provides security headers and middleware to protect against
 * common web vulnerabilities.
 */

import { NextRequest, NextResponse } from 'next/server';

export interface SecurityConfig {
  contentSecurityPolicy?: string;
  frameOptions?: string;
  contentTypeOptions?: string;
  referrerPolicy?: string;
  xssProtection?: string;
  strictTransportSecurity?: string;
  permissionsPolicy?: string;
}

export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "media-src 'self' https:",
    "connect-src 'self' https: wss:",
    "frame-src 'self' https://www.youtube.com https://www.tiktok.com https://rumble.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),
  
  frameOptions: 'DENY',
  contentTypeOptions: 'nosniff',
  referrerPolicy: 'strict-origin-when-cross-origin',
  xssProtection: '1; mode=block',
  strictTransportSecurity: 'max-age=31536000; includeSubDomains; preload',
  
  permissionsPolicy: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
    'ambient-light-sensor=()',
    'autoplay=(self)',
    'encrypted-media=(self)',
    'fullscreen=(self)',
    'picture-in-picture=(self)'
  ].join(', ')
};

/**
 * Apply security headers to a response
 */
export function applySecurityHeaders(
  response: NextResponse, 
  config: SecurityConfig = DEFAULT_SECURITY_CONFIG
): NextResponse {
  if (config.contentSecurityPolicy) {
    response.headers.set('Content-Security-Policy', config.contentSecurityPolicy);
  }
  
  if (config.frameOptions) {
    response.headers.set('X-Frame-Options', config.frameOptions);
  }
  
  if (config.contentTypeOptions) {
    response.headers.set('X-Content-Type-Options', config.contentTypeOptions);
  }
  
  if (config.referrerPolicy) {
    response.headers.set('Referrer-Policy', config.referrerPolicy);
  }
  
  if (config.xssProtection) {
    response.headers.set('X-XSS-Protection', config.xssProtection);
  }
  
  if (config.strictTransportSecurity) {
    response.headers.set('Strict-Transport-Security', config.strictTransportSecurity);
  }
  
  if (config.permissionsPolicy) {
    response.headers.set('Permissions-Policy', config.permissionsPolicy);
  }

  // Additional security headers
  response.headers.set('X-DNS-Prefetch-Control', 'off');
  response.headers.set('X-Download-Options', 'noopen');
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');
  
  return response;
}

/**
 * Security middleware for API routes
 */
export function securityMiddleware(
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    // Execute the handler
    const response = await handler(req);
    
    // Apply security headers
    return applySecurityHeaders(response);
  };
}

/**
 * CSRF Token validation
 */
export class CSRFProtection {
  private static readonly CSRF_HEADER = 'x-csrf-token';
  private static readonly CSRF_COOKIE = 'csrf-token';
  
  static generateToken(): string {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }
  
  static validateToken(req: NextRequest): boolean {
    const headerToken = req.headers.get(this.CSRF_HEADER);
    const cookieToken = req.cookies.get(this.CSRF_COOKIE)?.value;
    
    if (!headerToken || !cookieToken) {
      return false;
    }
    
    return headerToken === cookieToken;
  }
  
  static middleware(
    handler: (req: NextRequest) => Promise<NextResponse>
  ) {
    return async (req: NextRequest): Promise<NextResponse> => {
      // Skip CSRF for GET, HEAD, OPTIONS
      if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
        return handler(req);
      }
      
      // Validate CSRF token for state-changing operations
      if (!this.validateToken(req)) {
        return NextResponse.json(
          { error: 'Invalid CSRF token' },
          { status: 403 }
        );
      }
      
      return handler(req);
    };
  }
}

/**
 * Rate limiting for security
 */
export class SecurityRateLimit {
  private static attempts = new Map<string, { count: number; resetTime: number }>();
  
  static isBlocked(identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(identifier);
    
    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + windowMs });
      return false;
    }
    
    if (attempt.count >= maxAttempts) {
      return true;
    }
    
    attempt.count++;
    return false;
  }
  
  static recordFailedAttempt(identifier: string): void {
    const now = Date.now();
    const attempt = this.attempts.get(identifier);
    
    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + 15 * 60 * 1000 });
    } else {
      attempt.count++;
    }
  }
  
  static reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
  
  static cleanup(): void {
    const now = Date.now();
    for (const [key, attempt] of this.attempts.entries()) {
      if (now > attempt.resetTime) {
        this.attempts.delete(key);
      }
    }
  }
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize HTML content to prevent XSS
   */
  static sanitizeHtml(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
  
  /**
   * Sanitize SQL input to prevent injection
   */
  static sanitizeSql(input: string): string {
    return input
      .replace(/['";\\]/g, '')
      .replace(/--/g, '')
      .replace(/\/\*/g, '')
      .replace(/\*\//g, '');
  }
  
  /**
   * Validate and sanitize URLs
   */
  static sanitizeUrl(url: string): string {
    try {
      const parsed = new URL(url);
      
      // Only allow specific protocols
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        throw new Error('Invalid protocol');
      }
      
      return parsed.toString();
    } catch {
      throw new Error('Invalid URL format');
    }
  }
  
  /**
   * Sanitize file paths to prevent directory traversal
   */
  static sanitizePath(path: string): string {
    return path
      .replace(/\.\./g, '')
      .replace(/[<>:"|?*]/g, '')
      .replace(/^\/+/, '')
      .trim();
  }
}

/**
 * Content Security Policy violation reporting
 */
export function handleCSPViolation(req: NextRequest): NextResponse {
  // Log CSP violations for monitoring
  console.warn('CSP Violation reported:', {
    userAgent: req.headers.get('user-agent'),
    ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
    timestamp: new Date().toISOString()
  });
  
  return NextResponse.json({ status: 'reported' }, { status: 204 });
}

/**
 * Security audit logger
 */
export class SecurityAuditLogger {
  static logSecurityEvent(event: {
    type: 'authentication' | 'authorization' | 'input_validation' | 'rate_limit' | 'csrf';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    userId?: string;
    ip?: string;
    userAgent?: string;
    metadata?: Record<string, unknown>;
  }): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      ...event
    };
    
    // In production, this would send to a security monitoring service
    console.log('SECURITY_EVENT:', JSON.stringify(logEntry));
    
    // For critical events, could trigger alerts
    if (event.severity === 'critical') {
      console.error('CRITICAL SECURITY EVENT:', logEntry);
    }
  }
}

/**
 * Secure session configuration
 */
export const SECURE_SESSION_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: 24 * 60 * 60, // 24 hours
  path: '/',
};

/**
 * Environment-specific security settings
 */
export function getSecurityConfig(): SecurityConfig {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // Relaxed CSP for development
    return {
      ...DEFAULT_SECURITY_CONFIG,
      contentSecurityPolicy: [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https: blob:",
        "connect-src 'self' ws: wss:",
        "frame-src 'self'"
      ].join('; ')
    };
  }
  
  return DEFAULT_SECURITY_CONFIG;
}