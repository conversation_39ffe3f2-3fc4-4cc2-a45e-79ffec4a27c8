/**
 * Token Refresh Utility
 *
 * Handles automatic token refresh before expiration to improve user experience
 * and reduce authentication interruptions.
 */

import { logger } from './logger';

export interface TokenRefreshConfig {
  refreshThreshold: number; // Minutes before expiration to trigger refresh
  maxRefreshAttempts: number;
  refreshWindow: number; // Minutes
}

export interface TokenData {
  accessToken: string;
  expiresAt: number;
  refreshToken?: string;
}

export class TokenRefreshManager {
  private static instance: TokenRefreshManager;
  private config: TokenRefreshConfig;
  private refreshPromises = new Map<string, Promise<TokenData>>();
  private refreshAttempts = new Map<string, number>();

  private defaultConfig: TokenRefreshConfig = {
    refreshThreshold: 5, // Refresh 5 minutes before expiration
    maxRefreshAttempts: 3,
    refreshWindow: 60, // 1 hour refresh window
  };

  private constructor(config?: Partial<TokenRefreshConfig>) {
    this.config = { ...this.defaultConfig, ...config };
  }

  static getInstance(config?: Partial<TokenRefreshConfig>): TokenRefreshManager {
    if (!TokenRefreshManager.instance) {
      TokenRefreshManager.instance = new TokenRefreshManager(config);
    }
    return TokenRefreshManager.instance;
  }

  /**
   * Check if token needs refresh
   */
  shouldRefreshToken(expiresAt: number, userId: string): boolean {
    const now = Date.now();
    const expiresAtMs = expiresAt * 1000; // Convert to milliseconds
    const refreshThresholdMs = this.config.refreshThreshold * 60 * 1000;

    const needsRefresh = (expiresAtMs - now) <= refreshThresholdMs;

    if (needsRefresh) {
      logger.debug('Token refresh needed', {
        userId,
        expiresAt,
        timeUntilExpiry: Math.floor((expiresAtMs - now) / 1000),
        refreshThreshold: this.config.refreshThreshold,
      });
    }

    return needsRefresh;
  }

  /**
   * Refresh token with deduplication to prevent multiple concurrent requests
   */
  async refreshToken(userId: string, currentToken: TokenData): Promise<TokenData | null> {
    // Check if refresh is already in progress
    if (this.refreshPromises.has(userId)) {
      logger.debug('Token refresh already in progress, waiting', { userId });
      return await this.refreshPromises.get(userId)!;
    }

    // Check refresh attempt limits
    const attempts = this.refreshAttempts.get(userId) || 0;
    if (attempts >= this.config.maxRefreshAttempts) {
      logger.warn('Max refresh attempts exceeded', { userId, attempts });
      return null;
    }

    // Create refresh promise
    const refreshPromise = this.performTokenRefresh(userId, currentToken);
    this.refreshPromises.set(userId, refreshPromise);

    try {
      const result = await refreshPromise;
      this.refreshAttempts.delete(userId); // Reset on success
      return result;
    } catch (error) {
      this.refreshAttempts.set(userId, attempts + 1);
      logger.error('Token refresh failed', { userId, attempts: attempts + 1 }, error as Error);
      return null;
    } finally {
      this.refreshPromises.delete(userId);
    }
  }

  /**
   * Perform the actual token refresh (placeholder for actual implementation)
   */
  private async performTokenRefresh(userId: string, currentToken: TokenData): Promise<TokenData> {
    // This is a placeholder implementation
    // In a real scenario, this would make an API call to refresh the token

    logger.info('Performing token refresh', { userId });

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // For now, return an extended token
    // In production, this would call the OAuth provider's refresh endpoint
    const newExpiresAt = Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60); // 30 days

    return {
      accessToken: currentToken.accessToken, // In reality, this would be a new token
      expiresAt: newExpiresAt,
      refreshToken: currentToken.refreshToken,
    };
  }

  /**
   * Clean up expired refresh attempts
   */
  cleanup(userId: string): void {
    this.refreshPromises.delete(userId);
    this.refreshAttempts.delete(userId);
  }

  /**
   * Get refresh status for debugging
   */
  getRefreshStatus(userId: string): { inProgress: boolean; attempts: number } {
    return {
      inProgress: this.refreshPromises.has(userId),
      attempts: this.refreshAttempts.get(userId) || 0,
    };
  }
}

// Export singleton instance
export const tokenRefreshManager = TokenRefreshManager.getInstance();