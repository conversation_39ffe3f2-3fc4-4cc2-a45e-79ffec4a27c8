import { redisClient, CACHE_KEYS, CACHE_TTL } from './redis';

export class CacheService {
  private async isRedisAvailable(): Promise<boolean> {
    return await redisClient.isAvailable();
  }

  // Generic cache get method
  async get<T>(key: string): Promise<T | null> {
    try {
      if (!(await this.isRedisAvailable())) {
        return null;
      }

      const client = await redisClient.getClient();
      const cached = await client.get(key);
      
      if (cached) {
        return JSON.parse(cached) as T;
      }
      
      return null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  // Generic cache set method
  async set(key: string, value: unknown, ttl?: number): Promise<void> {
    try {
      if (!(await this.isRedisAvailable())) {
        return;
      }

      const client = await redisClient.getClient();
      const serialized = JSON.stringify(value);
      
      if (ttl) {
        await client.setEx(key, ttl, serialized);
      } else {
        await client.set(key, serialized);
      }
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
    }
  }

  // Generic cache delete method
  async delete(key: string): Promise<void> {
    try {
      if (!(await this.isRedisAvailable())) {
        return;
      }

      const client = await redisClient.getClient();
      await client.del(key);
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
    }
  }

  // Delete multiple keys with pattern
  async deletePattern(pattern: string): Promise<void> {
    try {
      if (!(await this.isRedisAvailable())) {
        return;
      }

      const client = await redisClient.getClient();
      const keys = await client.keys(pattern);
      
      if (keys.length > 0) {
        await client.del(keys);
      }
    } catch (error) {
      console.error(`Cache delete pattern error for pattern ${pattern}:`, error);
    }
  }

  // Leaderboard caching methods
  async getLeaderboard() {
    return this.get(CACHE_KEYS.LEADERBOARD);
  }

  async setLeaderboard(data: unknown) {
      return this.set(CACHE_KEYS.LEADERBOARD, data, CACHE_TTL.LEADERBOARD);
  }

  async invalidateLeaderboard() {
    return this.delete(CACHE_KEYS.LEADERBOARD);
  }

  // Dashboard stats caching methods
  async getDashboardStats() {
    return this.get(CACHE_KEYS.DASHBOARD_STATS);
  }

  async setDashboardStats(data: unknown) {
      return this.set(CACHE_KEYS.DASHBOARD_STATS, data, CACHE_TTL.DASHBOARD_STATS);
  }

  async invalidateDashboardStats() {
    return this.delete(CACHE_KEYS.DASHBOARD_STATS);
  }

  // User stats caching methods
  async getUserStats(userId: string) {
    return this.get(CACHE_KEYS.USER_STATS(userId));
  }

  async setUserStats(userId: string, data: unknown) {
      return this.set(CACHE_KEYS.USER_STATS(userId), data, CACHE_TTL.USER_STATS);
  }

  async invalidateUserStats(userId: string) {
    return this.delete(CACHE_KEYS.USER_STATS(userId));
  }

  async invalidateAllUserStats() {
    return this.deletePattern('stats:user:*');
  }

  // Analytics caching methods
  async getAnalytics(days: number) {
    return this.get(CACHE_KEYS.ANALYTICS(days));
  }

  async setAnalytics(days: number, data: unknown) {
      return this.set(CACHE_KEYS.ANALYTICS(days), data, CACHE_TTL.ANALYTICS);
  }

  async invalidateAnalytics() {
    return this.deletePattern('analytics:*');
  }

  // Video caching methods
  async getNWAVideos(userId: string) {
    return this.get(CACHE_KEYS.NWA_VIDEOS(userId));
  }

  async setNWAVideos(userId: string, data: unknown) {
      return this.set(CACHE_KEYS.NWA_VIDEOS(userId), data, CACHE_TTL.VIDEOS);
  }

  async invalidateNWAVideos() {
    return this.deletePattern('videos:nwa:*');
  }

  async getUserVideoLinks() {
    return this.get(CACHE_KEYS.USER_VIDEO_LINKS);
  }

  async setUserVideoLinks(data: unknown) {
      return this.set(CACHE_KEYS.USER_VIDEO_LINKS, data, CACHE_TTL.VIDEOS);
  }

  async invalidateUserVideoLinks() {
    return this.delete(CACHE_KEYS.USER_VIDEO_LINKS);
  }


  // Cache invalidation strategies
  async invalidateVideoRelatedCaches() {
    await Promise.all([
      this.invalidateNWAVideos(),
      this.invalidateUserVideoLinks(),
      this.invalidateDashboardStats(),
    ]);
  }

  async invalidateUserRelatedCaches(userId?: string) {
    await Promise.all([
      this.invalidateLeaderboard(),
      this.invalidateDashboardStats(),
      userId ? this.invalidateUserStats(userId) : this.invalidateAllUserStats(),
    ]);
  }

  async invalidateEngagementRelatedCaches() {
    await Promise.all([
      this.invalidateLeaderboard(),
      this.invalidateAnalytics(),
      this.invalidateAllUserStats(),
      this.invalidateDashboardStats(),
    ]);
  }

  // Health check method
  async healthCheck(): Promise<{ redis: boolean; timestamp: string }> {
    const isAvailable = await this.isRedisAvailable();
    return {
      redis: isAvailable,
      timestamp: new Date().toISOString(),
    };
  }
}

// Singleton instance
export const cacheService = new CacheService();