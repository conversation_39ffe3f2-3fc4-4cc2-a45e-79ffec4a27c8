import { createClient, RedisClientType } from 'redis';

class RedisClient {
  private client: RedisClientType | null = null;
  private isConnecting = false;

  async getClient(): Promise<RedisClientType> {
    if (this.client && this.client.isOpen) {
      return this.client;
    }

    if (this.isConnecting) {
      // Wait for connection to complete
      while (this.isConnecting) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      if (this.client && this.client.isOpen) {
        return this.client;
      }
    }

    this.isConnecting = true;

    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      this.client = createClient({
        url: redisUrl,
        socket: {
          connectTimeout: 5000,
        },
      });

      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
      });

      this.client.on('connect', () => {
        console.log('Redis Client Connected');
      });

      this.client.on('disconnect', () => {
        console.log('Redis Client Disconnected');
      });

      await this.client.connect();
      this.isConnecting = false;
      return this.client;
    } catch (error) {
      this.isConnecting = false;
      console.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.client.isOpen) {
      await this.client.disconnect();
      this.client = null;
    }
  }

  // Check if Redis is available
  async isAvailable(): Promise<boolean> {
    try {
      const client = await this.getClient();
      await client.ping();
      return true;
    } catch (error) {
      console.warn('Redis is not available:', error);
      return false;
    }
  }
}

// Singleton instance
const redisClient = new RedisClient();

export { redisClient };

// Cache key constants
export const CACHE_KEYS = {
  LEADERBOARD: 'leaderboard:top50',
  DASHBOARD_STATS: 'stats:dashboard',
  USER_STATS: (userId: string) => `stats:user:${userId}`,
  ANALYTICS: (days: number) => `analytics:${days}days`,
  NWA_VIDEOS: (userId: string) => `videos:nwa:${userId}`,
  USER_VIDEO_LINKS: 'videos:user_links',
} as const;

// Cache TTL constants (in seconds)
export const CACHE_TTL = {
  LEADERBOARD: 300, // 5 minutes
  DASHBOARD_STATS: 180, // 3 minutes
  USER_STATS: 120, // 2 minutes
  ANALYTICS: 1800, // 30 minutes
  VIDEOS: 300, // 5 minutes
} as const;