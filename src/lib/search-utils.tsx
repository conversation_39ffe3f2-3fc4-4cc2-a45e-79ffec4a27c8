/**
 * Utility functions for search functionality
 */
import React from 'react';

/**
 * Highlights search terms in text by wrapping them with HTML elements
 * @param text - The text to highlight
 * @param searchQuery - The search query to highlight
 * @param className - CSS class to apply to highlighted text
 * @returns JSX element with highlighted text
 */
export function highlightSearchTerms(
  text: string, 
  searchQuery: string, 
  className: string = 'bg-yellow-200 text-yellow-900 px-1 rounded'
): React.ReactNode {
  if (!searchQuery || !text) {
    return text;
  }

  // Escape special regex characters in the search query
  const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  // Create regex for case-insensitive matching
  const regex = new RegExp(`(${escapedQuery})`, 'gi');
  
  // Split text by matches and create highlighted spans
  const parts = text.split(regex);
  
  return parts.map((part, index) => {
    if (regex.test(part)) {
      return (
        <span key={index} className={className}>
          {part}
        </span>
      );
    }
    return part;
  });
}

/**
 * Creates a React component for highlighted text
 * @param text - The text to highlight
 * @param searchQuery - The search query to highlight
 * @param className - CSS class to apply to highlighted text
 * @returns React component with highlighted text
 */
export function HighlightedText({ 
  text, 
  searchQuery, 
  className = 'bg-yellow-200 text-yellow-900 px-1 rounded' 
}: {
  text: string;
  searchQuery?: string;
  className?: string;
}) {
  if (!searchQuery || !text) {
    return <>{text}</>;
  }

  // Escape special regex characters in the search query
  const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  // Create regex for case-insensitive matching
  const regex = new RegExp(`(${escapedQuery})`, 'gi');
  
  // Split text by matches and create highlighted spans
  const parts = text.split(regex);
  
  return (
    <>
      {parts.map((part, index) => {
        if (regex.test(part)) {
          return (
            <span key={index} className={className}>
              {part}
            </span>
          );
        }
        return part;
      })}
    </>
  );
}

/**
 * Debounce function for search input
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Filters videos based on search criteria
 * @param videos - Array of videos to filter
 * @param filters - Search filters to apply
 * @returns Filtered array of videos
 */
export function filterVideos<T extends { title: string; description?: string | null }>(
  videos: T[],
  filters: {
    query?: string;
    type?: 'nwa' | 'user' | 'all';
    platform?: string;
    featured?: boolean | null;
  }
): T[] {
  let filtered = [...videos];

  // Apply text search filter
  if (filters.query) {
    const query = filters.query.toLowerCase().trim();
    filtered = filtered.filter(video => 
      video.title.toLowerCase().includes(query) ||
      (video.description && video.description.toLowerCase().includes(query))
    );
  }

  return filtered;
}