// src/middleware/__tests__/authMiddleware.test.ts

import { NextRequest, NextResponse } from 'next/server';

// Mock the security headers functions
jest.mock('../../lib/security-headers', () => ({
  applySecurityHeaders: jest.fn(),
  getSecurityConfig: jest.fn().mockReturnValue({}),
  CSRFProtection: {
    validateToken: jest.fn().mockReturnValue(true)
  }
}));

// Mock next-auth/jwt
jest.mock('next-auth/jwt', () => ({
  getToken: jest.fn()
}));

// Mock NextResponse
const mockNextResponse = {
  next: jest.fn().mockReturnValue({
    headers: {
      set: jest.fn()
    }
  }),
  redirect: jest.fn().mockReturnValue({
    headers: {
      set: jest.fn()
    }
  }),
  json: jest.fn()
};

jest.mock('next/server', () => ({
  NextResponse: mockNextResponse,
  NextRequest: jest.fn()
}));

// Import after mocks are set up
const { middleware } = require('../../middleware');
const { getToken } = require('next-auth/jwt');

describe('Authentication Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Unauthenticated User Redirect', () => {
    it('should redirect unauthenticated users to signin page when accessing protected routes', async () => {
      // Mock getToken to return null (unauthenticated)
      (getToken as jest.Mock).mockResolvedValue(null);

      // Mock request for a protected route
      const mockRequest = {
        nextUrl: {
          pathname: '/dashboard',
          clone: jest.fn().mockReturnThis()
        },
        headers: {
          get: jest.fn().mockReturnValue(null)
        },
        cookies: {
          get: jest.fn().mockReturnValue(undefined)
        },
        url: 'http://localhost:3000'
      } as unknown as NextRequest;

      // Mock NextResponse.next to return a response object
      const mockResponse = {
        headers: {
          set: jest.fn()
        }
      };
      mockNextResponse.next.mockReturnValueOnce(mockResponse);
      mockNextResponse.redirect.mockReturnValueOnce(mockResponse);

      const response = await middleware(mockRequest);

      // Should redirect to signin page
      expect(mockNextResponse.redirect).toHaveBeenCalled();
    });

    it('should preserve callbackUrl parameter when redirecting', async () => {
      // Mock getToken to return null (unauthenticated)
      (getToken as jest.Mock).mockResolvedValue(null);

      const mockRequest = {
        nextUrl: {
          pathname: '/dashboard',
          searchParams: new URLSearchParams(),
          clone: jest.fn().mockReturnThis()
        },
        headers: {
          get: jest.fn().mockReturnValue(null)
        },
        cookies: {
          get: jest.fn().mockReturnValue(undefined)
        },
        url: 'http://localhost:3000'
      } as unknown as NextRequest;

      const mockResponse = {
        headers: {
          set: jest.fn()
        }
      };
      mockNextResponse.next.mockReturnValueOnce(mockResponse);
      mockNextResponse.redirect.mockReturnValueOnce(mockResponse);

      const response = await middleware(mockRequest);

      // Should redirect with callbackUrl parameter
      expect(mockNextResponse.redirect).toHaveBeenCalledWith(
        expect.objectContaining({
          href: expect.stringContaining('/api/auth/signin/member-portal')
        })
      );
      
      // Verify that the callbackUrl parameter is set correctly
      const redirectCall = (mockNextResponse.redirect as jest.Mock).mock.calls[0][0];
      expect(redirectCall.searchParams.get('callbackUrl')).toBe('/dashboard');
    });
    
    it('should preserve callbackUrl parameter with query parameters', async () => {
      // Mock getToken to return null (unauthenticated)
      (getToken as jest.Mock).mockResolvedValue(null);

      const mockRequest = {
        nextUrl: {
          pathname: '/videos/123',
          searchParams: new URLSearchParams('category=gaming&sort=popular'),
          clone: jest.fn().mockReturnThis()
        },
        headers: {
          get: jest.fn().mockReturnValue(null)
        },
        cookies: {
          get: jest.fn().mockReturnValue(undefined)
        },
        url: 'http://localhost:3000'
      } as unknown as NextRequest;

      const mockResponse = {
        headers: {
          set: jest.fn()
        }
      };
      mockNextResponse.next.mockReturnValueOnce(mockResponse);
      mockNextResponse.redirect.mockReturnValueOnce(mockResponse);

      const response = await middleware(mockRequest);

      // Should redirect with callbackUrl parameter including query parameters
      const redirectCall = (mockNextResponse.redirect as jest.Mock).mock.calls[0][0];
      expect(redirectCall.searchParams.get('callbackUrl')).toBe('/videos/123');
    });
  });

  describe('Authenticated User Access', () => {
    it('should allow authenticated users to access protected routes', async () => {
      // Mock getToken to return a token (authenticated)
      (getToken as jest.Mock).mockResolvedValue({ id: 'user1', name: 'Test User' });

      const mockRequest = {
        nextUrl: {
          pathname: '/dashboard',
          clone: jest.fn().mockReturnThis()
        },
        headers: {
          get: jest.fn().mockReturnValue(null)
        },
        cookies: {
          get: jest.fn().mockReturnValue({
            name: 'next-auth.session-token',
            value: 'valid-session-token'
          })
        },
        url: 'http://localhost:3000'
      } as unknown as NextRequest;

      const mockResponse = {
        headers: {
          set: jest.fn()
        }
      };
      mockNextResponse.next.mockReturnValueOnce(mockResponse);

      const response = await middleware(mockRequest);

      expect(mockNextResponse.next).toHaveBeenCalled();
    });
  });

  describe('Public Route Access', () => {
    it('should allow access to public routes without authentication', async () => {
      // Mock getToken to return null (unauthenticated)
      (getToken as jest.Mock).mockResolvedValue(null);

      const publicRoutes = ['/', '/auth/signin', '/auth/error', '/api/auth/signin/member-portal'];

      for (const route of publicRoutes) {
        const mockRequest = {
          nextUrl: {
            pathname: route,
            clone: jest.fn().mockReturnThis()
          },
          headers: {
            get: jest.fn().mockReturnValue(null)
          },
          cookies: {
            get: jest.fn().mockReturnValue(undefined)
          },
          url: 'http://localhost:3000'
        } as unknown as NextRequest;

        const mockResponse = {
          headers: {
            set: jest.fn()
          }
        };
        mockNextResponse.next.mockReturnValueOnce(mockResponse);

        const response = await middleware(mockRequest);

        expect(mockNextResponse.next).toHaveBeenCalled();
      }
    });
  });

  describe('Security Headers', () => {
    it('should apply security headers to all responses', async () => {
      // Mock getToken to return a token (authenticated)
      (getToken as jest.Mock).mockResolvedValue({ id: 'user1', name: 'Test User' });

      const mockRequest = {
        nextUrl: {
          pathname: '/dashboard',
          clone: jest.fn().mockReturnThis()
        },
        headers: {
          get: jest.fn().mockReturnValue(null)
        },
        cookies: {
          get: jest.fn().mockReturnValue(undefined)
        },
        url: 'http://localhost:3000'
      } as unknown as NextRequest;

      const mockResponse = {
        headers: {
          set: jest.fn()
        }
      };
      mockNextResponse.next.mockReturnValueOnce(mockResponse);

      const response = await middleware(mockRequest);

      // Import the actual mock to check if security headers were applied
      const { applySecurityHeaders } = require('../../lib/security-headers');
      expect(applySecurityHeaders).toHaveBeenCalled();
    });
  });
});