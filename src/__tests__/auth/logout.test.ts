// src/__tests__/auth/logout.test.ts

import { createAuthOptions } from '@/lib/auth-config';
import { sessionManager } from '@/lib/session-manager';
import { tokenRefreshManager } from '@/lib/token-refresh';
import { RemoteOAuthService } from '@/services/RemoteOAuthService';

// Mock the dependencies
jest.mock('@/lib/session-manager');
jest.mock('@/lib/token-refresh');
jest.mock('@/lib/logger');
jest.mock('@/lib/prisma');
jest.mock('@/services/RemoteOAuthService');

describe('Logout Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up required environment variables
    process.env.NEXTAUTH_SECRET = 'test-secret-key-for-jwt-that-is-long-enough-for-nextauth-requirements';
    process.env.MEMBER_PORTAL_URL = 'http://localhost:3001';
    process.env.CLIENT_ID = 'test-client-id';
    process.env.CLIENT_SECRET = 'test-client-secret';
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('NextAuth Configuration', () => {
    it('should have signOut event handler configured', () => {
      const authOptions = createAuthOptions();
      
      expect(authOptions.events).toBeDefined();
      expect(authOptions.events?.signOut).toBeDefined();
      expect(typeof authOptions.events?.signOut).toBe('function');
    });

    it('should not have custom signOut page configured', () => {
      const authOptions = createAuthOptions();
      
      // Should not have custom signOut page since we removed it
      expect(authOptions.pages?.signOut).toBeUndefined();
      
      // Should still have error page
      expect(authOptions.pages?.error).toBe('/auth/error');
    });

    it('should use JWT strategy', () => {
      const authOptions = createAuthOptions();
      
      expect(authOptions.session?.strategy).toBe('jwt');
    });
  });

  describe('SignOut Event Handler', () => {
    it('should clean up custom session manager on signOut', async () => {
      const authOptions = createAuthOptions();
      const signOutHandler = authOptions.events?.signOut;
      
      // Mock session manager methods
      const mockDestroyUserSessions = jest.fn();
      (sessionManager.destroyUserSessions as jest.Mock) = mockDestroyUserSessions;
      
      // Mock token refresh manager cleanup
      const mockCleanup = jest.fn();
      (tokenRefreshManager.cleanup as jest.Mock) = mockCleanup;
      
      // Mock remote OAuth service
      const mockRemoteLogout = jest.fn().mockResolvedValue({ success: true });
      const mockIsConfigured = jest.fn().mockReturnValue(true);
      (RemoteOAuthService.logout as jest.Mock) = mockRemoteLogout;
      (RemoteOAuthService.isConfigured as jest.Mock) = mockIsConfigured;

      // Mock signOut message
      const mockMessage = {
        token: {
          email: '<EMAIL>',
          sub: 'user-123',
          id: 'user-123',
          accessToken: 'mock-access-token'
        }
      };

      if (signOutHandler) {
        await signOutHandler(mockMessage);
      }

      // Verify remote logout was called first
      expect(mockRemoteLogout).toHaveBeenCalledWith({
        accessToken: 'mock-access-token',
        userId: 'user-123',
        email: '<EMAIL>'
      });

      // Verify local cleanup methods were called
      expect(mockDestroyUserSessions).toHaveBeenCalledWith('user-123');
      expect(mockCleanup).toHaveBeenCalledWith('user-123');
    });

    it('should handle signOut without user ID gracefully', async () => {
      const authOptions = createAuthOptions();
      const signOutHandler = authOptions.events?.signOut;
      
      // Mock session manager methods
      const mockDestroyUserSessions = jest.fn();
      (sessionManager.destroyUserSessions as jest.Mock) = mockDestroyUserSessions;
      
      // Mock signOut message without user ID
      const mockMessage = {
        token: {
          email: '<EMAIL>'
          // No sub or id
        }
      };
      
      if (signOutHandler) {
        await signOutHandler(mockMessage);
      }
      
      // Should not call cleanup methods without user ID
      expect(mockDestroyUserSessions).not.toHaveBeenCalled();
    });

    it('should handle signOut without token gracefully', async () => {
      const authOptions = createAuthOptions();
      const signOutHandler = authOptions.events?.signOut;
      
      // Mock session manager methods
      const mockDestroyUserSessions = jest.fn();
      (sessionManager.destroyUserSessions as jest.Mock) = mockDestroyUserSessions;
      
      // Mock signOut message without token
      const mockMessage = {};
      
      if (signOutHandler) {
        await signOutHandler(mockMessage);
      }
      
      // Should not call cleanup methods without token
      expect(mockDestroyUserSessions).not.toHaveBeenCalled();
    });

    it('should handle remote logout failure gracefully', async () => {
      const authOptions = createAuthOptions();
      const signOutHandler = authOptions.events?.signOut;

      // Mock session manager methods
      const mockDestroyUserSessions = jest.fn();
      (sessionManager.destroyUserSessions as jest.Mock) = mockDestroyUserSessions;

      // Mock remote OAuth service to fail
      const mockRemoteLogout = jest.fn().mockResolvedValue({
        success: false,
        error: 'Remote server unavailable'
      });
      const mockIsConfigured = jest.fn().mockReturnValue(true);
      (RemoteOAuthService.logout as jest.Mock) = mockRemoteLogout;
      (RemoteOAuthService.isConfigured as jest.Mock) = mockIsConfigured;

      // Mock signOut message
      const mockMessage = {
        token: {
          email: '<EMAIL>',
          sub: 'user-123',
          accessToken: 'mock-access-token'
        }
      };

      if (signOutHandler) {
        await signOutHandler(mockMessage);
      }

      // Should still proceed with local cleanup even if remote logout fails
      expect(mockRemoteLogout).toHaveBeenCalled();
      expect(mockDestroyUserSessions).toHaveBeenCalledWith('user-123');
    });

    it('should skip remote logout when not configured', async () => {
      const authOptions = createAuthOptions();
      const signOutHandler = authOptions.events?.signOut;

      // Mock session manager methods
      const mockDestroyUserSessions = jest.fn();
      (sessionManager.destroyUserSessions as jest.Mock) = mockDestroyUserSessions;

      // Mock remote OAuth service as not configured
      const mockRemoteLogout = jest.fn();
      const mockIsConfigured = jest.fn().mockReturnValue(false);
      (RemoteOAuthService.logout as jest.Mock) = mockRemoteLogout;
      (RemoteOAuthService.isConfigured as jest.Mock) = mockIsConfigured;

      // Mock signOut message
      const mockMessage = {
        token: {
          email: '<EMAIL>',
          sub: 'user-123',
          accessToken: 'mock-access-token'
        }
      };

      if (signOutHandler) {
        await signOutHandler(mockMessage);
      }

      // Should skip remote logout and proceed with local cleanup
      expect(mockRemoteLogout).not.toHaveBeenCalled();
      expect(mockDestroyUserSessions).toHaveBeenCalledWith('user-123');
    });
  });

  describe('OAuth Provider Configuration', () => {
    it('should have member-portal OAuth provider configured', () => {
      const authOptions = createAuthOptions();
      
      const memberPortalProvider = authOptions.providers.find(
        (provider: any) => provider.id === 'member-portal'
      );
      
      expect(memberPortalProvider).toBeDefined();
      expect(memberPortalProvider.type).toBe('oauth');
      expect(memberPortalProvider.clientId).toBe(process.env.CLIENT_ID);
      expect(memberPortalProvider.clientSecret).toBe(process.env.CLIENT_SECRET);
    });

    it('should have correct OAuth endpoints configured', () => {
      const authOptions = createAuthOptions();
      
      const memberPortalProvider = authOptions.providers.find(
        (provider: any) => provider.id === 'member-portal'
      );
      
      expect(memberPortalProvider.authorization.url).toBe(
        `${process.env.MEMBER_PORTAL_URL}/api/oauth/authorize`
      );
      expect(memberPortalProvider.token).toBe(
        `${process.env.MEMBER_PORTAL_URL}/api/oauth/token`
      );
      expect(memberPortalProvider.userinfo).toBe(
        `${process.env.MEMBER_PORTAL_URL}/api/oauth/userinfo`
      );
    });
  });
});
