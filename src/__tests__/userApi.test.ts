// src/__tests__/userApi.test.ts

import { UserService } from '../services/UserService';
import { Role, NotificationPreference } from '@/generated/prisma/client';

jest.mock('../services/UserService');

describe('User API Endpoints', () => {
  const mockUser = {
    id: 'user1',
    name: 'Test User',
    email: '<EMAIL>',
    role: Role.USER,
    notificationPreference: NotificationPreference.ALL,
    createdAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('POST /api/admin/users should create user', async () => {
    (UserService.createUser as jest.Mock).mockResolvedValue(mockUser);
    // Simulate API handler call here (replace with actual handler import)
    // Example: const result = await handler(req, res)
    expect(await UserService.createUser({ name: 'Test User', email: '<EMAIL>', password: 'pass', role: Role.USER }))
      .toMatchObject({ name: 'Test User', email: '<EMAIL>', role: Role.USER });
  });

  it('GET /api/admin/users should list users', async () => {
    (UserService.getAllUsers as jest.Mock).mockResolvedValue([mockUser]);
    expect(await UserService.getAllUsers()).toEqual([mockUser]);
  });

  it('PUT /api/admin/users/:id should update user', async () => {
    (UserService.updateUser as jest.Mock).mockResolvedValue({ ...mockUser, name: 'Updated User' });
    expect(await UserService.updateUser('user1', { name: 'Updated User' })).toMatchObject({ name: 'Updated User' });
  });

  it('PATCH /api/admin/users/:id should deactivate user', async () => {
    (UserService.deactivateUser as jest.Mock).mockResolvedValue({ ...mockUser, notificationPreference: NotificationPreference.NWA_ONLY });
    expect(await UserService.deactivateUser('user1')).toMatchObject({ notificationPreference: NotificationPreference.NWA_ONLY });
  });

  it('DELETE /api/admin/users/:id should delete user', async () => {
    (UserService.deleteUser as jest.Mock).mockResolvedValue(mockUser);
    expect(await UserService.deleteUser('user1')).toMatchObject({ id: 'user1' });
  });
});