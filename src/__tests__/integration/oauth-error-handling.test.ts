/**
 * OAuth Error Handling Tests
 *
 * Tests error scenarios and edge cases in the OAuth authentication flow
 * that we have direct control over.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock Next.js server components
jest.mock('next/server', () => ({
  NextRequest: jest.fn(),
  NextResponse: {
    next: jest.fn(() => ({ headers: new Map() })),
    json: jest.fn(),
    redirect: jest.fn(),
  },
}));

// Import after mocks are set up
import { CSRFProtection } from '@/lib/security-headers';
import { SecurityRateLimit } from '@/lib/rate-limit';

describe('OAuth Error Handling Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment variables for testing
    process.env.NEXTAUTH_URL = 'http://localhost:3002';
    process.env.MEMBER_PORTAL_URL = 'http://localhost:3001';
    process.env.CLIENT_ID = 'nwapromote-client-local';
    process.env.CLIENT_SECRET = 'test-secret';
    process.env.NEXTAUTH_SECRET = 'test-secret-key-for-jwt-that-is-long-enough-for-nextauth-requirements';
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('CSRF Protection Error Handling', () => {
    it('should generate valid CSRF tokens', () => {
      const token1 = CSRFProtection.generateToken();
      const token2 = CSRFProtection.generateToken();

      expect(token1).toBeDefined();
      expect(token2).toBeDefined();
      expect(typeof token1).toBe('string');
      expect(typeof token2).toBe('string');
      expect(token1.length).toBeGreaterThan(0);
      expect(token2.length).toBeGreaterThan(0);
      // Tokens should be unique
      expect(token1).not.toBe(token2);
    });

    it('should validate matching CSRF tokens', () => {
      const token = CSRFProtection.generateToken();

      // Mock request with matching token
      const mockRequest = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'x-csrf-token') return token;
            return null;
          })
        },
        cookies: {
          get: jest.fn(() => ({ value: token }))
        }
      };

      const isValid = CSRFProtection.validateToken(mockRequest as any);
      expect(isValid).toBe(true);
    });

    it('should reject non-matching CSRF tokens', () => {
      const token1 = CSRFProtection.generateToken();
      const token2 = CSRFProtection.generateToken();

      // Mock request with non-matching tokens
      const mockRequest = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'x-csrf-token') return token1;
            return null;
          })
        },
        cookies: {
          get: jest.fn(() => ({ value: token2 }))
        }
      };

      const isValid = CSRFProtection.validateToken(mockRequest as any);
      expect(isValid).toBe(false);
    });

    it('should reject missing CSRF tokens', () => {
      // Mock request with no tokens
      const mockRequest = {
        headers: {
          get: jest.fn(() => null)
        },
        cookies: {
          get: jest.fn(() => null)
        }
      };

      const isValid = CSRFProtection.validateToken(mockRequest as any);
      expect(isValid).toBe(false);
    });
  });

  describe('Rate Limiting Error Handling', () => {
    it('should allow requests within rate limit', () => {
      const identifier = 'test-user';

      // First request should be allowed
      const isBlocked1 = SecurityRateLimit.isBlocked(identifier, 3, 60000);
      expect(isBlocked1).toBe(false);

      // Second request should be allowed
      const isBlocked2 = SecurityRateLimit.isBlocked(identifier, 3, 60000);
      expect(isBlocked2).toBe(false);

      // Third request should be allowed
      const isBlocked3 = SecurityRateLimit.isBlocked(identifier, 3, 60000);
      expect(isBlocked3).toBe(false);
    });

    it('should block requests exceeding rate limit', () => {
      const identifier = 'test-user';

      // Use up all allowed requests
      for (let i = 0; i < 3; i++) {
        SecurityRateLimit.isBlocked(identifier, 3, 60000);
      }

      // Fourth request should be blocked
      const isBlocked = SecurityRateLimit.isBlocked(identifier, 3, 60000);
      expect(isBlocked).toBe(true);
    });

    it('should handle different identifiers independently', () => {
      const identifier1 = 'user1';
      const identifier2 = 'user2';

      // Use up all requests for user1
      for (let i = 0; i < 3; i++) {
        SecurityRateLimit.isBlocked(identifier1, 3, 60000);
      }

      // user1 should be blocked
      const user1Blocked = SecurityRateLimit.isBlocked(identifier1, 3, 60000);
      expect(user1Blocked).toBe(true);

      // user2 should still be allowed
      const user2Blocked = SecurityRateLimit.isBlocked(identifier2, 3, 60000);
      expect(user2Blocked).toBe(false);
    });
  });

  describe('Signin Page Error Handling', () => {
    it('should handle various OAuth error codes correctly', () => {
      // Test error message mapping
      const errorMappings = {
        'OAuthAccountNotLinked': 'This account is already linked with another profile.',
        'OAuthCallback': 'Authentication failed. Please try again.',
        'Callback': 'Authentication callback failed. Please try again.',
        'TokenExchangeFailed': 'Failed to exchange token. Please try again.',
        'UserInfoFailed': 'Failed to retrieve user info. Please try again.',
        'UserCreationFailed': 'Failed to create user account. Please contact support.',
        'NetworkError': 'Network error occurred. Please check your connection and try again.',
        'OAuthInitiationFailed': 'Failed to initiate OAuth flow. Please try again.',
        'ConfigurationError': 'Service configuration error. Please contact support.',
        'server_error': 'Server error occurred during authentication. Please try again.',
        'unknown_error': 'An unexpected error occurred. Please try again.',
      };

      // Verify all error mappings are present
      Object.entries(errorMappings).forEach(([errorCode, expectedMessage]) => {
        expect(typeof expectedMessage).toBe('string');
        expect(expectedMessage.length).toBeGreaterThan(0);
      });
    });

    it('should handle malformed callback URLs gracefully', () => {
      // Test URL construction with various malformed inputs
      const testCases = [
        { input: null, shouldContain: '/dashboard' },
        { input: undefined, shouldContain: '/dashboard' },
        { input: '', shouldContain: '/dashboard' },
        { input: '/valid-path', shouldContain: '/valid-path' },
        { input: 'invalid-url', shouldContain: '/dashboard' },
        { input: 'http://external.com', shouldContain: '/dashboard' },
      ];

      testCases.forEach(({ input, shouldContain }) => {
        // This tests the logic that should be in the signin page
        const callbackUrl = input && input.startsWith('/') ? input : '/dashboard';
        expect(callbackUrl).toContain(shouldContain);
      });
    });
  });

  describe('Security Utilities Error Handling', () => {
    it('should handle malformed headers gracefully', () => {
      // Test that security utilities handle malformed input
      const malformedHeaders = [
        '',
        null,
        undefined,
        'invalid-header',
        'x-forwarded-for: invalid-ip',
        'x-real-ip: 192.168.1.999', // Invalid IP
      ];

      malformedHeaders.forEach(header => {
        // Rate limiting should handle malformed headers gracefully
        const identifier = header || 'unknown';
        const isBlocked = SecurityRateLimit.isBlocked(identifier, 5, 60000);
        expect(typeof isBlocked).toBe('boolean');
      });
    });

    it('should handle edge cases in CSRF token generation', () => {
      // Test multiple token generations for uniqueness
      const tokens = new Set();
      for (let i = 0; i < 100; i++) {
        const token = CSRFProtection.generateToken();
        expect(token).toBeDefined();
        expect(typeof token).toBe('string');
        expect(token.length).toBeGreaterThan(0);
        tokens.add(token);
      }

      // All tokens should be unique
      expect(tokens.size).toBe(100);
    });
  });
});