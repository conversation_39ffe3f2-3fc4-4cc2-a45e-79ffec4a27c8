// src/__tests__/userService.test.ts

import { UserService } from '../services/UserService';
import { prisma } from '@/lib/prisma';
import { Role, NotificationPreference } from '@/generated/prisma/client';

jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    }
  }
}));

describe('UserService', () => {
  const mockUser = {
    id: 'user1',
    name: 'Test User',
    email: '<EMAIL>',
    role: Role.USER,
    notificationPreference: NotificationPreference.ALL,
    createdAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a user', async () => {
    (prisma.user.create as jest.Mock).mockResolvedValue(mockUser);
    const result = await UserService.createUser({ name: 'Test User', email: '<EMAIL>', password: 'pass', role: Role.USER });
    expect(result).toMatchObject({ name: 'Test User', email: '<EMAIL>', role: Role.USER });
  });

  it('should update user details', async () => {
    (prisma.user.update as jest.Mock).mockResolvedValue({ ...mockUser, name: 'Updated User' });
    const result = await UserService.updateUser('user1', { name: 'Updated User' });
    expect(result.name).toBe('Updated User');
  });

  it('should deactivate user', async () => {
    (prisma.user.update as jest.Mock).mockResolvedValue({ ...mockUser, notificationPreference: NotificationPreference.NWA_ONLY });
    const result = await UserService.deactivateUser('user1');
    expect(result.notificationPreference).toBe(NotificationPreference.NWA_ONLY);
  });

  it('should delete user', async () => {
    (prisma.user.delete as jest.Mock).mockResolvedValue(mockUser);
    const result = await UserService.deleteUser('user1');
    expect(result.id).toBe('user1');
  });
});