// src/services/RemoteOAuthService.ts

import { logger } from '@/lib/logger';

export interface RemoteLogoutOptions {
  accessToken?: string;
  userId?: string;
  email?: string;
}

export interface RemoteLogoutResult {
  success: boolean;
  error?: string;
}

/**
 * Service for handling remote OAuth operations with the member portal
 */
export class RemoteOAuthService {
  private static readonly MEMBER_PORTAL_URL = process.env.MEMBER_PORTAL_URL;
  private static readonly CLIENT_ID = process.env.CLIENT_ID;
  private static readonly CLIENT_SECRET = process.env.CLIENT_SECRET;

  /**
   * Revoke OAuth tokens and logout from the remote member portal
   */
  static async logout(options: RemoteLogoutOptions): Promise<RemoteLogoutResult> {
    if (!this.MEMBER_PORTAL_URL) {
      logger.error('MEMBER_PORTAL_URL not configured for remote logout');
      return { success: false, error: 'Member portal URL not configured' };
    }

    const { accessToken, userId, email } = options;

    try {
      // Use the single comprehensive logout endpoint
      const logoutResult = await this.callLogoutEndpoint(options);
      
      if (logoutResult.success) {
        logger.info('Remote logout successful', { userId, email });
        return logoutResult;
      }

      // If logout fails, log but don't block local logout
      logger.warn('Remote logout failed, proceeding with local logout only', {
        userId,
        email,
        accessToken: !!accessToken
      });

      return { success: false, error: 'Remote logout failed' };

    } catch (error) {
      logger.error('Remote logout error', { userId, email }, error as Error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Call member portal logout endpoint
   */
  private static async callLogoutEndpoint(options: RemoteLogoutOptions): Promise<RemoteLogoutResult> {
    try {
      const { accessToken, userId } = options;
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // Include access token in Authorization header if available
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${this.MEMBER_PORTAL_URL}/api/oauth/logout`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          client_id: this.CLIENT_ID,
          user_id: userId,
          access_token: accessToken
        })
      });

      if (response.ok) {
        logger.info('Remote logout successful via /api/oauth/logout');
        return { success: true };
      }

      logger.warn('Remote logout failed', { 
        status: response.status, 
        statusText: response.statusText 
      });
      return { success: false, error: `Remote logout failed: ${response.status}` };

    } catch (error) {
      logger.error('Remote logout request failed', {}, error as Error);
      return { success: false, error: 'Remote logout request failed' };
    }
  }

  /**
   * Check if remote logout is properly configured
   */
  static isConfigured(): boolean {
    return !!(this.MEMBER_PORTAL_URL && this.CLIENT_ID && this.CLIENT_SECRET);
  }
}
