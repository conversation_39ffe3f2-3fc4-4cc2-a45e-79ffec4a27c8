// src/services/UserService.ts

import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { Role, NotificationPreference } from '@/generated/prisma';

export class UserService {
  static async createUser({ name, email, password, role }: { name: string; email: string; password: string; role: Role }) {
    const hashedPassword = await bcrypt.hash(password, 10);
    return prisma.user.create({
      data: { name, email, password: hashedPassword, role }
    });
  }

  static async updateUser(
    id: string,
    updates: Partial<{ name: string; email: string; role: Role; password: string }>
  ) {
    const updateData = { ...updates };
    if (updates.password) {
      updateData.password = await bcrypt.hash(updates.password, 10);
    }
    return prisma.user.update({
      where: { id },
      data: updateData
    });
  }

  static async getAllUsers() {
    return prisma.user.findMany();
  }

  static async deactivateUser(id: string) {
    // Use notificationPreference as a proxy for deactivation (set to NWA_ONLY)
    return prisma.user.update({
      where: { id },
      data: { notificationPreference: NotificationPreference.NWA_ONLY }
    });
  }

  static async deleteUser(id: string) {
    return prisma.user.delete({
      where: { id }
    });
  }
}