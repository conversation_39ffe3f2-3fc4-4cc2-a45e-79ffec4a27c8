import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';

export class JwtValidationService {
  static async validateToken(request: NextRequest) {
    try {
      const secret = process.env.NEXTAUTH_SECRET;

      if (!secret) {
        throw new Error('NEXTAUTH_SECRET is not configured');
      }

      const token = await getToken({
        req: request,
        secret: secret,
        secureCookie: process.env.NODE_ENV === 'production',
      });

      if (!token) {
        return { valid: false, error: 'No token found' };
      }

      // Check if token is expired
      if (token.exp && typeof token.exp === 'number' && Date.now() >= token.exp * 1000) {
        return { valid: false, error: 'Token expired' };
      }

      return {
        valid: true,
        token,
        user: {
          id: token.id as string,
          email: token.email as string,
          name: token.name as string,
          role: token.role as string,
        }
      };
    } catch (error) {
      console.error('JWT validation error:', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  static async debugToken(request: NextRequest) {
    const secret = process.env.NEXTAUTH_SECRET;
    console.log('JWT Debug Info:');
    console.log('- Secret configured:', !!secret);
    console.log('- Secret length:', secret?.length);
    console.log('- Environment:', process.env.NODE_ENV);

    try {
      const token = await getToken({
        req: request,
        secret: secret,
      });

      console.log('- Token found:', !!token);
      if (token) {
        console.log('- Token keys:', Object.keys(token));
        console.log('- Token exp:', token.exp);
        console.log('- Current time:', Date.now() / 1000);
        console.log('- Token expired:', token.exp && typeof token.exp === 'number' ? Date.now() >= token.exp * 1000 : 'No exp');
      }
    } catch (error) {
      console.error('- Token parsing error:', error);
    }
  }
}