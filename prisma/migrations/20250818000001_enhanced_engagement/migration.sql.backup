-- Enhanced engagement system migration

-- Add confirmation tracking to UserEngagement
ALTER TABLE `UserEngagement` 
ADD COLUMN `confirmed` BOOLEAN NULL,
ADD COLUMN `confirmedAt` DATETIME(3) NULL;

-- Create UserAchievement table
CREATE TABLE `UserAchievement` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `achievementId` VARCHAR(191) NOT NULL,
    `earnedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `UserAchievement_userId_achievementId_key` (`userId`, `achievementId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create UserEngagementStreak table
CREATE TABLE `UserEngagementStreak` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `currentStreak` INTEGER NOT NULL DEFAULT 0,
    `longestStreak` INTEGER NOT NULL DEFAULT 0,
    `lastEngagementDate` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserEngagementStreak_userId_key` (`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add foreign key constraints
ALTER TABLE `UserAchievement` ADD CONSTRAINT `UserAchievement_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `UserEngagementStreak` ADD CONSTRAINT `UserEngagementStreak_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX `UserEngagement_userId_confirmed_idx` ON `UserEngagement`(`userId`, `confirmed`);
CREATE INDEX `UserEngagement_confirmedAt_idx` ON `UserEngagement`(`confirmedAt`);
CREATE INDEX `UserAchievement_userId_idx` ON `UserAchievement`(`userId`);
CREATE INDEX `UserAchievement_achievementId_idx` ON `UserAchievement`(`achievementId`);
CREATE INDEX `UserEngagementStreak_userId_idx` ON `UserEngagementStreak`(`userId`);
