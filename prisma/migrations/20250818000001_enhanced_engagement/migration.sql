-- Enhanced engagement system migration

-- Add confirmation tracking to UserEngagement
ALTER TABLE "UserEngagement" 
ADD COLUMN "confirmed" BOOLEAN,
ADD COLUMN "confirmedAt" TIMESTAMP(3);

-- Create UserAchievement table
CREATE TABLE "UserAchievement" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "achievementId" TEXT NOT NULL,
    "earnedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("id")
) ;

CREATE UNIQUE INDEX "UserAchievement_userId_achievementId_key" ON "UserAchievement"("userId", "achievementId");

-- Create UserEngagementStreak table
CREATE TABLE "UserEngagementStreak" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "currentStreak" INT NOT NULL DEFAULT 0,
    "longestStreak" INT NOT NULL DEFAULT 0,
    "lastEngagementDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    PRIMARY KEY ("id")
) ;

CREATE UNIQUE INDEX "UserEngagementStreak_userId_key" ON "UserEngagementStreak"("userId");

-- Add foreign key constraints
ALTER TABLE "UserAchievement" ADD CONSTRAINT "UserAchievement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE;

ALTER TABLE "UserEngagementStreak" ADD CONSTRAINT "UserEngagementStreak_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX "UserEngagement_userId_confirmed_idx" ON "UserEngagement"("userId", "confirmed");
CREATE INDEX "UserEngagement_confirmedAt_idx" ON "UserEngagement"("confirmedAt");
CREATE INDEX "UserAchievement_userId_idx" ON "UserAchievement"("userId");
CREATE INDEX "UserAchievement_achievementId_idx" ON "UserAchievement"("achievementId");
CREATE INDEX "UserEngagementStreak_userId_idx" ON "UserEngagementStreak"("userId");