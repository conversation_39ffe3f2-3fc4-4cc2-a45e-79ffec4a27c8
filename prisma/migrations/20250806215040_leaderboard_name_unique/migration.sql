/*
  Warnings:

  - You are about to drop the column "createdAt" on the "Notification" table. All the data in the column will be lost.
  - You are about to drop the column "isRead" on the "Notification" table. All the data in the column will be lost.
  - You are about to drop the column "type" on the "Notification" table. All the data in the column will be lost.
  - You are about to drop the column "userId" on the "Notification" table. All the data in the column will be lost.
  - You are about to drop the column "uploadedById" on the "Video" table. All the data in the column will be lost.
  - Added the required column "sentBy" to the "Notification" table without a default value. This is not possible if the table is not empty.
  - Added the required column "videoId" to the "Notification" table without a default value. This is not possible if the table is not empty.
  - Added the required column "platform" to the "Video" table without a default value. This is not possible if the table is not empty.
  - Added the required column "updatedAt" to the "Video" table without a default value. This is not possible if the table is not empty.
  - Added the required column "url" to the "Video" table without a default value. This is not possible if the table is not empty.
  - Added the required column "userId" to the "Video" table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Notification" DROP CONSTRAINT "Notification_userId_fkey";

-- DropForeignKey
ALTER TABLE "Video" DROP CONSTRAINT "Video_uploadedById_fkey";

-- DropIndex
DROP INDEX IF EXISTS "Notification_createdAt_idx";
DROP INDEX IF EXISTS "Notification_isRead_idx";
DROP INDEX IF EXISTS "Notification_type_idx";
DROP INDEX IF EXISTS "Notification_userId_idx";
DROP INDEX IF EXISTS "Video_uploadedById_fkey";

-- AlterTable
ALTER TABLE "Notification" 
    DROP COLUMN "createdAt",
    DROP COLUMN "isRead",
    DROP COLUMN "type",
    DROP COLUMN "userId",
    ADD COLUMN "isSent" BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN "sentAt" TIMESTAMP(3),
    ADD COLUMN "sentBy" TEXT NOT NULL,
    ADD COLUMN "videoId" TEXT NOT NULL,
    ALTER COLUMN "message" TYPE TEXT;

-- AlterTable
ALTER TABLE "Video" 
    DROP COLUMN "uploadedById",
    ADD COLUMN "duration" INT,
    ADD COLUMN "platform" TEXT NOT NULL,
    ADD COLUMN "status" TEXT NOT NULL DEFAULT 'draft',
    ADD COLUMN "thumbnailUrl" TEXT,
    ADD COLUMN "updatedAt" TIMESTAMP(3) NOT NULL,
    ADD COLUMN "url" TEXT NOT NULL,
    ADD COLUMN "userId" TEXT NOT NULL,
    ADD COLUMN "views" INT NOT NULL DEFAULT 0,
    ALTER COLUMN "description" TYPE TEXT;

-- CreateTable
CREATE TABLE "LeaderboardEntry" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "points" INT NOT NULL,
    "level" INT NOT NULL,
    "rank" INT NOT NULL,
    "isCurrentUser" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    PRIMARY KEY ("id")
) ;

CREATE UNIQUE INDEX "LeaderboardEntry_name_key" ON "LeaderboardEntry"("name");

-- CreateTable
CREATE TABLE "UserNotification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "notificationId" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("id")
) ;

CREATE INDEX "UserNotification_isRead_idx" ON "UserNotification"("isRead");
CREATE INDEX "UserNotification_createdAt_idx" ON "UserNotification"("createdAt");
CREATE UNIQUE INDEX "UserNotification_userId_notificationId_key" ON "UserNotification"("userId", "notificationId");

-- CreateIndex
CREATE INDEX "Notification_isSent_idx" ON "Notification"("isSent");

-- CreateIndex
CREATE INDEX "Notification_sentAt_idx" ON "Notification"("sentAt");

-- CreateIndex
CREATE INDEX "Notification_sentBy_idx" ON "Notification"("sentBy");

-- CreateIndex
CREATE INDEX "Notification_videoId_idx" ON "Notification"("videoId");

-- CreateIndex
CREATE INDEX "Video_userId_idx" ON "Video"("userId");

-- CreateIndex
CREATE INDEX "Video_status_idx" ON "Video"("status");

-- CreateIndex
CREATE INDEX "Video_createdAt_idx" ON "Video"("createdAt");

-- AddForeignKey
ALTER TABLE "Video" ADD CONSTRAINT "Video_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_sentBy_fkey" FOREIGN KEY ("sentBy") REFERENCES "User"("id") ON DELETE RESTRICT;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "Video"("id") ON DELETE RESTRICT;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "Notification"("id") ON DELETE RESTRICT;