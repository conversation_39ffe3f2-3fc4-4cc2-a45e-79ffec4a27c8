/*
  Warnings:

  - You are about to drop the column `createdAt` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `isRead` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `uploadedById` on the `Video` table. All the data in the column will be lost.
  - Added the required column `sentBy` to the `Notification` table without a default value. This is not possible if the table is not empty.
  - Added the required column `videoId` to the `Notification` table without a default value. This is not possible if the table is not empty.
  - Added the required column `platform` to the `Video` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Video` table without a default value. This is not possible if the table is not empty.
  - Added the required column `url` to the `Video` table without a default value. This is not possible if the table is not empty.
  - Added the required column `userId` to the `Video` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `Notification` DROP FOREIGN KEY `Notification_userId_fkey`;

-- DropForeignKey
ALTER TABLE `Video` DROP FOREIGN KEY `Video_uploadedById_fkey`;

-- DropIndex
DROP INDEX `Notification_createdAt_idx` ON `Notification`;

-- DropIndex
DROP INDEX `Notification_isRead_idx` ON `Notification`;

-- DropIndex
DROP INDEX `Notification_type_idx` ON `Notification`;

-- DropIndex
DROP INDEX `Notification_userId_idx` ON `Notification`;

-- DropIndex
DROP INDEX `Video_uploadedById_fkey` ON `Video`;

-- AlterTable
ALTER TABLE `Notification` DROP COLUMN `createdAt`,
    DROP COLUMN `isRead`,
    DROP COLUMN `type`,
    DROP COLUMN `userId`,
    ADD COLUMN `isSent` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `sentAt` DATETIME(3) NULL,
    ADD COLUMN `sentBy` VARCHAR(191) NOT NULL,
    ADD COLUMN `videoId` VARCHAR(191) NOT NULL,
    MODIFY `message` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `Video` DROP COLUMN `uploadedById`,
    ADD COLUMN `duration` INTEGER NULL,
    ADD COLUMN `platform` VARCHAR(191) NOT NULL,
    ADD COLUMN `status` VARCHAR(191) NOT NULL DEFAULT 'draft',
    ADD COLUMN `thumbnailUrl` VARCHAR(191) NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NOT NULL,
    ADD COLUMN `url` VARCHAR(191) NOT NULL,
    ADD COLUMN `userId` VARCHAR(191) NOT NULL,
    ADD COLUMN `views` INTEGER NOT NULL DEFAULT 0,
    MODIFY `description` TEXT NULL;

-- CreateTable
CREATE TABLE `LeaderboardEntry` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `points` INTEGER NOT NULL,
    `level` INTEGER NOT NULL,
    `rank` INTEGER NOT NULL,
    `isCurrentUser` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `LeaderboardEntry_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserNotification` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `notificationId` VARCHAR(191) NOT NULL,
    `isRead` BOOLEAN NOT NULL DEFAULT false,
    `readAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `UserNotification_isRead_idx`(`isRead`),
    INDEX `UserNotification_createdAt_idx`(`createdAt`),
    UNIQUE INDEX `UserNotification_userId_notificationId_key`(`userId`, `notificationId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Notification_isSent_idx` ON `Notification`(`isSent`);

-- CreateIndex
CREATE INDEX `Notification_sentAt_idx` ON `Notification`(`sentAt`);

-- CreateIndex
CREATE INDEX `Notification_sentBy_idx` ON `Notification`(`sentBy`);

-- CreateIndex
CREATE INDEX `Notification_videoId_idx` ON `Notification`(`videoId`);

-- CreateIndex
CREATE INDEX `Video_userId_idx` ON `Video`(`userId`);

-- CreateIndex
CREATE INDEX `Video_status_idx` ON `Video`(`status`);

-- CreateIndex
CREATE INDEX `Video_createdAt_idx` ON `Video`(`createdAt`);

-- AddForeignKey
ALTER TABLE `Video` ADD CONSTRAINT `Video_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Notification` ADD CONSTRAINT `Notification_sentBy_fkey` FOREIGN KEY (`sentBy`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Notification` ADD CONSTRAINT `Notification_videoId_fkey` FOREIGN KEY (`videoId`) REFERENCES `Video`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserNotification` ADD CONSTRAINT `UserNotification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserNotification` ADD CONSTRAINT `UserNotification_notificationId_fkey` FOREIGN KEY (`notificationId`) REFERENCES `Notification`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
