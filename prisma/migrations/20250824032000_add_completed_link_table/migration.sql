CREATE TABLE "CompletedLink" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "mediaId" TEXT NOT NULL,
    "videoId" TEXT,
    "linkId" TEXT,
    "completedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CompletedLink_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CompletedLink_userId_idx" ON "CompletedLink"("userId");

-- CreateIndex
CREATE INDEX "CompletedLink_mediaId_idx" ON "CompletedLink"("mediaId");

-- CreateIndex
CREATE INDEX "CompletedLink_videoId_idx" ON "CompletedLink"("videoId");

-- CreateIndex
CREATE INDEX "CompletedLink_linkId_idx" ON "CompletedLink"("linkId");

-- CreateIndex
CREATE UNIQUE INDEX "CompletedLink_userId_mediaId_linkId_key" ON "CompletedLink"("userId", "mediaId", "linkId");

-- AddForeignKey
ALTER TABLE "CompletedLink" ADD CONSTRAINT "CompletedLink_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompletedLink" ADD CONSTRAINT "CompletedLink_mediaId_fkey" FOREIGN KEY ("mediaId") REFERENCES "Media"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompletedLink" ADD CONSTRAINT "CompletedLink_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "Video"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompletedLink" ADD CONSTRAINT "CompletedLink_linkId_fkey" FOREIGN KEY ("linkId") REFERENCES "VideoLink"("id") ON DELETE CASCADE ON UPDATE CASCADE;
