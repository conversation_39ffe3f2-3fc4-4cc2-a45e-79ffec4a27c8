/*
  Warnings:

  - You are about to alter the column `role` on the `User` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Enum(EnumId(0))`.
  - A unique constraint covering the columns `[userId,videoId,platform,action]` on the table `UserEngagement` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `action` to the `UserEngagement` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `UserEngagement` DROP FOREIGN KEY `UserEngagement_userId_fkey`;

-- DropIndex
DROP INDEX `UserEngagement_userId_videoId_platform_key` ON `UserEngagement`;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `notificationPreference` ENUM('ALL', 'NWA_ONLY') NOT NULL DEFAULT 'ALL',
    ADD COLUMN `nwaVideoNotifications` BOOLEAN NOT NULL DEFAULT true,
    ADD COLUMN `userVideoNotifications` BO<PERSON>EAN NOT NULL DEFAULT true,
    MODIFY `role` ENUM('USER', 'ADMIN', 'NWA_TEAM') NOT NULL DEFAULT 'USER';

-- AlterTable
ALTER TABLE `UserEngagement` ADD COLUMN `action` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `Video` ADD COLUMN `isFeatured` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `notificationSentAt` DATETIME(3) NULL,
    ADD COLUMN `rumbleUrl` VARCHAR(191) NULL,
    ADD COLUMN `tiktokUrl` VARCHAR(191) NULL,
    ADD COLUMN `youtubeUrl` VARCHAR(191) NULL,
    MODIFY `description` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `Share` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `videoId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `Share_videoId_idx`(`videoId`),
    UNIQUE INDEX `Share_userId_videoId_key`(`userId`, `videoId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompletedVideo` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `videoId` VARCHAR(191) NOT NULL,
    `completedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `CompletedVideo_userId_idx`(`userId`),
    INDEX `CompletedVideo_videoId_idx`(`videoId`),
    UNIQUE INDEX `CompletedVideo_userId_videoId_key`(`userId`, `videoId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `VideoLinkLike` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `videoLinkId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `VideoLinkLike_videoLinkId_idx`(`videoLinkId`),
    UNIQUE INDEX `VideoLinkLike_userId_videoLinkId_key`(`userId`, `videoLinkId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `VideoLinkShare` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `videoLinkId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `VideoLinkShare_videoLinkId_idx`(`videoLinkId`),
    UNIQUE INDEX `VideoLinkShare_userId_videoLinkId_key`(`userId`, `videoLinkId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `UserEngagement_action_idx` ON `UserEngagement`(`action`);

-- CreateIndex
CREATE UNIQUE INDEX `UserEngagement_userId_videoId_platform_action_key` ON `UserEngagement`(`userId`, `videoId`, `platform`, `action`);

-- CreateIndex
CREATE INDEX `Video_isFeatured_idx` ON `Video`(`isFeatured`);

-- AddForeignKey
ALTER TABLE `Share` ADD CONSTRAINT `Share_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Share` ADD CONSTRAINT `Share_videoId_fkey` FOREIGN KEY (`videoId`) REFERENCES `Video`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompletedVideo` ADD CONSTRAINT `CompletedVideo_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompletedVideo` ADD CONSTRAINT `CompletedVideo_videoId_fkey` FOREIGN KEY (`videoId`) REFERENCES `Video`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `VideoLinkLike` ADD CONSTRAINT `VideoLinkLike_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `VideoLinkLike` ADD CONSTRAINT `VideoLinkLike_videoLinkId_fkey` FOREIGN KEY (`videoLinkId`) REFERENCES `VideoLink`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `VideoLinkShare` ADD CONSTRAINT `VideoLinkShare_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `VideoLinkShare` ADD CONSTRAINT `VideoLinkShare_videoLinkId_fkey` FOREIGN KEY (`videoLinkId`) REFERENCES `VideoLink`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
