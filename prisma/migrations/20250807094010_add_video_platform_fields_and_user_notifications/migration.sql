/*
  Warnings:

  - You are about to alter the column "role" on the "User" table. The data in that column could be lost. The data in that column will be cast from "VarChar(191)" to "Enum(EnumId(0))".
  - A unique constraint covering the columns "[userId,videoId,platform,action]" on the table "UserEngagement" will be added. If there are existing duplicate values, this will fail.
  - Added the required column "action" to the "UserEngagement" table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "UserEngagement" DROP CONSTRAINT "UserEngagement_userId_fkey";

-- DropIndex
DROP INDEX IF EXISTS "UserEngagement_userId_videoId_platform_key";

-- AlterTable
ALTER TABLE "User" 
    ADD COLUMN "notificationPreference" TEXT NOT NULL DEFAULT 'ALL',
    ADD COLUMN "nwaVideoNotifications" BOOLEAN NOT NULL DEFAULT true,
    ADD COLUMN "userVideoNotifications" BOOLEAN NOT NULL DEFAULT true,
    ALTER COLUMN "role" TYPE TEXT,
    ALTER COLUMN "role" SET DEFAULT 'USER';

-- AlterTable
ALTER TABLE "UserEngagement" ADD COLUMN "action" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Video" 
    ADD COLUMN "isFeatured" BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN "notificationSentAt" TIMESTAMP(3),
    ADD COLUMN "rumbleUrl" TEXT,
    ADD COLUMN "tiktokUrl" TEXT,
    ADD COLUMN "youtubeUrl" TEXT,
    ALTER COLUMN "description" TYPE TEXT;

-- CreateTable
CREATE TABLE "Share" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "videoId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("id")
) ;

CREATE INDEX "Share_videoId_idx" ON "Share"("videoId");
CREATE UNIQUE INDEX "Share_userId_videoId_key" ON "Share"("userId", "videoId");

-- CreateTable
CREATE TABLE "CompletedVideo" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "videoId" TEXT NOT NULL,
    "completedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("id")
) ;

CREATE INDEX "CompletedVideo_userId_idx" ON "CompletedVideo"("userId");
CREATE INDEX "CompletedVideo_videoId_idx" ON "CompletedVideo"("videoId");
CREATE UNIQUE INDEX "CompletedVideo_userId_videoId_key" ON "CompletedVideo"("userId", "videoId");

-- CreateTable
CREATE TABLE "VideoLinkLike" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "videoLinkId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("id")
) ;

CREATE INDEX "VideoLinkLike_videoLinkId_idx" ON "VideoLinkLike"("videoLinkId");
CREATE UNIQUE INDEX "VideoLinkLike_userId_videoLinkId_key" ON "VideoLinkLike"("userId", "videoLinkId");

-- CreateTable
CREATE TABLE "VideoLinkShare" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "videoLinkId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("id")
) ;

CREATE INDEX "VideoLinkShare_videoLinkId_idx" ON "VideoLinkShare"("videoLinkId");
CREATE UNIQUE INDEX "VideoLinkShare_userId_videoLinkId_key" ON "VideoLinkShare"("userId", "videoLinkId");

-- CreateIndex
CREATE INDEX "UserEngagement_action_idx" ON "UserEngagement"("action");

-- CreateIndex
CREATE UNIQUE INDEX "UserEngagement_userId_videoId_platform_action_key" ON "UserEngagement"("userId", "videoId", "platform", "action");

-- CreateIndex
CREATE INDEX "Video_isFeatured_idx" ON "Video"("isFeatured");

-- AddForeignKey
ALTER TABLE "Share" ADD CONSTRAINT "Share_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "Share" ADD CONSTRAINT "Share_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "Video"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "CompletedVideo" ADD CONSTRAINT "CompletedVideo_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "CompletedVideo" ADD CONSTRAINT "CompletedVideo_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "Video"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "VideoLinkLike" ADD CONSTRAINT "VideoLinkLike_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "VideoLinkLike" ADD CONSTRAINT "VideoLinkLike_videoLinkId_fkey" FOREIGN KEY ("videoLinkId") REFERENCES "VideoLink"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "VideoLinkShare" ADD CONSTRAINT "VideoLinkShare_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE;

-- AddForeignKey
ALTER TABLE "VideoLinkShare" ADD CONSTRAINT "VideoLinkShare_videoLinkId_fkey" FOREIGN KEY ("videoLinkId") REFERENCES "VideoLink"("id") ON DELETE CASCADE;