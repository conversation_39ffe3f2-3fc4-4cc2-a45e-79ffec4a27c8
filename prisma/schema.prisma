generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Media {
  id          String          @id @default(cuid())
  type        String // "video" or "ebook"
  title       String
  description String?
  fileUrl     String? // For ebooks (PDF)
  videoUrl    String? // For videos
  releaseTime DateTime? // For scheduled ebook release
  published   Boolean         @default(false)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  // Relations
  likes       Like[]
  shares      Share[]
  completions CompletedLink[]
  userId      String
  user        User            @relation(fields: [userId], references: [id])

  // Indexes
  @@index([type])
  @@index([fileUrl])
  @@index([releaseTime])
  @@index([published])
}

model Affiliate {
  id          String   @id
  name        String
  description String?  @db.Text
  logoUrl     String?
  websiteUrl  String?
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime

  @@index([isActive])
  @@index([sortOrder])
}

model AnalyticsEvent {
  id        String   @id
  eventType String
  eventData String   @db.Text
  createdAt DateTime @default(now())

  @@index([createdAt])
  @@index([eventType])
}

model Badge {
  id          String      @id
  name        String      @unique
  description String?     @db.Text
  icon        String?
  category    String
  criteria    String      @db.Text
  points      Int         @default(0)
  rarity      String      @default("common")
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime
  UserBadge   UserBadge[]

  @@index([category])
  @@index([isActive])
  @@index([rarity])
}

model EmailPreference {
  id               String   @id @default(cuid())
  userId           String
  notificationType String
  isEnabled        Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  User             User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, notificationType])
  @@index([isEnabled])
  @@index([notificationType])
  @@index([userId])
}

model EmailQueue {
  id           String    @id @default(cuid())
  userId       String?
  emailAddress String
  subject      String
  bodyHtml     String?   @db.Text
  bodyText     String?   @db.Text
  templateName String?
  templateData String?   @db.Text
  status       String    @default("pending")
  attempts     Int       @default(0)
  maxAttempts  Int       @default(3)
  scheduledAt  DateTime  @default(now())
  sentAt       DateTime?
  errorMessage String?   @db.Text
  createdAt    DateTime  @default(now())
  User         User?     @relation(fields: [userId], references: [id])

  @@index([attempts])
  @@index([scheduledAt])
  @@index([status])
  @@index([userId])
}

model Share {
  id        String   @id @default(cuid())
  userId    String
  mediaId   String
  videoId   String?
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  media     Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  Video     Video?   @relation(fields: [videoId], references: [id])

  @@unique([userId, mediaId, videoId])
  @@index([mediaId])
  @@index([videoId])
}

model Like {
  id        String   @id @default(cuid())
  userId    String
  mediaId   String
  videoId   String?
  createdAt DateTime @default(now())
  User      User     @relation(fields: [userId], references: [id])
  Media     Media    @relation(fields: [mediaId], references: [id])
  Video     Video?   @relation(fields: [videoId], references: [id])

  @@unique([userId, mediaId, videoId])
  @@index([mediaId])
  @@index([videoId])
}

model SupportTicket {
  id        String   @id
  userId    String?
  email     String
  subject   String
  message   String   @db.Text
  status    String   @default("open")
  priority  String   @default("medium")
  createdAt DateTime @default(now())
  updatedAt DateTime

  @@index([createdAt])
  @@index([priority])
  @@index([status])
}

model User {
  id                       String                 @id @default(cuid())
  email                    String                 @unique
  name                     String?
  password                 String
  score                    Int                    @default(0)
  createdAt                DateTime               @default(now())
  role                     Role                   @default(USER)
  experiencePoints         Int                    @default(0)
  lastActivityAt           DateTime?
  level                    Int                    @default(1)
  totalPoints              Int                    @default(0)
  notificationPreference   NotificationPreference @default(ALL)
  nwaVideoNotifications    Boolean                @default(true)
  userVideoNotifications   Boolean                @default(true)
  pushSubscription         String?                @db.Text
  pushSubscriptionEndpoint String?
  locale                   String?                @default("en")
  auditLogs                AuditLog[]
  completedLinks           CompletedLink[]
  reportsSubmitted         ContentReport[]        @relation("ReportedBy")
  reportsReviewed          ContentReport[]        @relation("ReviewedBy")
  EmailPreference          EmailPreference[]
  EmailQueue               EmailQueue[]
  Like                     Like[]
  moderationActions        ModerationAction[]     @relation("ModeratedBy")
  SentNotifications        Notification[]         @relation("NotificationSender")
  notifications            Notification[]         @relation("UserNotifications")
  Share                    Share[]
  UserBadge                UserBadge[]
  UserEngagement           UserEngagement[]
  UserMilestone            UserMilestone[]
  UserAchievement          UserAchievement[]
  UserEngagementStreak     UserEngagementStreak?
  ReceivedNotifications    UserNotification[]
  UserSession              UserSession[]
  Accounts                 Account[]
  Sessions                 Session[]
  Videos                   Video[]
  VideoLinks               VideoLink[]
  VideoLinkLikes           VideoLinkLike[]
  VideoLinkShares          VideoLinkShare[]
  media                    Media[] // User's uploaded media

  @@index([lastActivityAt])
  @@index([level])
  @@index([totalPoints])
}

model UserBadge {
  id           String   @id
  userId       String
  badgeId      String
  earnedAt     DateTime @default(now())
  progressData String?  @db.Text
  Badge        Badge    @relation(fields: [badgeId], references: [id], onDelete: Cascade)
  User         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, badgeId])
  @@index([badgeId])
  @@index([earnedAt])
  @@index([userId])
}

model LeaderboardEntry {
  id            String   @id @default(cuid())
  name          String   @unique
  points        Int
  level         Int
  rank          Int
  isCurrentUser Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Video {
  id                 String             @id @default(cuid())
  createdAt          DateTime           @default(now())
  description        String?
  title              String
  duration           Int?
  platform           String
  status             String             @default("draft")
  thumbnailUrl       String?
  updatedAt          DateTime           @updatedAt
  url                String
  userId             String
  views              Int                @default(0)
  isFeatured         Boolean            @default(false)
  notificationSentAt DateTime?
  rumbleUrl          String?
  tiktokUrl          String?
  youtubeUrl         String?
  completions        CompletedLink[]
  reports            ContentReport[]
  likes              Like[]
  moderationActions  ModerationAction[]
  notifications      Notification[]
  shares             Share[]
  engagements        UserEngagement[]
  user               User               @relation(fields: [userId], references: [id])
  links              VideoLink[]

  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([isFeatured])
}

model Notification {
  id         String             @id @default(cuid())
  title      String
  message    String
  isSent     Boolean            @default(false)
  sentAt     DateTime?
  sentBy     String
  videoId    String?
  userId     String?
  type       String             @default("general")
  sender     User               @relation("NotificationSender", fields: [sentBy], references: [id])
  user       User?              @relation("UserNotifications", fields: [userId], references: [id])
  video      Video?             @relation(fields: [videoId], references: [id])
  recipients UserNotification[]

  @@index([isSent])
  @@index([sentAt])
  @@index([sentBy])
  @@index([videoId])
  @@index([userId])
}

model UserNotification {
  id             String       @id @default(cuid())
  userId         String
  notificationId String
  isRead         Boolean      @default(false)
  readAt         DateTime?
  createdAt      DateTime     @default(now())
  notification   Notification @relation(fields: [notificationId], references: [id])
  user           User         @relation(fields: [userId], references: [id])

  @@unique([userId, notificationId])
  @@index([isRead])
  @@index([createdAt])
  @@index([notificationId], map: "UserNotification_notificationId_unique")
}

model UserEngagement {
  id          String    @id @default(cuid())
  userId      String
  videoId     String
  platform    String
  likedAt     DateTime?
  createdAt   DateTime  @default(now())
  action      String
  confirmed   Boolean?
  confirmedAt DateTime?
  User        User      @relation(fields: [userId], references: [id])
  Video       Video     @relation(fields: [videoId], references: [id])

  @@unique([userId, videoId, platform, action])
  @@index([likedAt])
  @@index([platform])
  @@index([action])
  @@index([userId])
  @@index([videoId])
  @@index([userId, confirmed])
  @@index([confirmedAt])
}

model UserMilestone {
  id            String   @id
  userId        String
  milestoneType String
  currentValue  Int      @default(0)
  lastUpdated   DateTime @default(now())
  User          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, milestoneType])
  @@index([currentValue])
  @@index([milestoneType])
  @@index([userId])
}

model UserSession {
  id           String   @id
  userId       String
  sessionToken String   @unique
  ipAddress    String?
  userAgent    String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  expiresAt    DateTime
  User         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([expiresAt])
  @@index([sessionToken])
  @@index([userId], map: "UserSession_userId_unique")
}

model CompletedLink {
  id          String     @id @default(cuid())
  userId      String
  mediaId     String
  videoId     String?
  linkId      String?
  completedAt DateTime   @default(now())
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  media       Media      @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  video       Video?     @relation(fields: [videoId], references: [id])
  link        VideoLink? @relation(fields: [linkId], references: [id])

  @@unique([userId, mediaId, linkId])
  @@index([userId])
  @@index([mediaId])
  @@index([videoId])
  @@index([linkId])
}

model VideoLink {
  id                String             @id @default(cuid())
  userId            String
  videoId           String
  platform          String
  linkUrl           String
  createdAt         DateTime           @default(now())
  status            String             @default("shared")
  updatedAt         DateTime           @updatedAt
  reports           ContentReport[]
  moderationActions ModerationAction[]
  User              User               @relation(fields: [userId], references: [id])
  Video             Video              @relation(fields: [videoId], references: [id])
  likes             VideoLinkLike[]
  shares            VideoLinkShare[]
  completedLinks    CompletedLink[]

  @@unique([userId, videoId, platform])
  @@index([platform])
  @@index([status])
  @@index([userId])
  @@index([videoId])
}

model VideoLinkLike {
  id          String    @id @default(cuid())
  userId      String
  videoLinkId String
  createdAt   DateTime  @default(now())
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  videoLink   VideoLink @relation(fields: [videoLinkId], references: [id], onDelete: Cascade)

  @@unique([userId, videoLinkId])
  @@index([videoLinkId])
}

model VideoLinkShare {
  id          String    @id @default(cuid())
  userId      String
  videoLinkId String
  createdAt   DateTime  @default(now())
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  videoLink   VideoLink @relation(fields: [videoLinkId], references: [id], onDelete: Cascade)

  @@unique([userId, videoLinkId])
  @@index([videoLinkId])
}

model ContentReport {
  id                String             @id @default(cuid())
  reporterId        String
  videoId           String?
  videoLinkId       String?
  reason            String
  description       String?            @db.Text
  status            String             @default("pending")
  reviewedBy        String?
  reviewedAt        DateTime?
  resolution        String?            @db.Text
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  reporter          User               @relation("ReportedBy", fields: [reporterId], references: [id], onDelete: Cascade)
  reviewer          User?              @relation("ReviewedBy", fields: [reviewedBy], references: [id])
  video             Video?             @relation(fields: [videoId], references: [id], onDelete: Cascade)
  videoLink         VideoLink?         @relation(fields: [videoLinkId], references: [id], onDelete: Cascade)
  moderationActions ModerationAction[]

  @@index([status])
  @@index([reason])
  @@index([createdAt])
  @@index([reporterId])
  @@index([reviewedBy])
  @@index([videoId])
  @@index([videoLinkId])
}

model ModerationAction {
  id          String         @id @default(cuid())
  moderatorId String
  videoId     String?
  videoLinkId String?
  reportId    String?
  action      String
  reason      String?        @db.Text
  createdAt   DateTime       @default(now())
  moderator   User           @relation("ModeratedBy", fields: [moderatorId], references: [id])
  report      ContentReport? @relation(fields: [reportId], references: [id], onDelete: Cascade)
  video       Video?         @relation(fields: [videoId], references: [id], onDelete: Cascade)
  videoLink   VideoLink?     @relation(fields: [videoLinkId], references: [id], onDelete: Cascade)

  @@index([action])
  @@index([createdAt])
  @@index([moderatorId])
  @@index([videoId])
  @@index([videoLinkId])
  @@index([reportId])
}

model ContentFilter {
  id          String   @id @default(cuid())
  type        String
  pattern     String   @db.Text
  severity    String   @default("medium")
  action      String   @default("flag")
  isActive    Boolean  @default(true)
  description String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([severity])
  @@index([isActive])
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String?
  action     String
  resource   String
  resourceId String?
  details    String?  @db.Text
  ipAddress  String?
  userAgent  String?  @db.Text
  sessionId  String?
  timestamp  DateTime @default(now())
  user       User?    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@index([resourceId])
}

model UserAchievement {
  id            String   @id @default(cuid())
  userId        String
  achievementId String
  earnedAt      DateTime @default(now())
  createdAt     DateTime @default(now())
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@index([userId])
  @@index([achievementId])
  @@index([earnedAt])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserEngagementStreak {
  id                 String   @id @default(cuid())
  userId             String   @unique
  currentStreak      Int      @default(0)
  longestStreak      Int      @default(0)
  lastEngagementDate DateTime
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([currentStreak])
  @@index([longestStreak])
  @@index([lastEngagementDate])
}

enum Role {
  USER
  ADMIN
  NWA_TEAM
}

enum NotificationPreference {
  ALL
  NWA_ONLY
}
