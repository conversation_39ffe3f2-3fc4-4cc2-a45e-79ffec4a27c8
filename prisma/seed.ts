import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  const email = '<EMAIL>';
  const password = 'fcA*5-c0nwmFF!!';
  const hashedPassword = await bcrypt.hash(password, 12);

  console.log('Attempting to create/update admin user...');
  
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (!existingUser) {
      // Create admin user
      await prisma.user.create({
        data: {
          id: 'admin-user-1',
          email,
          name: 'Admin User',
          password: hashedPassword,
          role: 'ADMIN',
          score: 0,
          level: 1,
          totalPoints: 0,
          experiencePoints: 0,
        },
      });
      console.log('✅ Admin user created successfully');
    }

    // Seed leaderboard entries
    console.log('Seeding leaderboard data...');
    const leaderboardEntries = [
      { name: 'ProGamer2024', points: 15420, level: 12, rank: 1, isCurrentUser: false },
      { name: 'VideoMaster', points: 14890, level: 11, rank: 2, isCurrentUser: false },
      { name: 'You', points: 12340, level: 10, rank: 3, isCurrentUser: true },
      { name: 'ContentKing', points: 11200, level: 9, rank: 4, isCurrentUser: false },
      { name: 'StreamQueen', points: 10850, level: 9, rank: 5, isCurrentUser: false },
    ];
    for (const entry of leaderboardEntries) {
      await prisma.leaderboardEntry.upsert({
        where: { name: entry.name },
        update: entry,
        create: entry,
      });
    }
    console.log('✅ Leaderboard data seeded successfully');

    // Update existing user's password
    if (existingUser) {
      await prisma.user.update({
        where: { email },
        data: {
          password: hashedPassword,
          role: 'ADMIN',
        },
      });
      console.log('✅ Admin user updated successfully');
    }
  } catch (error) {
    console.error('❌ Error creating/updating user:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seed script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
