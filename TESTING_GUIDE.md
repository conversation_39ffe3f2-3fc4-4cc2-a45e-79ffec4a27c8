# 🧪 NWA Promote - Live Testing Guide

## 🚀 **Your Application is Running at: http://localhost:${process.env.PORT || 3000}**

## 📋 **Testing Checklist**

### 1️⃣ **User Registration Testing**

#### **Test Steps:**
1. **Navigate to Registration Page**
   ```
   http://localhost:${process.env.PORT || 3000}/register
   ```

2. **Test Valid Registration:**
   - Name: `Test User`
   - Email: `<EMAIL>`
   - Password: `SecurePassword123!`
   - Click "Create Account"
   - ✅ **Expected**: Redirect to sign-in page

3. **Test Duplicate Email:**
   - Try registering with the same email again
   - ✅ **Expected**: Error message "User with this email already exists"

4. **Test Invalid Data:**
   - Empty fields
   - Invalid email format
   - Weak password
   - ✅ **Expected**: Validation errors

#### **What to Look For:**
- ✅ Clean, responsive registration form
- ✅ Real-time validation feedback
- ✅ Proper error handling
- ✅ Secure password hashing
- ✅ Redirect after successful registration

---

### 2️⃣ **User Authentication Testing**

#### **Test Steps:**
1. **Navigate to Sign-In Page**
   ```
   http://localhost:${process.env.PORT || 3000}/auth/signin
   ```

2. **Test Login with Registered User:**
   - Email: `<EMAIL>`
   - Password: `SecurePassword123!`
   - ✅ **Expected**: Successful login and redirect to dashboard

3. **Test Invalid Credentials:**
   - Wrong email or password
   - ✅ **Expected**: Authentication error

#### **What to Look For:**
- ✅ NextAuth.js integration working
- ✅ Session management
- ✅ Proper redirects
- ✅ Security measures active

---

### 3️⃣ **Content Management Testing**

#### **Admin Video Management:**
1. **Create Admin User** (if needed):
   - Register a user
   - Manually update role in database to 'ADMIN'

2. **Navigate to Admin Dashboard:**
   ```
   http://localhost:${process.env.PORT || 3000}/admin/videos
   ```

3. **Test Video Creation:**
   - Title: `Test Multi-Platform Video`
   - Description: `Testing video upload functionality`
   - YouTube URL: `https://youtube.com/watch?v=dQw4w9WgXcQ`
   - TikTok URL: `https://tiktok.com/@user/video/123456`
   - Rumble URL: `https://rumble.com/test-video`
   - ✅ **Expected**: Video created successfully

4. **Test Video Management:**
   - Edit existing videos
   - Feature/unfeature videos
   - Send notifications
   - ✅ **Expected**: All operations work smoothly

#### **User Video Sharing:**
1. **Navigate to Dashboard:**
   ```
   http://localhost:${process.env.PORT || 3000}/dashboard
   ```

2. **Test User Video Creation:**
   - Create personal video links
   - Share on different platforms
   - ✅ **Expected**: Videos appear in user section

#### **What to Look For:**
- ✅ Multi-platform URL support
- ✅ Video validation
- ✅ Admin vs User permissions
- ✅ Real-time updates
- ✅ Notification system

---

### 4️⃣ **Feature Testing**

#### **Engagement Features:**
1. **Test Leaderboard:**
   ```
   http://localhost:${process.env.PORT || 3000}/leaderboard
   ```
   - ✅ **Expected**: User rankings with medals

2. **Test Video Interactions:**
   - Like videos on different platforms
   - Share videos
   - Complete videos
   - ✅ **Expected**: Buttons work, stats update

#### **Notification System:**
1. **Test Notification Preferences:**
   ```
   http://localhost:${process.env.PORT || 3000}/settings/notifications
   ```
   - Toggle NWA video notifications
   - Toggle user video notifications
   - ✅ **Expected**: Preferences save correctly

2. **Test Notification Delivery:**
   - Admin sends video notification
   - Check notification bell
   - ✅ **Expected**: Notifications appear

#### **International Support:**
1. **Test Language Switching:**
   - Look for language selector
   - Switch between languages
   - ✅ **Expected**: Interface translates

---

### 5️⃣ **Admin Features Testing**

#### **Analytics Dashboard:**
```
http://localhost:${process.env.PORT || 3000}/admin/analytics
```
- ✅ **Expected**: Charts and metrics display

#### **User Management:**
```
http://localhost:${process.env.PORT || 3000}/admin/users
```
- ✅ **Expected**: User list and management tools

#### **Content Moderation:**
```
http://localhost:${process.env.PORT || 3000}/admin/moderation
```
- ✅ **Expected**: Content reports and moderation tools

#### **System Monitoring:**
```
http://localhost:${process.env.PORT || 3000}/admin/monitoring
```
- ✅ **Expected**: System health and performance metrics

#### **Audit Logs:**
```
http://localhost:${process.env.PORT || 3000}/admin/audit-logs
```
- ✅ **Expected**: Activity logs with filtering

---

### 6️⃣ **Security Testing**

#### **Test Security Headers:**
1. Open browser developer tools
2. Check Network tab for security headers:
   - ✅ `Content-Security-Policy`
   - ✅ `X-Frame-Options`
   - ✅ `X-Content-Type-Options`
   - ✅ `Strict-Transport-Security`

#### **Test Rate Limiting:**
1. Try rapid API requests
2. ✅ **Expected**: Rate limiting kicks in

#### **Test CSRF Protection:**
1. API requests include CSRF tokens
2. ✅ **Expected**: Unauthorized requests blocked

---

### 7️⃣ **Performance Testing**

#### **Page Load Times:**
- Dashboard: < 3 seconds
- Video pages: < 2 seconds
- Admin pages: < 4 seconds

#### **Mobile Responsiveness:**
1. Test on different screen sizes
2. ✅ **Expected**: Responsive design works

#### **PWA Features:**
1. Install app prompt
2. Offline functionality
3. Push notifications

---

## 🎯 **Quick Test Commands**

### **Browser Console Tests:**
```javascript
// Test API Health
fetch('/api/health').then(r => r.json()).then(console.log)

// Test Leaderboard
fetch('/api/leaderboard').then(r => r.json()).then(console.log)

// Test Video Search
fetch('/api/videos/search?q=test').then(r => r.json()).then(console.log)
```

### **Database Quick Check:**
```bash
# Check user count
npx prisma studio
# Or check in browser at: http://localhost:${process.env.PORT || 3000}
```

---

## 🚨 **Common Issues & Solutions**

### **CSRF Token Issues:**
- Ensure you're logged in
- Refresh the page to get new tokens
- Check browser cookies are enabled

### **Database Connection:**
- Verify DATABASE_URL in .env
- Check if database is running
- Run `npx prisma migrate deploy` if needed

### **Redis Connection:**
- Check if Redis is running locally
- Verify REDIS_URL in .env
- Some features may work without Redis

---

## ✅ **Success Criteria**

Your platform is working correctly if:
- ✅ Users can register and login
- ✅ Videos can be created and managed
- ✅ Multi-platform URLs work
- ✅ Admin features are accessible
- ✅ Security measures are active
- ✅ Performance is acceptable
- ✅ Mobile experience is smooth

---

## 🎉 **Next Steps After Testing**

1. **Create Demo Content** for showcase
2. **Set up Production Environment**
3. **Configure Real Social Media URLs**
4. **Set up Email/SMS Notifications**
5. **Deploy using CI/CD Pipeline**

**Happy Testing! Your world-class platform awaits! 🚀**