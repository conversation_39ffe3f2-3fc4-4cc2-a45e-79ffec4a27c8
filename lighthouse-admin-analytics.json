{"lighthouseVersion": "12.8.1", "requestedUrl": "http://localhost:3000/admin/analytics", "mainDocumentUrl": "http://localhost:3000/admin/analytics", "finalDisplayedUrl": "http://localhost:3000/auth/signin", "finalUrl": "http://localhost:3000/admin/analytics", "fetchTime": "2025-08-19T07:30:31.166Z", "gatherMode": "navigation", "runWarnings": [], "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2622, "credits": {"axe-core": "4.10.3"}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "Insecure URL"}, {"key": "resolution", "valueType": "text", "label": "Request Resolution"}], "items": []}}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "notApplicable"}, "viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 916.96, "numericUnit": "millisecond", "displayValue": "0.9 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.04, "scoreDisplayMode": "numeric", "numericValue": 7575.************, "numericUnit": "millisecond", "displayValue": "7.6 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 0.9, "scoreDisplayMode": "numeric", "numericValue": 3328.8074857354877, "numericUnit": "millisecond", "displayValue": "3.3 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3057, "items": [{"timing": 382, "timestamp": 87656207484, "data": "data:image/jpeg;base64,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"}, {"timing": 764, "timestamp": 87656589609, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1146, "timestamp": 87656971734, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1529, "timestamp": 87657353859, "data": "data:image/jpeg;base64,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"}, {"timing": 1911, "timestamp": 87657735984, "data": "data:image/jpeg;base64,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"}, {"timing": 2293, "timestamp": 87658118109, "data": "data:image/jpeg;base64,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"}, {"timing": 2675, "timestamp": 87658500234, "data": "data:image/jpeg;base64,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"}, {"timing": 3057, "timestamp": 87658882359, "data": "data:image/jpeg;base64,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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 3072, "timestamp": 87658897566, "data": "data:image/jpeg;base64,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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 0.68, "scoreDisplayMode": "numeric", "numericValue": 395, "numericUnit": "millisecond", "displayValue": "400 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 0.55, "scoreDisplayMode": "numeric", "numericValue": 232, "numericUnit": "millisecond", "displayValue": "230 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "unitless", "displayValue": "0", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0, "newEngineResult": {"cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0}, "newEngineResultDiffered": false}]}}, "errors-in-console": {"id": "errors-in-console", "title": "No browser errors logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "sourceLocation", "valueType": "source-location", "label": "Source"}, {"key": "description", "valueType": "code", "label": "Description"}], "items": []}}, "server-response-time": {"id": "server-response-time", "title": "Reduce initial server response time", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1125.98, "numericUnit": "millisecond", "displayValue": "Root document took 1,130 ms", "metricSavings": {"FCP": 1050, "LCP": 1050}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:3000/admin/analytics", "responseTime": 1125.98}], "overallSavingsMs": 1025.98}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.47, "scoreDisplayMode": "numeric", "numericValue": 7575.************, "numericUnit": "millisecond", "displayValue": "7.6 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "name", "valueType": "text", "label": "Name"}, {"key": "timingType", "valueType": "text", "label": "Type"}, {"key": "startTime", "valueType": "ms", "granularity": 0.01, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 0.01, "label": "Duration"}], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": null, "scoreDisplayMode": "notApplicable", "displayValue": "", "details": {"type": "criticalrequestchain", "chains": {"12F1892B7F5D37AF00F54BB54DBB915B": {"request": {"url": "http://localhost:3000/admin/analytics", "startTime": 87655.82714, "endTime": 87656.970313, "responseReceivedTime": 87656.953596, "transferSize": 10730}}}, "longestChain": {"duration": 1143.1729999929667, "length": 1, "transferSize": 10730}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}, {"key": "displayedAspectRatio", "valueType": "text", "label": "Aspect Ratio (Displayed)"}, {"key": "actualAspectRatio", "valueType": "text", "label": "Aspect Ratio (Actual)"}], "items": []}}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}, {"key": "displayedSize", "valueType": "text", "label": "Displayed size"}, {"key": "actualSize", "valueType": "text", "label": "Actual size"}, {"key": "expectedSize", "valueType": "text", "label": "Expected size"}], "items": []}}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "value", "valueType": "text", "label": "Deprecation / Warning"}, {"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "name", "valueType": "text", "label": "Name"}, {"key": "url", "valueType": "url", "label": "URL"}], "items": []}}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimize main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 2429.6159999999973, "numericUnit": "millisecond", "displayValue": "2.4 s", "metricSavings": {"TBT": 400}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 1031.************}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 469.42400000000004}, {"group": "other", "groupLabel": "Other", "duration": 448.4760000000028}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 379.21600000000035}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 79.93600000000016}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 20.915999999999997}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "Reduce JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1390.9400000000007, "numericUnit": "millisecond", "displayValue": "1.4 s", "metricSavings": {"TBT": 400}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "http://localhost:3000/admin/analytics", "total": 586.5720000000006, "scripting": 4.956, "scriptParseCompile": 6.847999999999997}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "total": 527.6640000000002, "scripting": 476.13200000000023, "scriptParseCompile": 21.856}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "total": 482.6200000000003, "scripting": 351.02400000000034, "scriptParseCompile": 105.88000000000001}, {"url": "Unattributable", "total": 265.6240000000008, "scripting": 11.487999999999998, "scriptParseCompile": 0}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "total": 169.32800000000015, "scripting": 75.60000000000016, "scriptParseCompile": 93.252}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "total": 108.02799999999999, "scripting": 33.831999999999994, "scriptParseCompile": 69.912}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "total": 85.49599999999998, "scripting": 34.731999999999985, "scriptParseCompile": 35.632000000000005}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "total": 70.292, "scripting": 3.5560000000000005, "scriptParseCompile": 66.24}], "summary": {"wastedMs": 1390.9400000000007}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "ms", "label": "Est Savings"}], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 36, "numScripts": 26, "numStylesheets": 1, "numFonts": 2, "numTasks": 1150, "numTasksOver10ms": 9, "numTasksOver25ms": 4, "numTasksOver50ms": 2, "numTasksOver100ms": 0, "numTasksOver500ms": 0, "rtt": 0.16500000000000004, "throughput": 19123965.175714534, "maxRtt": 0.16500000000000004, "maxServerLatency": 8.48, "totalByteWeight": 1054398, "totalTaskTime": 607.4040000000011, "mainDocumentTransferSize": 10730}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "http://localhost:3000/admin/analytics", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 0, "networkRequestTime": 0.9300000071525574, "networkEndTime": 1144.1030000001192, "finished": true, "transferSize": 10730, "resourceSize": 30788, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/media/8ee3a1ba4ed5baee-s.p.be19f591.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1135.593000009656, "networkRequestTime": 1136.3020000010729, "networkEndTime": 1149.2580000162125, "finished": true, "transferSize": 31588, "resourceSize": 31288, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/media/b0a57561b6cb5495-s.p.da1ebef7.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1135.9350000023842, "networkRequestTime": 1136.6060000061989, "networkEndTime": 1149.5610000044107, "finished": true, "transferSize": 28656, "resourceSize": 28356, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/%5Broot-of-the-server%5D__8ebb6d4b._.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1136.0680000185966, "networkRequestTime": 1136.882000014186, "networkEndTime": 1149.6909999996424, "finished": true, "transferSize": 21547, "resourceSize": 186553, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_6aaa83c7._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1140.4389999955893, "networkRequestTime": 1145.6060000061989, "networkEndTime": 1158.8540000021458, "finished": true, "transferSize": 1010, "resourceSize": 673, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1140.6180000156164, "networkRequestTime": 1145.8719999939203, "networkEndTime": 1191.1300000101328, "finished": true, "transferSize": 156810, "resourceSize": 896265, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1140.6990000009537, "networkRequestTime": 1146.2170000076294, "networkEndTime": 1206.722000002861, "finished": true, "transferSize": 243992, "resourceSize": 1382529, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1140.792999997735, "networkRequestTime": 1150.0230000019073, "networkEndTime": 1168.9780000001192, "finished": true, "transferSize": 48298, "resourceSize": 252242, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1140.882000014186, "networkRequestTime": 1150.190000012517, "networkEndTime": 1180.5760000050068, "finished": true, "transferSize": 88052, "resourceSize": 482158, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1140.9740000069141, "networkRequestTime": 1150.2639999985695, "networkEndTime": 1166.612000003457, "finished": true, "transferSize": 29893, "resourceSize": 157987, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_%40swc_helpers_cjs_8e433861._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.0480000078678, "networkRequestTime": 1164.2040000110865, "networkEndTime": 1170.1140000075102, "finished": true, "transferSize": 1377, "resourceSize": 3436, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/_e69f0d32._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.149000003934, "networkRequestTime": 1167.3080000132322, "networkEndTime": 1175.8439999967813, "finished": true, "transferSize": 1026, "resourceSize": 689, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/_01f48b92._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.2850000113249, "networkRequestTime": 1170.4210000038147, "networkEndTime": 1181.1270000040531, "finished": true, "transferSize": 17124, "resourceSize": 69113, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_01fcdebf._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.3750000149012, "networkRequestTime": 1171.042999997735, "networkEndTime": 1182.2950000166893, "finished": true, "transferSize": 21497, "resourceSize": 134984, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_favicon_ico_mjs_f9cadd25._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.444000005722, "networkRequestTime": 1179.7260000109673, "networkEndTime": 1191.4770000129938, "finished": true, "transferSize": 597, "resourceSize": 260, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.5190000087023, "networkRequestTime": 1181.9360000044107, "networkEndTime": 1200.542999997735, "finished": true, "transferSize": 28683, "resourceSize": 160770, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_55585268._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.5980000048876, "networkRequestTime": 1182.151000007987, "networkEndTime": 1198.524000018835, "finished": true, "transferSize": 11055, "resourceSize": 95767, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_layout_tsx_68b267f5._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.6850000023842, "networkRequestTime": 1183.0760000050068, "networkEndTime": 1195.7930000126362, "finished": true, "transferSize": 680, "resourceSize": 343, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_26659e8e._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.7650000154972, "networkRequestTime": 1191.6810000091791, "networkEndTime": 1200.9790000021458, "finished": true, "transferSize": 8051, "resourceSize": 120130, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.8930000066757, "networkRequestTime": 1191.871000006795, "networkEndTime": 1228.912000015378, "finished": true, "transferSize": 142275, "resourceSize": 1161068, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1141.972000002861, "networkRequestTime": 1196.4810000061989, "networkEndTime": 1232.5770000070333, "finished": true, "transferSize": 130083, "resourceSize": 870103, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_admin_analytics_page_tsx_2ab5ba2b._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1142.0659999996424, "networkRequestTime": 1200.3090000152588, "networkEndTime": 1216.5439999997616, "finished": true, "transferSize": 683, "resourceSize": 346, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_components_builtin_global-error_2ab5ba2b.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1142.1500000059605, "networkRequestTime": 1201.3210000097752, "networkEndTime": 1216.7660000026226, "finished": true, "transferSize": 540, "resourceSize": 204, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/unregister-sw.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1142.229000017047, "networkRequestTime": 1201.6130000054836, "networkEndTime": 1216.998000010848, "finished": true, "transferSize": 1562, "resourceSize": 1231, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/manifest.json", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1454.151000007987, "networkRequestTime": 1454.6330000013113, "networkEndTime": 1462.2269999980927, "finished": true, "transferSize": 1706, "resourceSize": 2419, "statusCode": 200, "mimeType": "application/json", "resourceType": "Manifest", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/icons/icon.svg", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1460.7550000101328, "networkRequestTime": 1461.7360000014305, "networkEndTime": 1467.0020000040531, "finished": true, "transferSize": 1666, "resourceSize": 598, "statusCode": 200, "mimeType": "image/svg+xml", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/icons/icon-144x144.png", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1490.8680000007153, "networkRequestTime": 1491.2520000189543, "networkEndTime": 1496.6270000040531, "finished": true, "transferSize": 9036, "resourceSize": 7993, "statusCode": 200, "mimeType": "image/png", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/auth/signin?_rsc=1j921", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1499.3780000060797, "networkRequestTime": 1500.8229999989271, "networkEndTime": 1643.387999996543, "finished": true, "transferSize": 3615, "resourceSize": 7534, "statusCode": 200, "mimeType": "text/x-component", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1508.097000002861, "networkRequestTime": 1508.925000011921, "networkEndTime": 1521.4850000143051, "finished": true, "transferSize": 4400, "resourceSize": 16529, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_5af4a3bc._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1508.8550000190735, "networkRequestTime": 1509.6760000139475, "networkEndTime": 1519.916000008583, "finished": true, "transferSize": 623, "resourceSize": 286, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1615.632000014186, "networkRequestTime": 1616.8870000094175, "networkEndTime": 1639.5119999945164, "finished": true, "transferSize": 2163, "resourceSize": 10233, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_2ab5ba2b._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1616.7549999952316, "networkRequestTime": 1617.9070000201464, "networkEndTime": 1638.5730000138283, "finished": true, "transferSize": 603, "resourceSize": 266, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1671.7099999934435, "networkRequestTime": 1674.1180000007153, "networkEndTime": 2155.744000002742, "finished": true, "transferSize": 2163, "resourceSize": 10233, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_2ab5ba2b._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1674.5729999989271, "networkRequestTime": 1676.7590000033379, "networkEndTime": 1819.6930000036955, "finished": true, "transferSize": 603, "resourceSize": 266, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/manifest.json", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1688.179000005126, "networkRequestTime": 1688.6280000060797, "networkEndTime": 1787.4800000041723, "finished": true, "transferSize": 1005, "resourceSize": 2419, "statusCode": 200, "mimeType": "application/json", "resourceType": "Manifest", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/icons/icon-144x144.png", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 1789.0720000118017, "networkRequestTime": 1789.4930000156164, "networkEndTime": 2198.396999999881, "finished": true, "transferSize": 1006, "resourceSize": 7993, "statusCode": 200, "mimeType": "image/png", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 87655826210, "initiators": [{"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 479}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 579}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 696}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 795}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 892}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 982}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1081}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1149}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1217}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1307}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1398}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1478}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1549}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1635}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1706}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1799}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1879}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 1979}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 2106}, {"type": "parser", "url": "http://localhost:3000/admin/analytics", "lineNumber": 0, "columnNumber": 3672}]}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 0.16500000000000004, "numericUnit": "millisecond", "displayValue": "0 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:3000", "rtt": 0.16500000000000004}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 8.48, "numericUnit": "millisecond", "displayValue": "10 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:3000", "serverResponseTime": 8.48}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 11.635, "startTime": 1132.396}, {"duration": 65.015, "startTime": 1158.757}, {"duration": 5.731, "startTime": 1245.416}, {"duration": 9.578, "startTime": 1257.053}, {"duration": 17.836, "startTime": 1266.978}, {"duration": 57.894, "startTime": 1292.472}, {"duration": 5.55, "startTime": 1359.13}, {"duration": 17.626, "startTime": 1367.075}, {"duration": 42.397, "startTime": 1384.726}, {"duration": 5.686, "startTime": 1427.37}, {"duration": 5.738, "startTime": 1435.652}, {"duration": 5.227, "startTime": 1441.454}, {"duration": 6.193, "startTime": 1448.643}, {"duration": 5.311, "startTime": 1456.034}, {"duration": 5.867, "startTime": 1463.646}, {"duration": 10.488, "startTime": 1470.371}, {"duration": 5.143, "startTime": 1481.063}, {"duration": 5.122, "startTime": 1486.383}, {"duration": 5.38, "startTime": 1491.853}, {"duration": 25.188, "startTime": 1497.266}, {"duration": 7.731, "startTime": 1536.758}, {"duration": 5.221, "startTime": 1625.241}, {"duration": 5.216, "startTime": 1630.873}, {"duration": 5.432, "startTime": 1645.324}, {"duration": 5.615, "startTime": 1655.061}, {"duration": 5.139, "startTime": 1661.832}, {"duration": 17.356, "startTime": 1667.048}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 7575, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 917, "largestContentfulPaint": 7575, "interactive": 7575, "speedIndex": 3329, "totalBlockingTime": 395, "maxPotentialFID": 232, "cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0, "timeToFirstByte": 1128, "observedTimeOrigin": 0, "observedTimeOriginTs": 87655825359, "observedNavigationStart": 0, "observedNavigationStartTs": 87655825359, "observedFirstPaint": 1246, "observedFirstPaintTs": 87657071760, "observedFirstContentfulPaint": 1246, "observedFirstContentfulPaintTs": 87657071760, "observedFirstContentfulPaintAllFrames": 1246, "observedFirstContentfulPaintAllFramesTs": 87657071760, "observedLargestContentfulPaint": 1709, "observedLargestContentfulPaintTs": 87657534318, "observedLargestContentfulPaintAllFrames": 1709, "observedLargestContentfulPaintAllFramesTs": 87657534318, "observedTraceEnd": 4513, "observedTraceEndTs": 87660338456, "observedLoad": 1433, "observedLoadTs": 87657258757, "observedDomContentLoaded": 1238, "observedDomContentLoadedTs": 87657063649, "observedCumulativeLayoutShift": 0, "observedCumulativeLayoutShiftMainFrame": 0, "observedFirstVisualChange": 1246, "observedFirstVisualChangeTs": 87657071359, "observedLastVisualChange": 3057, "observedLastVisualChangeTs": 87658882359, "observedSpeedIndex": 1666, "observedSpeedIndexTs": 87657491714}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 36, "transferSize": 1054398}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 26, "transferSize": 943843}, {"resourceType": "font", "label": "Font", "requestCount": 2, "transferSize": 60244}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 1, "transferSize": 21547}, {"resourceType": "other", "label": "Other", "requestCount": 6, "transferSize": 18034}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 10730}, {"resourceType": "image", "label": "Image", "requestCount": 0, "transferSize": 0}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 0, "transferSize": 0}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "7,580 ms", "metricSavings": {"LCP": 5100}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-P", "path": "1,H<PERSON>L,1,BODY,1,DIV,1,MAIN,2,DIV,0,DIV,1,P", "selector": "main.flex-grow > div > div > p", "boundingRect": {"top": 458, "bottom": 506, "left": 40, "right": 372, "width": 332, "height": 48}, "snippet": "<p style=\"margin-bottom: 24px; color: rgb(102, 102, 102);\">", "nodeLabel": "You need to be signed in to access this page."}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 1128.134, "percent": "15%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 6447.************, "percent": "85%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "extra"}, "label": "Element"}, {"key": "score", "valueType": "numeric", "subItemsHeading": {"key": "cause", "valueType": "text"}, "granularity": 0.001, "label": "Layout shift score"}], "items": []}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "6 long tasks found", "metricSavings": {"TBT": 400}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Duration"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "duration": 232, "startTime": 7233.************}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "duration": 170, "startTime": 6826.************}, {"url": "http://localhost:3000/admin/analytics", "duration": 130, "startTime": 665.48}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "duration": 101, "startTime": 7465.************}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "duration": 71, "startTime": 5883.************}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "duration": 71, "startTime": 6996.************}], "sortedBy": ["duration"], "skipSumming": ["startTime"], "debugData": {"type": "debugdata", "urls": ["http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "http://localhost:3000/admin/analytics", "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js"], "tasks": [{"urlIndex": 0, "startTime": 7233.9, "duration": 232, "other": 232, "scriptEvaluation": 0}, {"urlIndex": 1, "startTime": 6826.3, "duration": 170, "other": 170, "scriptEvaluation": 0}, {"urlIndex": 2, "startTime": 665.5, "duration": 130, "other": 130, "paintCompositeRender": 0, "scriptEvaluation": 0, "styleLayout": 0}, {"urlIndex": 3, "startTime": 7465.9, "duration": 101, "other": 101}, {"urlIndex": 4, "startTime": 5883.9, "duration": 71, "other": 71, "scriptEvaluation": 0}, {"urlIndex": 5, "startTime": 6996.3, "duration": 71, "other": 71, "scriptEvaluation": 0}]}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "2 animated elements found", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "failureReason", "valueType": "text"}, "label": "Element"}, {"key": null, "valueType": "text", "subItemsHeading": {"key": "animation", "valueType": "text"}, "label": "Name"}], "items": [{"node": {"type": "node", "lhId": "page-1-path", "path": "1,HTML,1,BODY,16,SCRIPT,0,NEXTJS-PORTAL,a,#document-fragment,8,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,BUTTON,0,svg,0,g,0,path", "selector": "button > svg > g > path.paused", "boundingRect": {"top": 779, "bottom": 793, "left": 33, "right": 44, "width": 11, "height": 14}, "snippet": "<path class=\"paused\" d=\"M13.3 15.2 L2.34 1 V12.6\" fill=\"none\" stroke=\"url(#next_logo_paint0_linear_1357_10853)\" stroke-width=\"1.86\" mask=\"url(#next_logo_mask0)\" stroke-dasharray=\"29.6\" stroke-dashoffset=\"29.6\">", "nodeLabel": "button > svg > g > path.paused"}, "subItems": {"type": "subitems", "items": [{"failureReason": "Unsupported CSS Property: stroke-dashoffset", "animation": "draw0"}, {"failureReason": "Unsupported CSS Property: stroke-dashoffset", "animation": "draw0"}]}}, {"node": {"type": "node", "lhId": "page-2-path", "path": "1,HTML,1,BODY,16,SCRIPT,0,NEXTJS-PORTAL,a,#document-fragment,8,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,BUTTON,0,svg,0,g,1,path", "selector": "button > svg > g > path.paused", "boundingRect": {"top": 780, "bottom": 791, "left": 42, "right": 42, "width": 0, "height": 12}, "snippet": "<path class=\"paused\" d=\"M11.825 1.5 V13.1\" stroke-width=\"1.86\" stroke=\"url(#next_logo_paint1_linear_1357_10853)\" stroke-dasharray=\"11.6\" stroke-dashoffset=\"11.6\">", "nodeLabel": "button > svg > g > path.paused"}, "subItems": {"type": "subitems", "items": [{"failureReason": "Unsupported CSS Property: stroke-dashoffset", "animation": "draw1"}, {"failureReason": "Unsupported CSS Property: stroke-dashoffset", "animation": "draw1"}]}}]}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}], "items": []}, "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Missing source maps for large first-party JavaScript", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "scriptUrl", "valueType": "url", "subItemsHeading": {"key": "error"}, "label": "URL"}, {"key": "sourceMapUrl", "valueType": "url", "label": "Map URL"}], "items": [{"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}, {"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}, {"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js.map", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}, {"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}, {"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/src_55585268._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/src_55585268._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/src_26659e8e._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/src_26659e8e._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_01fcdebf._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_01fcdebf._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/node_modules_%40swc_helpers_cjs_8e433861._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/node_modules_%40swc_helpers_cjs_8e433861._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}, {"scriptUrl": "http://localhost:3000/_next/static/chunks/_01f48b92._.js", "sourceMapUrl": "http://localhost:3000/_next/static/chunks/_01f48b92._.js.map", "subItems": {"type": "subitems", "items": [{"error": "Error: Map has no `mappings` field"}]}}]}}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"directive": "script-src", "description": "`'unsafe-inline'` allows the execution of unsafe in-page scripts and event handlers. Consider using CSP nonces or hashes to allow scripts individually.", "severity": "High"}]}}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": []}}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"description": "No COOP header found", "severity": "High"}]}}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": []}}, "trusted-types-xss": {"id": "trusted-types-xss", "title": "Mitigate DOM-based XSS with Trusted Types", "description": "The `require-trusted-types-for` directive in the `Content-Security-Policy` (CSP) header instructs user agents to control the data passed to DOM XSS sink functions. [Learn more about mitigating DOM-based XSS with Trusted Types](https://developer.chrome.com/docs/lighthouse/best-practices/trusted-types-xss).", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No `Content-Security-Policy` header with Trusted Types directive found"}]}}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "http://localhost:3000/admin/analytics", "resourceBytes": 18780, "encodedBytes": 5655, "children": [{"name": "(inline) (self.__next_f=…", "resourceBytes": 43, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2151, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2244, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2441, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 146, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 3294, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 350, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2167, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 867, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 1832, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 73, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 1374, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 1798, "unusedBytes": 0}]}, {"name": "http://localhost:3000/unregister-sw.js", "resourceBytes": 1231, "encodedBytes": 439, "unusedBytes": 530}, {"name": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_6aaa83c7._.js", "resourceBytes": 673, "encodedBytes": 673, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "resourceBytes": 157983, "encodedBytes": 29523, "unusedBytes": 90966}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_%40swc_helpers_cjs_8e433861._.js", "resourceBytes": 3436, "encodedBytes": 1009, "unusedBytes": 282}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "resourceBytes": 252242, "encodedBytes": 47928, "unusedBytes": 119552}, {"name": "http://localhost:3000/_next/static/chunks/_e69f0d32._.js", "resourceBytes": 689, "encodedBytes": 689, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/_01f48b92._.js", "resourceBytes": 69113, "encodedBytes": 16754, "unusedBytes": 33119}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_01fcdebf._.js", "resourceBytes": 134962, "encodedBytes": 21127, "unusedBytes": 78881}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "resourceBytes": 482140, "encodedBytes": 87682, "unusedBytes": 157359}, {"name": "http://localhost:3000/_next/static/chunks/src_app_favicon_ico_mjs_f9cadd25._.js", "resourceBytes": 260, "encodedBytes": 260, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "resourceBytes": 896265, "encodedBytes": 156440, "unusedBytes": 259376}, {"name": "http://localhost:3000/_next/static/chunks/src_app_layout_tsx_68b267f5._.js", "resourceBytes": 343, "encodedBytes": 343, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/src_55585268._.js", "resourceBytes": 95766, "encodedBytes": 10685, "unusedBytes": 15337}, {"name": "http://localhost:3000/_next/static/chunks/src_26659e8e._.js", "resourceBytes": 120130, "encodedBytes": 7681, "unusedBytes": 10556}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "resourceBytes": 160766, "encodedBytes": 28313, "unusedBytes": 58834}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "resourceBytes": 1382509, "encodedBytes": 243621, "unusedBytes": 933069}, {"name": "http://localhost:3000/_next/static/chunks/src_app_admin_analytics_page_tsx_2ab5ba2b._.js", "resourceBytes": 346, "encodedBytes": 346, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_components_builtin_global-error_2ab5ba2b.js", "resourceBytes": 204, "encodedBytes": 204, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "resourceBytes": 870082, "encodedBytes": 129713, "unusedBytes": 546150}, {"name": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "resourceBytes": 1161066, "encodedBytes": 141904, "unusedBytes": 721023}, {"name": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_5af4a3bc._.js", "resourceBytes": 286, "encodedBytes": 286, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js", "resourceBytes": 16529, "encodedBytes": 4031, "unusedBytes": 11874}, {"name": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_2ab5ba2b._.js", "resourceBytes": 266, "encodedBytes": 266, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js", "resourceBytes": 10233, "encodedBytes": 1794, "unusedBytes": 394}, {"name": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_2ab5ba2b._.js", "resourceBytes": 266, "encodedBytes": 266, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/src_app_auth_signin_page_tsx_cfeb1ee2._.js", "resourceBytes": 10233, "encodedBytes": 1794, "unusedBytes": 9960}]}}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "button-name": {"id": "button-name", "title": "Buttons do not have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-BUTTON", "path": "1,HTML,1,BODY,1,DIV,3,DIV,0,DIV,0,DIV,2,BUTTON", "selector": "div.fixed > div.bg-white > div.flex > button.flex-shrink-0", "boundingRect": {"top": 654, "bottom": 674, "left": 359, "right": 379, "width": 20, "height": 20}, "snippet": "<button class=\"flex-shrink-0 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300\">", "nodeLabel": "div.fixed > div.bg-white > div.flex > button.flex-shrink-0", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}], "debugData": {"type": "debugdata", "impact": "critical", "tags": ["cat.name-role-value", "wcag2a", "wcag412", "section508", "section508.22.a", "TTv5", "TT6.a", "EN-301-549", "EN-*******", "ACT"]}}}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "notApplicable"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors do not have a sufficient contrast ratio.", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-1-BUTTON", "path": "1,HTML,1,BODY,1,DIV,3,DIV,0,DIV,1,DIV,0,BUTTON", "selector": "div.fixed > div.bg-white > div.mt-4 > button.flex-1", "boundingRect": {"top": 754, "bottom": 790, "left": 33, "right": 202, "width": 169, "height": 36}, "snippet": "<button class=\"flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium py-2 p…\">", "nodeLabel": "Install App", "explanation": "Fix any of the following:\n  Element has insufficient color contrast of 3.76 (foreground color: #ffffff, background color: #2b7fff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1"}}], "debugData": {"type": "debugdata", "impact": "serious", "tags": ["cat.color", "wcag2aa", "wcag143", "TTv5", "TT13.c", "EN-301-549", "EN-*******", "ACT"]}}}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "notApplicable"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "document-title": {"id": "document-title", "title": "Document has a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "notApplicable"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "notApplicable"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "notApplicable"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "notApplicable"}, "heading-order": {"id": "heading-order", "title": "Heading elements are not in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-2-H3", "path": "1,HTML,1,BODY,1,DIV,3,DIV,0,DIV,0,DIV,1,DIV,0,H3", "selector": "div.bg-white > div.flex > div.flex-1 > h3.text-sm", "boundingRect": {"top": 654, "bottom": 674, "left": 85, "right": 347, "width": 262, "height": 20}, "snippet": "<h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">", "nodeLabel": "Install NWA Promote", "explanation": "Fix any of the following:\n  Heading order invalid"}}], "debugData": {"type": "debugdata", "impact": "moderate", "tags": ["cat.semantics", "best-practice"]}}}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element has a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": null, "scoreDisplayMode": "notApplicable"}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "notApplicable"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": null, "scoreDisplayMode": "notApplicable"}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is used in the `<meta name=\"viewport\">` element or the `[maximum-scale]` attribute is less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-3-META", "path": "1,HTM<PERSON>,0,HEAD,55,META", "selector": "head > meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">", "nodeLabel": "head > meta", "explanation": "Fix any of the following:\n  user-scalable=no on <meta> tag disables zooming on mobile devices"}}], "debugData": {"type": "debugdata", "impact": "critical", "tags": ["cat.sensory-and-visual-cues", "wcag2aa", "wcag144", "EN-301-549", "EN-*******", "ACT"]}}}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "notApplicable"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "target-size": {"id": "target-size", "title": "Touch targets have sufficient size and spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "notApplicable"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "notApplicable"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "manual"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "manual"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "manual"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "manual"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "manual"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "manual"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "manual"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "manual"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "manual"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "manual"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Uses efficient cache policy on static assets", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "byte", "displayValue": "0 resources found", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": []}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 1054398, "numericUnit": "byte", "displayValue": "Total size was 1,030 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "totalBytes": 243992}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "totalBytes": 156810}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "totalBytes": 142275}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "totalBytes": 130083}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "totalBytes": 88052}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "totalBytes": 48298}, {"url": "http://localhost:3000/_next/static/media/8ee3a1ba4ed5baee-s.p.be19f591.woff2", "totalBytes": 31588}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "totalBytes": 29893}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "totalBytes": 28683}, {"url": "http://localhost:3000/_next/static/media/b0a57561b6cb5495-s.p.da1ebef7.woff2", "totalBytes": 28656}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 4 KiB", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/%5Broot-of-the-server%5D__8ebb6d4b._.css", "totalBytes": 21547, "wastedBytes": 3939, "wastedPercent": 18.280595862837913}], "overallSavingsMs": 0, "overallSavingsBytes": 3939, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1950, "numericUnit": "millisecond", "displayValue": "Est savings of 300 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 1950}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "totalBytes": 243621, "wastedBytes": 111390, "wastedPercent": 45.72274032212449}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "totalBytes": 156440, "wastedBytes": 42839, "wastedPercent": 27.38386526306393}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "totalBytes": 129713, "wastedBytes": 35975, "wastedPercent": 27.73393772081252}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "totalBytes": 87682, "wastedBytes": 33392, "wastedPercent": 38.08333679014394}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "totalBytes": 141904, "wastedBytes": 25313, "wastedPercent": 17.83826242435831}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "totalBytes": 47928, "wastedBytes": 18398, "wastedPercent": 38.3861529800747}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "totalBytes": 29523, "wastedBytes": 9000, "wastedPercent": 30.484925593260037}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_01fcdebf._.js", "totalBytes": 21127, "wastedBytes": 8288, "wastedPercent": 39.229560913442306}, {"url": "http://localhost:3000/_next/static/chunks/_01f48b92._.js", "totalBytes": 16754, "wastedBytes": 8236, "wastedPercent": 49.16007118776496}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "totalBytes": 28313, "wastedBytes": 7722, "wastedPercent": 27.273801674483412}, {"url": "http://localhost:3000/_next/static/chunks/src_55585268._.js", "totalBytes": 10685, "wastedBytes": 3469, "wastedPercent": 32.46872585259904}, {"url": "http://localhost:3000/_next/static/chunks/src_26659e8e._.js", "totalBytes": 7681, "wastedBytes": 3383, "wastedPercent": 44.04062265878631}], "overallSavingsMs": 1950, "overallSavingsBytes": 307405, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 1950}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 2250, "numericUnit": "millisecond", "displayValue": "Est savings of 420 KiB", "metricSavings": {"FCP": 0, "LCP": 2250}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "totalBytes": 243617, "wastedBytes": 164420, "wastedPercent": 67.49098920875018}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_recharts_es6_cd4772bc._.js", "totalBytes": 141904, "wastedBytes": 88122, "wastedPercent": 62.10008733353659}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_361b775b._.js", "totalBytes": 129710, "wastedBytes": 81419, "wastedPercent": 62.76994582119846}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js", "totalBytes": 156440, "wastedBytes": 45273, "wastedPercent": 28.93965512432149}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_20b209c9._.js", "totalBytes": 87679, "wastedBytes": 28616, "wastedPercent": 32.63761563031485}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js", "totalBytes": 47928, "wastedBytes": 22716, "wastedPercent": 47.39575487032294}], "overallSavingsMs": 2250, "overallSavingsBytes": 430566, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 2250}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 9 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "wastedBytes": 8182, "subItems": {"type": "subitems", "items": [{"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 40, "column": 27}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 20, "column": 29}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 22, "column": 3}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 36, "column": 27}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 43, "column": 22}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 14, "column": 127}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js", "urlProvider": "network", "line": 14, "column": 36}}]}, "totalBytes": 0}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "wastedBytes": 379, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "urlProvider": "network", "line": 393, "column": 71}}, {"signal": "@babel/plugin-transform-spread", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js", "urlProvider": "network", "line": 22078, "column": 29}}]}, "totalBytes": 0}, {"url": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "wastedBytes": 212, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "urlProvider": "network", "line": 717, "column": 48}}, {"signal": "@babel/plugin-transform-regenerator", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/node_modules_b55780ac._.js", "urlProvider": "network", "line": 312, "column": 44}}]}, "totalBytes": 0}], "overallSavingsMs": 0, "overallSavingsBytes": 8773, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": 1, "scoreDisplayMode": "binary"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": 1, "scoreDisplayMode": "binary"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 101, "numericUnit": "element", "displayValue": "101 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 101}}, {"node": {"type": "node", "lhId": "1-9-stop", "path": "1,HTML,1,BODY,16,SCRIPT,0,NEXTJS-PORTAL,a,#document-fragment,8,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,BUTTON,0,svg,1,defs,0,linearGradient,0,stop", "selector": "svg > defs > lineargradient#next_logo_paint0_linear_1357_10853 > stop", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<stop stop-color=\"white\">", "nodeLabel": "svg > defs > lineargradient#next_logo_paint0_linear_1357_10853 > stop"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 13}}, {"node": {"type": "node", "lhId": "1-10-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body.geist_e531dabc-module__QGiZLq__variable", "boundingRect": {"top": 0, "bottom": 995, "left": 0, "right": 412, "width": 412, "height": 995}, "snippet": "<body class=\"geist_e531dabc-module__QGiZLq__variable geist_mono_68a01160-module__YLcDdW…\">", "nodeLabel": "body.geist_e531dabc-module__QGiZLq__variable"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 18}}]}, "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "issueType", "valueType": "text", "subItemsHeading": {"key": "url", "valueType": "url"}, "label": "Issue type"}], "items": []}}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}, "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "notApplicable"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Failing Elements"}], "items": []}}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}, "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document has a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": 1, "scoreDisplayMode": "binary"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": 1, "scoreDisplayMode": "binary"}, "font-size": {"id": "font-size", "title": "Document uses legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": 1, "scoreDisplayMode": "binary", "displayValue": "100% legible text", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "selector", "valueType": "code", "label": "Selector"}, {"key": "coverage", "valueType": "text", "label": "% of Page Text"}, {"key": "fontSize", "valueType": "text", "label": "Font Size"}], "items": [{"source": {"type": "code", "value": "Legible text"}, "selector": "", "coverage": "100.00%", "fontSize": "≥ 12px"}]}}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "href", "valueType": "url", "label": "Link destination"}, {"key": "text", "valueType": "text", "label": "Link Text"}], "items": []}}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Uncrawlable Link"}], "items": []}}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": 1, "scoreDisplayMode": "binary", "warnings": [], "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "label": "Blocking Directive Source"}], "items": []}}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": null, "scoreDisplayMode": "notApplicable"}, "hreflang": {"id": "hreflang", "title": "Document has a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "subItemsHeading": {"key": "reason", "valueType": "text"}, "label": ""}], "items": []}}, "canonical": {"id": "canonical", "title": "Document has a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": null, "scoreDisplayMode": "notApplicable"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "manual"}, "bf-cache": {"id": "bf-cache", "title": "Page prevented back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 0, "scoreDisplayMode": "binary", "displayValue": "4 failure reasons", "details": {"type": "table", "headings": [{"key": "reason", "valueType": "text", "subItemsHeading": {"key": "frameUrl", "valueType": "url"}, "label": "Failure reason"}, {"key": "failureType", "valueType": "text", "label": "Failure type"}], "items": [{"reason": "Pages with WebSocket cannot enter back/forward cache.", "failureType": "Pending browser support", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/auth/signin"}]}, "protocolReason": "WebSocket"}, {"reason": "Pages whose main resource has cache-control:no-store cannot enter back/forward cache.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/auth/signin"}]}, "protocolReason": "MainResourceHasCacheControlNoStore"}, {"reason": "Back/forward cache is disabled because some JavaScript network request received resource with Cache-Control: no-store header.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/auth/signin"}]}, "protocolReason": "JsNetworkRequestReceivedCacheControlNoStoreResource"}, {"reason": "Back/forward cache is disabled because WebSocket has been used.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/auth/signin"}]}, "protocolReason": "WebSocketSticky"}]}, "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "Request"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": []}, "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": []}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "Est savings of 1,030 ms", "metricSavings": {"FCP": 1050, "LCP": 1050}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Avoids redirects", "value": true}, "serverResponseIsFast": {"label": "Server responded slowly (observed 1126 ms)", "value": false}, "usesCompression": {"label": "Applies text compression", "value": true}}, "debugData": {"type": "debugdata", "redirectDuration": 0, "serverResponseTime": 1126, "uncompressedResponseBytes": 0, "wastedBytes": 0}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 102}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-4-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body.geist_e531dabc-module__QGiZLq__variable", "boundingRect": {"top": 0, "bottom": 995, "left": 0, "right": 412, "width": 412, "height": 995}, "snippet": "<body class=\"geist_e531dabc-module__QGiZLq__variable geist_mono_68a01160-module__YLcDdW…\">", "nodeLabel": "body.geist_e531dabc-module__QGiZLq__variable"}, "value": {"type": "numeric", "granularity": 1, "value": 18}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-5-path", "path": "0,DIV,0,DIV,0,DIV,1,DIV,0,NAV,0,A,0,SPAN,0,svg,0,path", "selector": "a.text-gray-300 > span.mr-3 > svg.lucide > path", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<path d=\"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34…\">", "nodeLabel": "a.text-gray-300 > span.mr-3 > svg.lucide > path"}, "value": {"type": "numeric", "granularity": 1, "value": 13}}], "debugData": {"type": "debugdata", "totalElements": 102, "maxChildren": 18, "maxDepth": 13}}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "subItemsHeading": {"key": "url", "valueType": "url"}, "label": "Source"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceTransferBytes"}, "granularity": 10, "label": "Duplicated bytes"}], "items": []}, "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "ms", "label": "Est Savings"}], "items": []}, "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "A forced reflow occurs when JavaScript queries geometric properties (such as offsetWidth) after styles have been invalidated by a change to the DOM state. This can result in poor performance. Learn more about [forced reflows](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and possible mitigations.", "score": 0, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "reflowTime", "valueType": "ms", "granularity": 1, "label": "Total reflow time"}], "items": [{"source": {"type": "text", "value": "[unattributed]"}, "reflowTime": 62.554}]}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "inp-breakdown-insight": {"id": "inp-breakdown-insight", "title": "INP breakdown", "description": "Start investigating with the longest subpart. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-breakdown-insight": {"id": "lcp-breakdown-insight", "title": "LCP breakdown", "description": "Each [subpart has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Subpart"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"subpart": "timeToFirstByte", "label": "Time to first byte", "duration": 1128.134}, {"subpart": "elementRenderDelay", "label": "Element render delay", "duration": 580.825}]}, {"type": "node", "lhId": "page-0-P", "path": "1,H<PERSON>L,1,BODY,1,DIV,1,MAIN,2,DIV,0,DIV,1,P", "selector": "main.flex-grow > div > div > p", "boundingRect": {"top": 458, "bottom": 506, "left": 40, "right": 372, "width": 332, "height": 48}, "snippet": "<p style=\"margin-bottom: 24px; color: rgb(102, 102, 102);\">", "nodeLabel": "You need to be signed in to access this page."}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable older browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support older browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Wasted bytes"}], "items": []}, "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}], "items": []}, "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "list-section", "value": {"type": "network-tree", "chains": {"12F1892B7F5D37AF00F54BB54DBB915B": {"url": "http://localhost:3000/admin/analytics", "navStartToEndTime": 1147, "transferSize": 10730, "isLongest": true, "children": {}}}, "longestChain": {"duration": 1147}}}, {"type": "list-section", "title": "Preconnected origins", "description": "[preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) hints help the browser establish a connection earlier in the page load, saving time when the first request for that origin is made. The following are the origins that the page preconnected to.", "value": {"type": "text", "value": "no origins were preconnected"}}, {"type": "list-section", "title": "Preconnect candidates", "description": "Add [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) hints to your most important origins, but try to use no more than 4.", "value": {"type": "text", "value": "No additional origins are good candidates for preconnecting"}}]}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains", "uses-rel-preconnect"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) can move these network requests out of the critical path.", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Duration"}], "items": []}, "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) to prioritize your page's content.", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "3rd party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer size", "subItemsHeading": {"key": "transferSize"}}, {"key": "mainThreadTime", "granularity": 1, "valueType": "ms", "label": "Main thread time", "subItemsHeading": {"key": "mainThreadTime"}}], "items": []}, "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-3-META", "path": "", "selector": "meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">", "nodeLabel": "meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": "json", "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "node", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": ["performance", "accessibility", "best-practices", "seo"], "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "inp-breakdown-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-breakdown-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.65}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 0, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 7, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 0, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 7, "group": "a11y-aria"}, {"id": "aria-text", "weight": 0, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 0, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 0, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 0, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 0, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 0, "group": "a11y-language"}, {"id": "image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "label", "weight": 0, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 0, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 7, "group": "a11y-names-labels"}, {"id": "list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 0, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 0, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 0, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 0, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 7, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 0, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 0, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 0, "group": "a11y-language"}, {"id": "video-caption", "weight": 0, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": 0.79}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "trusted-types-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": 1}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 0, "group": "seo-crawl"}, {"id": "image-alt", "weight": 0, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 0, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": 1}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "localhost", "origins": ["http://localhost:3000"], "isFirstParty": true, "isUnrecognized": true}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 412, "height": 995}, "nodes": {"page-0-P": {"id": "", "top": 544, "bottom": 592, "left": 40, "right": 372, "width": 332, "height": 48}, "page-1-path": {"id": "", "top": 951, "bottom": 965, "left": 33, "right": 44, "width": 11, "height": 14}, "page-2-path": {"id": "", "top": 952, "bottom": 963, "left": 42, "right": 42, "width": 0, "height": 12}, "page-3-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-4-BODY": {"id": "", "top": 0, "bottom": 1167, "left": 0, "right": 412, "width": 412, "height": 1167}, "page-5-path": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-0-BUTTON": {"id": "", "top": 826, "bottom": 846, "left": 359, "right": 379, "width": 20, "height": 20}, "1-1-BUTTON": {"id": "", "top": 926, "bottom": 962, "left": 33, "right": 202, "width": 169, "height": 36}, "1-2-H3": {"id": "", "top": 826, "bottom": 846, "left": 85, "right": 347, "width": 262, "height": 20}, "1-3-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-4-H1": {"id": "", "top": 25, "bottom": 57, "left": 77, "right": 211, "width": 134, "height": 32}, "1-5-A": {"id": "", "top": 17, "bottom": 65, "left": 17, "right": 211, "width": 194, "height": 48}, "1-6-A": {"id": "", "top": 21, "bottom": 61, "left": 259, "right": 365, "width": 106, "height": 40}, "1-7-A": {"id": "", "top": 21, "bottom": 61, "left": 369, "right": 527, "width": 158, "height": 40}, "1-8-A": {"id": "", "top": 9, "bottom": 73, "left": 465, "right": 558, "width": 93, "height": 64}, "1-9-stop": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-10-BODY": {"id": "", "top": 0, "bottom": 1167, "left": 0, "right": 412, "width": 412, "height": 1167}, "1-11-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-12-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-13-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-14-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-15-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-16-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-17-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-18-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-19-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-20-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-21-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-22-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-23-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-24-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-25-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-26-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-27-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-28-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-29-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-30-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-31-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-32-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-33-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-34-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-35-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-36-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-37-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-38-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-39-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-40-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-41-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-42-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-43-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-44-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-45-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-46-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-47-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-48-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-49-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-50-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-51-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-52-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-53-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-54-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 13471.27, "name": "lh:config", "duration": 217.43, "entryType": "measure"}, {"startTime": 13471.83, "name": "lh:config:resolveArtifactsToDefns", "duration": 1.07, "entryType": "measure"}, {"startTime": 13688.77, "name": "lh:runner:gather", "duration": 8623.29, "entryType": "measure"}, {"startTime": 13722.5, "name": "lh:driver:connect", "duration": 3.74, "entryType": "measure"}, {"startTime": 13726.27, "name": "lh:driver:navigate", "duration": 3.54, "entryType": "measure"}, {"startTime": 13729.93, "name": "lh:gather:getBenchmarkIndex", "duration": 1006.26, "entryType": "measure"}, {"startTime": 14736.24, "name": "lh:gather:getVersion", "duration": 1.17, "entryType": "measure"}, {"startTime": 14737.45, "name": "lh:gather:getDevicePixelRatio", "duration": 1.46, "entryType": "measure"}, {"startTime": 14739, "name": "lh:prepare:navigationMode", "duration": 25.54, "entryType": "measure"}, {"startTime": 14756.26, "name": "lh:storage:clearDataForOrigin", "duration": 3.41, "entryType": "measure"}, {"startTime": 14759.7, "name": "lh:storage:clearBrowserCaches", "duration": 3.24, "entryType": "measure"}, {"startTime": 14763.5, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 1.02, "entryType": "measure"}, {"startTime": 14790.96, "name": "lh:driver:navigate", "duration": 4516.46, "entryType": "measure"}, {"startTime": 19842.09, "name": "lh:computed:NetworkRecords", "duration": 0.91, "entryType": "measure"}, {"startTime": 19843.09, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.06, "entryType": "measure"}, {"startTime": 19843.16, "name": "lh:gather:getArtifact:Trace", "duration": 0.02, "entryType": "measure"}, {"startTime": 19843.18, "name": "lh:gather:getArtifact:Accessibility", "duration": 199.67, "entryType": "measure"}, {"startTime": 20042.93, "name": "lh:gather:getArtifact:AnchorElements", "duration": 27.62, "entryType": "measure"}, {"startTime": 20070.59, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.05, "entryType": "measure"}, {"startTime": 20070.65, "name": "lh:gather:getArtifact:CSSUsage", "duration": 282.27, "entryType": "measure"}, {"startTime": 20352.96, "name": "lh:gather:getArtifact:Doctype", "duration": 1.13, "entryType": "measure"}, {"startTime": 20354.11, "name": "lh:gather:getArtifact:DOMStats", "duration": 3.59, "entryType": "measure"}, {"startTime": 20357.72, "name": "lh:gather:getArtifact:FontSize", "duration": 14.98, "entryType": "measure"}, {"startTime": 20372.72, "name": "lh:gather:getArtifact:Inputs", "duration": 2, "entryType": "measure"}, {"startTime": 20374.75, "name": "lh:gather:getArtifact:ImageElements", "duration": 14.29, "entryType": "measure"}, {"startTime": 20389.12, "name": "lh:gather:getArtifact:InspectorIssues", "duration": 0.08, "entryType": "measure"}, {"startTime": 20389.21, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.04, "entryType": "measure"}, {"startTime": 20389.38, "name": "lh:gather:getArtifact:LinkElements", "duration": 3.96, "entryType": "measure"}, {"startTime": 20392.85, "name": "lh:computed:MainResource", "duration": 0.08, "entryType": "measure"}, {"startTime": 20393.4, "name": "lh:gather:getArtifact:MainDocumentContent", "duration": 0.85, "entryType": "measure"}, {"startTime": 20394.26, "name": "lh:gather:getArtifact:MetaElements", "duration": 4.86, "entryType": "measure"}, {"startTime": 20399.21, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.09, "entryType": "measure"}, {"startTime": 20399.32, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.06, "entryType": "measure"}, {"startTime": 20399.41, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 3.1, "entryType": "measure"}, {"startTime": 20402.54, "name": "lh:gather:getArtifact:RobotsTxt", "duration": 58.59, "entryType": "measure"}, {"startTime": 20461.2, "name": "lh:gather:getArtifact:Scripts", "duration": 0.16, "entryType": "measure"}, {"startTime": 20461.41, "name": "lh:gather:getArtifact:SourceMaps", "duration": 164.35, "entryType": "measure"}, {"startTime": 20625.8, "name": "lh:gather:getArtifact:Stacks", "duration": 17.84, "entryType": "measure"}, {"startTime": 20625.92, "name": "lh:gather:collectStacks", "duration": 17.7, "entryType": "measure"}, {"startTime": 20643.66, "name": "lh:gather:getArtifact:Stylesheets", "duration": 15.69, "entryType": "measure"}, {"startTime": 20659.4, "name": "lh:gather:getArtifact:TraceElements", "duration": 253.25, "entryType": "measure"}, {"startTime": 20659.48, "name": "lh:computed:TraceEngineResult", "duration": 227.84, "entryType": "measure"}, {"startTime": 20659.54, "name": "lh:computed:ProcessedTrace", "duration": 23.13, "entryType": "measure"}, {"startTime": 20684.76, "name": "lh:computed:TraceEngineResult:total", "duration": 188.26, "entryType": "measure"}, {"startTime": 20684.93, "name": "lh:computed:TraceEngineResult:parse", "duration": 158.92, "entryType": "measure"}, {"startTime": 20685.16, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 82.95, "entryType": "measure"}, {"startTime": 20768.14, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 1.33, "entryType": "measure"}, {"startTime": 20769.5, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 2.05, "entryType": "measure"}, {"startTime": 20771.6, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 1.3, "entryType": "measure"}, {"startTime": 20772.92, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 1.12, "entryType": "measure"}, {"startTime": 20774.06, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 1.11, "entryType": "measure"}, {"startTime": 20775.19, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 2.72, "entryType": "measure"}, {"startTime": 20777.95, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 16.65, "entryType": "measure"}, {"startTime": 20794.66, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 4.54, "entryType": "measure"}, {"startTime": 20799.26, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 2.95, "entryType": "measure"}, {"startTime": 20802.23, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 1.19, "entryType": "measure"}, {"startTime": 20803.45, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 1.17, "entryType": "measure"}, {"startTime": 20804.63, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 1.24, "entryType": "measure"}, {"startTime": 20805.9, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 1.52, "entryType": "measure"}, {"startTime": 20807.44, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 11.37, "entryType": "measure"}, {"startTime": 20818.85, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 1.25, "entryType": "measure"}, {"startTime": 20820.13, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 1.15, "entryType": "measure"}, {"startTime": 20821.3, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 1.33, "entryType": "measure"}, {"startTime": 20822.65, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 1.21, "entryType": "measure"}, {"startTime": 20823.88, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 1.29, "entryType": "measure"}, {"startTime": 20825.18, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 1.21, "entryType": "measure"}, {"startTime": 20826.4, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 1.12, "entryType": "measure"}, {"startTime": 20827.54, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 3.45, "entryType": "measure"}, {"startTime": 20831.03, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 1.2, "entryType": "measure"}, {"startTime": 20832.24, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 1.15, "entryType": "measure"}, {"startTime": 20833.41, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 1.14, "entryType": "measure"}, {"startTime": 20834.57, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 1.61, "entryType": "measure"}, {"startTime": 20836.21, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 1.16, "entryType": "measure"}, {"startTime": 20837.39, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 1.16, "entryType": "measure"}, {"startTime": 20838.56, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 1.14, "entryType": "measure"}, {"startTime": 20839.72, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 1.19, "entryType": "measure"}, {"startTime": 20840.93, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 2.87, "entryType": "measure"}, {"startTime": 20843.85, "name": "lh:computed:TraceEngineResult:insights", "duration": 29.15, "entryType": "measure"}, {"startTime": 20844, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.14, "entryType": "measure"}, {"startTime": 20844.15, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 0.07, "entryType": "measure"}, {"startTime": 20844.23, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.11, "entryType": "measure"}, {"startTime": 20844.35, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.04, "entryType": "measure"}, {"startTime": 20844.39, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 0.26, "entryType": "measure"}, {"startTime": 20844.66, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.06, "entryType": "measure"}, {"startTime": 20844.73, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.05, "entryType": "measure"}, {"startTime": 20844.8, "name": "lh:computed:TraceEngineResult:insights:INPBreakdown", "duration": 0.03, "entryType": "measure"}, {"startTime": 20844.83, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.05, "entryType": "measure"}, {"startTime": 20844.89, "name": "lh:computed:TraceEngineResult:insights:LCPBreakdown", "duration": 0.03, "entryType": "measure"}, {"startTime": 20844.93, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.03, "entryType": "measure"}, {"startTime": 20844.97, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 0.06, "entryType": "measure"}, {"startTime": 20845.04, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 0.07, "entryType": "measure"}, {"startTime": 20845.12, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 0.11, "entryType": "measure"}, {"startTime": 20845.24, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.05, "entryType": "measure"}, {"startTime": 20845.3, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.06, "entryType": "measure"}, {"startTime": 20845.38, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 0.89, "entryType": "measure"}, {"startTime": 20846.29, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.11, "entryType": "measure"}, {"startTime": 20846.52, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 15.77, "entryType": "measure"}, {"startTime": 20862.35, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.37, "entryType": "measure"}, {"startTime": 20862.73, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 0.22, "entryType": "measure"}, {"startTime": 20862.96, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.1, "entryType": "measure"}, {"startTime": 20863.07, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.13, "entryType": "measure"}, {"startTime": 20863.22, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 0.19, "entryType": "measure"}, {"startTime": 20863.42, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.05, "entryType": "measure"}, {"startTime": 20863.47, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.12, "entryType": "measure"}, {"startTime": 20863.6, "name": "lh:computed:TraceEngineResult:insights:INPBreakdown", "duration": 0.04, "entryType": "measure"}, {"startTime": 20863.64, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.05, "entryType": "measure"}, {"startTime": 20863.7, "name": "lh:computed:TraceEngineResult:insights:LCPBreakdown", "duration": 0.08, "entryType": "measure"}, {"startTime": 20863.79, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.04, "entryType": "measure"}, {"startTime": 20863.84, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 0.08, "entryType": "measure"}, {"startTime": 20863.93, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 1.69, "entryType": "measure"}, {"startTime": 20865.63, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 0.66, "entryType": "measure"}, {"startTime": 20866.31, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.2, "entryType": "measure"}, {"startTime": 20866.52, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.05, "entryType": "measure"}, {"startTime": 20866.58, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 6.03, "entryType": "measure"}, {"startTime": 20872.63, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.18, "entryType": "measure"}, {"startTime": 20891.4, "name": "lh:computed:ProcessedNavigation", "duration": 0.27, "entryType": "measure"}, {"startTime": 20891.69, "name": "lh:computed:CumulativeLayoutShift", "duration": 9.52, "entryType": "measure"}, {"startTime": 20902.06, "name": "lh:computed:Responsiveness", "duration": 0.07, "entryType": "measure"}, {"startTime": 20912.68, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.85, "entryType": "measure"}, {"startTime": 20913.55, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1079.71, "entryType": "measure"}, {"startTime": 21993.3, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 300.92, "entryType": "measure"}, {"startTime": 22312.18, "name": "lh:runner:audit", "duration": 1102.51, "entryType": "measure"}, {"startTime": 22312.2, "name": "lh:runner:auditing", "duration": 1102.28, "entryType": "measure"}, {"startTime": 22312.92, "name": "lh:audit:is-on-https", "duration": 0.84, "entryType": "measure"}, {"startTime": 22314, "name": "lh:audit:redirects-http", "duration": 0.66, "entryType": "measure"}, {"startTime": 22314.85, "name": "lh:audit:viewport", "duration": 0.8, "entryType": "measure"}, {"startTime": 22315.08, "name": "lh:computed:ViewportMeta", "duration": 0.13, "entryType": "measure"}, {"startTime": 22315.83, "name": "lh:audit:first-contentful-paint", "duration": 7.48, "entryType": "measure"}, {"startTime": 22316.06, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 6.53, "entryType": "measure"}, {"startTime": 22316.11, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 6.47, "entryType": "measure"}, {"startTime": 22316.13, "name": "lh:computed:PageDependencyGraph", "duration": 5.61, "entryType": "measure"}, {"startTime": 22321.8, "name": "lh:computed:LoadSimulator", "duration": 0.28, "entryType": "measure"}, {"startTime": 22321.82, "name": "lh:computed:NetworkAnalysis", "duration": 0.23, "entryType": "measure"}, {"startTime": 22323.47, "name": "lh:audit:largest-contentful-paint", "duration": 2.33, "entryType": "measure"}, {"startTime": 22323.74, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 1.55, "entryType": "measure"}, {"startTime": 22323.8, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 1.48, "entryType": "measure"}, {"startTime": 22326.1, "name": "lh:audit:first-meaningful-paint", "duration": 0.6, "entryType": "measure"}, {"startTime": 22326.86, "name": "lh:audit:speed-index", "duration": 143.35, "entryType": "measure"}, {"startTime": 22327.06, "name": "lh:computed:SpeedIndex", "duration": 142.51, "entryType": "measure"}, {"startTime": 22327.11, "name": "lh:computed:LanternSpeedIndex", "duration": 142.44, "entryType": "measure"}, {"startTime": 22327.13, "name": "lh:computed:Speedline", "duration": 140.87, "entryType": "measure"}, {"startTime": 22470.23, "name": "lh:audit:screenshot-thumbnails", "duration": 0.18, "entryType": "measure"}, {"startTime": 22470.42, "name": "lh:audit:final-screenshot", "duration": 1.15, "entryType": "measure"}, {"startTime": 22470.47, "name": "lh:computed:Screenshots", "duration": 1.07, "entryType": "measure"}, {"startTime": 22471.75, "name": "lh:audit:total-blocking-time", "duration": 3.58, "entryType": "measure"}, {"startTime": 22471.99, "name": "lh:computed:TotalBlockingTime", "duration": 2.73, "entryType": "measure"}, {"startTime": 22472.03, "name": "lh:computed:LanternTotalBlockingTime", "duration": 2.68, "entryType": "measure"}, {"startTime": 22472.05, "name": "lh:computed:LanternInteractive", "duration": 1.4, "entryType": "measure"}, {"startTime": 22475.49, "name": "lh:audit:max-potential-fid", "duration": 2.61, "entryType": "measure"}, {"startTime": 22475.71, "name": "lh:computed:MaxPotentialFID", "duration": 1.35, "entryType": "measure"}, {"startTime": 22475.75, "name": "lh:computed:LanternMaxPotentialFID", "duration": 1.3, "entryType": "measure"}, {"startTime": 22478.25, "name": "lh:audit:cumulative-layout-shift", "duration": 0.56, "entryType": "measure"}, {"startTime": 22478.96, "name": "lh:audit:errors-in-console", "duration": 0.61, "entryType": "measure"}, {"startTime": 22479.15, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.02, "entryType": "measure"}, {"startTime": 22479.72, "name": "lh:audit:server-response-time", "duration": 0.83, "entryType": "measure"}, {"startTime": 22480.7, "name": "lh:audit:interactive", "duration": 0.68, "entryType": "measure"}, {"startTime": 22480.89, "name": "lh:computed:Interactive", "duration": 0.05, "entryType": "measure"}, {"startTime": 22481.52, "name": "lh:audit:user-timings", "duration": 1.72, "entryType": "measure"}, {"startTime": 22481.67, "name": "lh:computed:UserTimings", "duration": 1.18, "entryType": "measure"}, {"startTime": 22483.38, "name": "lh:audit:critical-request-chains", "duration": 0.63, "entryType": "measure"}, {"startTime": 22483.56, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 0.14, "entryType": "measure"}, {"startTime": 22484.12, "name": "lh:audit:redirects", "duration": 2.32, "entryType": "measure"}, {"startTime": 22486.64, "name": "lh:audit:image-aspect-ratio", "duration": 0.6, "entryType": "measure"}, {"startTime": 22487.4, "name": "lh:audit:image-size-responsive", "duration": 0.73, "entryType": "measure"}, {"startTime": 22487.6, "name": "lh:computed:ImageRecords", "duration": 0.04, "entryType": "measure"}, {"startTime": 22488.29, "name": "lh:audit:deprecations", "duration": 0.55, "entryType": "measure"}, {"startTime": 22488.99, "name": "lh:audit:third-party-cookies", "duration": 0.53, "entryType": "measure"}, {"startTime": 22489.68, "name": "lh:audit:mainthread-work-breakdown", "duration": 17.46, "entryType": "measure"}, {"startTime": 22489.91, "name": "lh:computed:MainThreadTasks", "duration": 15.92, "entryType": "measure"}, {"startTime": 22507.36, "name": "lh:audit:bootup-time", "duration": 32.63, "entryType": "measure"}, {"startTime": 22508.67, "name": "lh:computed:TBTImpactTasks", "duration": 27.59, "entryType": "measure"}, {"startTime": 22540.18, "name": "lh:audit:uses-rel-preconnect", "duration": 1.42, "entryType": "measure"}, {"startTime": 22541.78, "name": "lh:audit:font-display", "duration": 1.71, "entryType": "measure"}, {"startTime": 22543.53, "name": "lh:audit:diagnostics", "duration": 0.48, "entryType": "measure"}, {"startTime": 22544.02, "name": "lh:audit:network-requests", "duration": 0.88, "entryType": "measure"}, {"startTime": 22544.08, "name": "lh:computed:EntityClassification", "duration": 0.52, "entryType": "measure"}, {"startTime": 22545.08, "name": "lh:audit:network-rtt", "duration": 0.89, "entryType": "measure"}, {"startTime": 22546.12, "name": "lh:audit:network-server-latency", "duration": 0.65, "entryType": "measure"}, {"startTime": 22546.79, "name": "lh:audit:main-thread-tasks", "duration": 0.26, "entryType": "measure"}, {"startTime": 22547.07, "name": "lh:audit:metrics", "duration": 2.13, "entryType": "measure"}, {"startTime": 22547.21, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 1.92, "entryType": "measure"}, {"startTime": 22547.28, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.08, "entryType": "measure"}, {"startTime": 22547.38, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.05, "entryType": "measure"}, {"startTime": 22547.51, "name": "lh:computed:LCPBreakdown", "duration": 1.53, "entryType": "measure"}, {"startTime": 22547.54, "name": "lh:computed:TimeToFirstByte", "duration": 0.07, "entryType": "measure"}, {"startTime": 22547.62, "name": "lh:computed:LCPImageRecord", "duration": 1.39, "entryType": "measure"}, {"startTime": 22549.21, "name": "lh:audit:resource-summary", "duration": 0.75, "entryType": "measure"}, {"startTime": 22549.32, "name": "lh:computed:ResourceSummary", "duration": 0.13, "entryType": "measure"}, {"startTime": 22550.19, "name": "lh:audit:third-party-summary", "duration": 6.1, "entryType": "measure"}, {"startTime": 22556.55, "name": "lh:audit:third-party-facades", "duration": 6.43, "entryType": "measure"}, {"startTime": 22563.13, "name": "lh:audit:largest-contentful-paint-element", "duration": 1.08, "entryType": "measure"}, {"startTime": 22564.42, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.84, "entryType": "measure"}, {"startTime": 22565.47, "name": "lh:audit:layout-shifts", "duration": 0.84, "entryType": "measure"}, {"startTime": 22566.5, "name": "lh:audit:long-tasks", "duration": 7.21, "entryType": "measure"}, {"startTime": 22573.92, "name": "lh:audit:non-composited-animations", "duration": 1.07, "entryType": "measure"}, {"startTime": 22575.17, "name": "lh:audit:unsized-images", "duration": 0.64, "entryType": "measure"}, {"startTime": 22576, "name": "lh:audit:valid-source-maps", "duration": 1.13, "entryType": "measure"}, {"startTime": 22577.27, "name": "lh:audit:prioritize-lcp-image", "duration": 0.42, "entryType": "measure"}, {"startTime": 22577.83, "name": "lh:audit:csp-xss", "duration": 1.08, "entryType": "measure"}, {"startTime": 22579.08, "name": "lh:audit:has-hsts", "duration": 0.53, "entryType": "measure"}, {"startTime": 22579.74, "name": "lh:audit:origin-isolation", "duration": 0.58, "entryType": "measure"}, {"startTime": 22580.45, "name": "lh:audit:clickjacking-mitigation", "duration": 0.48, "entryType": "measure"}, {"startTime": 22581.06, "name": "lh:audit:trusted-types-xss", "duration": 0.57, "entryType": "measure"}, {"startTime": 22581.65, "name": "lh:audit:script-treemap-data", "duration": 29.62, "entryType": "measure"}, {"startTime": 22581.74, "name": "lh:computed:ModuleDuplication", "duration": 0.04, "entryType": "measure"}, {"startTime": 22581.8, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.01, "entryType": "measure"}, {"startTime": 22581.87, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.18, "entryType": "measure"}, {"startTime": 22582.15, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 22582.2, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.23, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.01, "entryType": "measure"}, {"startTime": 22582.25, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.29, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.01, "entryType": "measure"}, {"startTime": 22582.32, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.36, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.39, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.43, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.47, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 22582.51, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 22582.56, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 22582.62, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 22582.67, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.73, "entryType": "measure"}, {"startTime": 22583.43, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.04, "entryType": "measure"}, {"startTime": 22583.5, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.12, "entryType": "measure"}, {"startTime": 22584.65, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 22584.71, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.32, "entryType": "measure"}, {"startTime": 22585.06, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.64, "entryType": "measure"}, {"startTime": 22585.75, "name": "lh:computed:UnusedJavascriptSummary", "duration": 2.11, "entryType": "measure"}, {"startTime": 22587.9, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 22587.97, "name": "lh:computed:UnusedJavascriptSummary", "duration": 3.69, "entryType": "measure"}, {"startTime": 22591.7, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.04, "entryType": "measure"}, {"startTime": 22591.78, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.41, "entryType": "measure"}, {"startTime": 22592.22, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.49, "entryType": "measure"}, {"startTime": 22592.75, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.71, "entryType": "measure"}, {"startTime": 22593.5, "name": "lh:computed:UnusedJavascriptSummary", "duration": 6.44, "entryType": "measure"}, {"startTime": 22600, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 22600.17, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 22600.33, "name": "lh:computed:UnusedJavascriptSummary", "duration": 4.47, "entryType": "measure"}, {"startTime": 22604.87, "name": "lh:computed:UnusedJavascriptSummary", "duration": 5.55, "entryType": "measure"}, {"startTime": 22610.48, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.05, "entryType": "measure"}, {"startTime": 22610.58, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.12, "entryType": "measure"}, {"startTime": 22610.75, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.05, "entryType": "measure"}, {"startTime": 22610.85, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 22610.98, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.05, "entryType": "measure"}, {"startTime": 22611.08, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 22611.54, "name": "lh:audit:accesskeys", "duration": 0.8, "entryType": "measure"}, {"startTime": 22612.56, "name": "lh:audit:aria-allowed-attr", "duration": 5.97, "entryType": "measure"}, {"startTime": 22618.73, "name": "lh:audit:aria-allowed-role", "duration": 2.45, "entryType": "measure"}, {"startTime": 22621.36, "name": "lh:audit:aria-command-name", "duration": 0.65, "entryType": "measure"}, {"startTime": 22622.19, "name": "lh:audit:aria-conditional-attr", "duration": 2.5, "entryType": "measure"}, {"startTime": 22624.86, "name": "lh:audit:aria-deprecated-role", "duration": 2.33, "entryType": "measure"}, {"startTime": 22627.35, "name": "lh:audit:aria-dialog-name", "duration": 0.65, "entryType": "measure"}, {"startTime": 22628.16, "name": "lh:audit:aria-hidden-body", "duration": 2.44, "entryType": "measure"}, {"startTime": 22630.77, "name": "lh:audit:aria-hidden-focus", "duration": 2.42, "entryType": "measure"}, {"startTime": 22633.35, "name": "lh:audit:aria-input-field-name", "duration": 0.69, "entryType": "measure"}, {"startTime": 22634.2, "name": "lh:audit:aria-meter-name", "duration": 0.86, "entryType": "measure"}, {"startTime": 22635.28, "name": "lh:audit:aria-progressbar-name", "duration": 0.76, "entryType": "measure"}, {"startTime": 22636.19, "name": "lh:audit:aria-prohibited-attr", "duration": 2.39, "entryType": "measure"}, {"startTime": 22638.76, "name": "lh:audit:aria-required-attr", "duration": 6.47, "entryType": "measure"}, {"startTime": 22645.45, "name": "lh:audit:aria-required-children", "duration": 1.1, "entryType": "measure"}, {"startTime": 22646.74, "name": "lh:audit:aria-required-parent", "duration": 0.87, "entryType": "measure"}, {"startTime": 22647.77, "name": "lh:audit:aria-roles", "duration": 2.34, "entryType": "measure"}, {"startTime": 22650.28, "name": "lh:audit:aria-text", "duration": 0.94, "entryType": "measure"}, {"startTime": 22651.38, "name": "lh:audit:aria-toggle-field-name", "duration": 0.95, "entryType": "measure"}, {"startTime": 22652.5, "name": "lh:audit:aria-tooltip-name", "duration": 0.98, "entryType": "measure"}, {"startTime": 22653.65, "name": "lh:audit:aria-treeitem-name", "duration": 1.01, "entryType": "measure"}, {"startTime": 22654.82, "name": "lh:audit:aria-valid-attr-value", "duration": 2.33, "entryType": "measure"}, {"startTime": 22657.31, "name": "lh:audit:aria-valid-attr", "duration": 2.39, "entryType": "measure"}, {"startTime": 22659.87, "name": "lh:audit:button-name", "duration": 7.13, "entryType": "measure"}, {"startTime": 22667.28, "name": "lh:audit:bypass", "duration": 2.52, "entryType": "measure"}, {"startTime": 22670, "name": "lh:audit:color-contrast", "duration": 2.36, "entryType": "measure"}, {"startTime": 22672.55, "name": "lh:audit:definition-list", "duration": 1.14, "entryType": "measure"}, {"startTime": 22673.87, "name": "lh:audit:dlitem", "duration": 1.08, "entryType": "measure"}, {"startTime": 22675.11, "name": "lh:audit:document-title", "duration": 2.34, "entryType": "measure"}, {"startTime": 22677.62, "name": "lh:audit:duplicate-id-aria", "duration": 1.14, "entryType": "measure"}, {"startTime": 22678.92, "name": "lh:audit:empty-heading", "duration": 2.41, "entryType": "measure"}, {"startTime": 22681.52, "name": "lh:audit:form-field-multiple-labels", "duration": 1.16, "entryType": "measure"}, {"startTime": 22682.84, "name": "lh:audit:frame-title", "duration": 1.12, "entryType": "measure"}, {"startTime": 22684.11, "name": "lh:audit:heading-order", "duration": 6.97, "entryType": "measure"}, {"startTime": 22691.3, "name": "lh:audit:html-has-lang", "duration": 2.66, "entryType": "measure"}, {"startTime": 22694.15, "name": "lh:audit:html-lang-valid", "duration": 2.4, "entryType": "measure"}, {"startTime": 22696.72, "name": "lh:audit:html-xml-lang-mismatch", "duration": 1.38, "entryType": "measure"}, {"startTime": 22698.27, "name": "lh:audit:identical-links-same-purpose", "duration": 2.39, "entryType": "measure"}, {"startTime": 22700.83, "name": "lh:audit:image-alt", "duration": 1.28, "entryType": "measure"}, {"startTime": 22702.28, "name": "lh:audit:image-redundant-alt", "duration": 1.34, "entryType": "measure"}, {"startTime": 22703.77, "name": "lh:audit:input-button-name", "duration": 1.39, "entryType": "measure"}, {"startTime": 22705.33, "name": "lh:audit:input-image-alt", "duration": 8.77, "entryType": "measure"}, {"startTime": 22714.7, "name": "lh:audit:label-content-name-mismatch", "duration": 3.67, "entryType": "measure"}, {"startTime": 22718.79, "name": "lh:audit:label", "duration": 4.21, "entryType": "measure"}, {"startTime": 22723.54, "name": "lh:audit:landmark-one-main", "duration": 5.42, "entryType": "measure"}, {"startTime": 22729.51, "name": "lh:audit:link-name", "duration": 5.48, "entryType": "measure"}, {"startTime": 22735.33, "name": "lh:audit:link-in-text-block", "duration": 9.89, "entryType": "measure"}, {"startTime": 22745.47, "name": "lh:audit:list", "duration": 1.95, "entryType": "measure"}, {"startTime": 22747.62, "name": "lh:audit:listitem", "duration": 1.83, "entryType": "measure"}, {"startTime": 22749.67, "name": "lh:audit:meta-refresh", "duration": 2.04, "entryType": "measure"}, {"startTime": 22752.07, "name": "lh:audit:meta-viewport", "duration": 3.18, "entryType": "measure"}, {"startTime": 22755.47, "name": "lh:audit:object-alt", "duration": 1.99, "entryType": "measure"}, {"startTime": 22757.66, "name": "lh:audit:select-name", "duration": 1.92, "entryType": "measure"}, {"startTime": 22759.76, "name": "lh:audit:skip-link", "duration": 1.79, "entryType": "measure"}, {"startTime": 22761.72, "name": "lh:audit:tabindex", "duration": 1.88, "entryType": "measure"}, {"startTime": 22763.83, "name": "lh:audit:table-duplicate-name", "duration": 2.06, "entryType": "measure"}, {"startTime": 22766.08, "name": "lh:audit:table-fake-caption", "duration": 1.99, "entryType": "measure"}, {"startTime": 22768.23, "name": "lh:audit:target-size", "duration": 2.95, "entryType": "measure"}, {"startTime": 22771.4, "name": "lh:audit:td-has-header", "duration": 6.89, "entryType": "measure"}, {"startTime": 22778.65, "name": "lh:audit:td-headers-attr", "duration": 2.39, "entryType": "measure"}, {"startTime": 22781.28, "name": "lh:audit:th-has-data-cells", "duration": 2.14, "entryType": "measure"}, {"startTime": 22783.59, "name": "lh:audit:valid-lang", "duration": 2.37, "entryType": "measure"}, {"startTime": 22786.2, "name": "lh:audit:video-caption", "duration": 2.35, "entryType": "measure"}, {"startTime": 22788.59, "name": "lh:audit:custom-controls-labels", "duration": 0.07, "entryType": "measure"}, {"startTime": 22788.68, "name": "lh:audit:custom-controls-roles", "duration": 0.03, "entryType": "measure"}, {"startTime": 22788.73, "name": "lh:audit:focus-traps", "duration": 0.03, "entryType": "measure"}, {"startTime": 22788.76, "name": "lh:audit:focusable-controls", "duration": 0.02, "entryType": "measure"}, {"startTime": 22788.79, "name": "lh:audit:interactive-element-affordance", "duration": 0.03, "entryType": "measure"}, {"startTime": 22788.82, "name": "lh:audit:logical-tab-order", "duration": 0.02, "entryType": "measure"}, {"startTime": 22788.85, "name": "lh:audit:managed-focus", "duration": 0.02, "entryType": "measure"}, {"startTime": 22788.88, "name": "lh:audit:offscreen-content-hidden", "duration": 0.02, "entryType": "measure"}, {"startTime": 22788.91, "name": "lh:audit:use-landmarks", "duration": 0.02, "entryType": "measure"}, {"startTime": 22788.94, "name": "lh:audit:visual-order-follows-dom", "duration": 0.02, "entryType": "measure"}, {"startTime": 22789.17, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.3, "entryType": "measure"}, {"startTime": 22790.65, "name": "lh:audit:total-byte-weight", "duration": 0.84, "entryType": "measure"}, {"startTime": 22791.65, "name": "lh:audit:offscreen-images", "duration": 7.82, "entryType": "measure"}, {"startTime": 22799.66, "name": "lh:audit:render-blocking-resources", "duration": 16.85, "entryType": "measure"}, {"startTime": 22800, "name": "lh:computed:UnusedCSS", "duration": 15.6, "entryType": "measure"}, {"startTime": 22815.69, "name": "lh:computed:NavigationInsights", "duration": 0.05, "entryType": "measure"}, {"startTime": 22815.82, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.08, "entryType": "measure"}, {"startTime": 22816.69, "name": "lh:audit:unminified-css", "duration": 22.18, "entryType": "measure"}, {"startTime": 22839.05, "name": "lh:audit:unminified-javascript", "duration": 101.91, "entryType": "measure"}, {"startTime": 22941.16, "name": "lh:audit:unused-css-rules", "duration": 2.01, "entryType": "measure"}, {"startTime": 22943.36, "name": "lh:audit:unused-javascript", "duration": 3.53, "entryType": "measure"}, {"startTime": 22947.08, "name": "lh:audit:modern-image-formats", "duration": 2.1, "entryType": "measure"}, {"startTime": 22949.33, "name": "lh:audit:uses-optimized-images", "duration": 1.88, "entryType": "measure"}, {"startTime": 22951.36, "name": "lh:audit:uses-text-compression", "duration": 2.85, "entryType": "measure"}, {"startTime": 22954.36, "name": "lh:audit:uses-responsive-images", "duration": 1.81, "entryType": "measure"}, {"startTime": 22956.31, "name": "lh:audit:efficient-animated-content", "duration": 1.83, "entryType": "measure"}, {"startTime": 22958.27, "name": "lh:audit:duplicated-javascript", "duration": 1.77, "entryType": "measure"}, {"startTime": 22960.19, "name": "lh:audit:legacy-javascript", "duration": 408.67, "entryType": "measure"}, {"startTime": 23369.06, "name": "lh:audit:doctype", "duration": 0.62, "entryType": "measure"}, {"startTime": 23369.88, "name": "lh:audit:charset", "duration": 0.71, "entryType": "measure"}, {"startTime": 23370.77, "name": "lh:audit:dom-size", "duration": 2.66, "entryType": "measure"}, {"startTime": 23373.62, "name": "lh:audit:geolocation-on-start", "duration": 0.68, "entryType": "measure"}, {"startTime": 23374.47, "name": "lh:audit:inspector-issues", "duration": 0.55, "entryType": "measure"}, {"startTime": 23375.18, "name": "lh:audit:no-document-write", "duration": 0.59, "entryType": "measure"}, {"startTime": 23375.9, "name": "lh:audit:js-libraries", "duration": 0.44, "entryType": "measure"}, {"startTime": 23376.5, "name": "lh:audit:notification-on-start", "duration": 0.57, "entryType": "measure"}, {"startTime": 23377.25, "name": "lh:audit:paste-preventing-inputs", "duration": 0.55, "entryType": "measure"}, {"startTime": 23377.94, "name": "lh:audit:uses-http2", "duration": 2.09, "entryType": "measure"}, {"startTime": 23380.2, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.62, "entryType": "measure"}, {"startTime": 23380.98, "name": "lh:audit:meta-description", "duration": 0.54, "entryType": "measure"}, {"startTime": 23381.69, "name": "lh:audit:http-status-code", "duration": 0.58, "entryType": "measure"}, {"startTime": 23382.44, "name": "lh:audit:font-size", "duration": 0.93, "entryType": "measure"}, {"startTime": 23383.53, "name": "lh:audit:link-text", "duration": 0.62, "entryType": "measure"}, {"startTime": 23384.32, "name": "lh:audit:crawlable-anchors", "duration": 0.73, "entryType": "measure"}, {"startTime": 23385.24, "name": "lh:audit:is-crawlable", "duration": 0.59, "entryType": "measure"}, {"startTime": 23386.02, "name": "lh:audit:robots-txt", "duration": 0.5, "entryType": "measure"}, {"startTime": 23386.67, "name": "lh:audit:hreflang", "duration": 0.52, "entryType": "measure"}, {"startTime": 23387.35, "name": "lh:audit:canonical", "duration": 0.54, "entryType": "measure"}, {"startTime": 23388.04, "name": "lh:audit:structured-data", "duration": 0.38, "entryType": "measure"}, {"startTime": 23388.59, "name": "lh:audit:bf-cache", "duration": 0.93, "entryType": "measure"}, {"startTime": 23389.68, "name": "lh:audit:cache-insight", "duration": 0.7, "entryType": "measure"}, {"startTime": 23390.54, "name": "lh:audit:cls-culprits-insight", "duration": 0.65, "entryType": "measure"}, {"startTime": 23391.34, "name": "lh:audit:document-latency-insight", "duration": 0.76, "entryType": "measure"}, {"startTime": 23395.18, "name": "lh:audit:dom-size-insight", "duration": 0.95, "entryType": "measure"}, {"startTime": 23396.35, "name": "lh:audit:duplicated-javascript-insight", "duration": 0.67, "entryType": "measure"}, {"startTime": 23397.26, "name": "lh:audit:font-display-insight", "duration": 0.8, "entryType": "measure"}, {"startTime": 23398.25, "name": "lh:audit:forced-reflow-insight", "duration": 0.74, "entryType": "measure"}, {"startTime": 23399.16, "name": "lh:audit:image-delivery-insight", "duration": 0.55, "entryType": "measure"}, {"startTime": 23399.88, "name": "lh:audit:inp-breakdown-insight", "duration": 0.54, "entryType": "measure"}, {"startTime": 23400.59, "name": "lh:audit:lcp-breakdown-insight", "duration": 0.74, "entryType": "measure"}, {"startTime": 23401.51, "name": "lh:audit:lcp-discovery-insight", "duration": 0.55, "entryType": "measure"}, {"startTime": 23402.25, "name": "lh:audit:legacy-javascript-insight", "duration": 0.66, "entryType": "measure"}, {"startTime": 23403.07, "name": "lh:audit:modern-http-insight", "duration": 0.59, "entryType": "measure"}, {"startTime": 23403.84, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.75, "entryType": "measure"}, {"startTime": 23404.76, "name": "lh:audit:render-blocking-insight", "duration": 0.67, "entryType": "measure"}, {"startTime": 23405.59, "name": "lh:audit:third-parties-insight", "duration": 8.01, "entryType": "measure"}, {"startTime": 23413.79, "name": "lh:audit:viewport-insight", "duration": 0.67, "entryType": "measure"}, {"startTime": 23414.49, "name": "lh:runner:generate", "duration": 0.19, "entryType": "measure"}], "total": 9725.800000000001}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/is-on-https.js | columnInsecureURL": ["audits[is-on-https].details.headings[0].label"], "core/audits/is-on-https.js | columnResolution": ["audits[is-on-https].details.headings[1].label"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 916.96}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 7575.************}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 3328.8074857354877}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 7575.************}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 2429.6159999999973}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 1390.9400000000007}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 395}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 232}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 0.16500000000000004}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 8.48}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 7575.************}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | title": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/lib/i18n/i18n.js | columnSource": ["audits[errors-in-console].details.headings[0].label", "audits.deprecations.details.headings[1].label", "audits[geolocation-on-start].details.headings[0].label", "audits[no-document-write].details.headings[0].label", "audits[notification-on-start].details.headings[0].label", "audits[uses-passive-event-listeners].details.headings[0].label", "audits[font-size].details.headings[0].label", "audits[forced-reflow-insight].details.items[0].headings[0].label"], "core/lib/i18n/i18n.js | columnDescription": ["audits[errors-in-console].details.headings[1].label", "audits[csp-xss].details.headings[0].label", "audits[has-hsts].details.headings[0].label", "audits[origin-isolation].details.headings[0].label", "audits[clickjacking-mitigation].details.headings[0].label", "audits[trusted-types-xss].details.headings[0].label"], "core/audits/server-response-time.js | failureTitle": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 1125.98}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits[image-aspect-ratio].details.headings[1].label", "audits[image-size-responsive].details.headings[1].label", "audits[third-party-cookies].details.headings[1].label", "audits[bootup-time].details.headings[0].label", "audits[font-display].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[long-tasks].details.headings[0].label", "audits[unsized-images].details.headings[1].label", "audits[valid-source-maps].details.headings[0].label", "audits[uses-long-cache-ttl].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[unminified-css].details.headings[0].label", "audits[unminified-javascript].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[legacy-javascript].details.headings[0].label", "audits[font-display-insight].details.headings[0].label", "audits[legacy-javascript-insight].details.headings[0].label", "audits[modern-http-insight].details.headings[0].label", "audits[render-blocking-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/lib/i18n/i18n.js | columnName": ["audits[user-timings].details.headings[0].label", "audits[third-party-cookies].details.headings[0].label", "audits[non-composited-animations].details.headings[1].label"], "core/audits/user-timings.js | columnType": ["audits[user-timings].details.headings[1].label"], "core/lib/i18n/i18n.js | columnStartTime": ["audits[user-timings].details.headings[2].label", "audits[long-tasks].details.headings[1].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[user-timings].details.headings[3].label", "audits[long-tasks].details.headings[2].label", "audits[lcp-breakdown-insight].details.items[0].headings[1].label", "audits[render-blocking-insight].details.headings[2].label"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-aspect-ratio.js | columnDisplayed": ["audits[image-aspect-ratio].details.headings[2].label"], "core/audits/image-aspect-ratio.js | columnActual": ["audits[image-aspect-ratio].details.headings[3].label"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/image-size-responsive.js | columnDisplayed": ["audits[image-size-responsive].details.headings[2].label"], "core/audits/image-size-responsive.js | columnActual": ["audits[image-size-responsive].details.headings[3].label"], "core/audits/image-size-responsive.js | columnExpected": ["audits[image-size-responsive].details.headings[4].label"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/deprecations.js | columnDeprecate": ["audits.deprecations.details.headings[0].label"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | failureTitle": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | failureTitle": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[font-display].details.headings[1].label", "audits[unminified-css].details.headings[2].label", "audits[unminified-javascript].details.headings[2].label", "audits[unused-javascript].details.headings[2].label", "audits[legacy-javascript].details.headings[2].label", "audits[font-display-insight].details.headings[1].label"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[uses-long-cache-ttl].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[unminified-css].details.headings[1].label", "audits[unminified-javascript].details.headings[1].label", "audits[unused-javascript].details.headings[1].label", "audits[cache-insight].details.headings[2].label", "audits[render-blocking-insight].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[layout-shifts].details.headings[0].label", "audits[non-composited-animations].details.headings[0].label", "audits[dom-size].details.headings[1].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/layout-shifts.js | columnScore": ["audits[layout-shifts].details.headings[1].label"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/long-tasks.js | displayValue": [{"values": {"itemCount": 6}, "path": "audits[long-tasks].displayValue"}], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/non-composited-animations.js | displayValue": [{"values": {"itemCount": 2}, "path": "audits[non-composited-animations].displayValue"}], "core/audits/non-composited-animations.js | unsupportedCSSProperty": [{"values": {"propertyCount": 1, "properties": "stroke-dashoffset"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[0].failureReason"}, {"values": {"propertyCount": 1, "properties": "stroke-dashoffset"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[1].failureReason"}, {"values": {"propertyCount": 1, "properties": "stroke-dashoffset"}, "path": "audits[non-composited-animations].details.items[1].subItems.items[0].failureReason"}, {"values": {"propertyCount": 1, "properties": "stroke-dashoffset"}, "path": "audits[non-composited-animations].details.items[1].subItems.items[1].failureReason"}], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | failureTitle": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/valid-source-maps.js | columnMapURL": ["audits[valid-source-maps].details.headings[1].label"], "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": ["audits[valid-source-maps].details.items[0].subItems.items[0].error", "audits[valid-source-maps].details.items[1].subItems.items[0].error", "audits[valid-source-maps].details.items[2].subItems.items[0].error", "audits[valid-source-maps].details.items[3].subItems.items[0].error"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/csp-xss.js | columnDirective": ["audits[csp-xss].details.headings[1].label"], "core/audits/csp-xss.js | columnSeverity": ["audits[csp-xss].details.headings[2].label"], "core/lib/csp-evaluator.js | unsafeInline": ["audits[csp-xss].details.items[0].description"], "core/lib/i18n/i18n.js | itemSeverityHigh": ["audits[csp-xss].details.items[0].severity", "audits[origin-isolation].details.items[0].severity", "audits[trusted-types-xss].details.items[0].severity"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/has-hsts.js | columnDirective": ["audits[has-hsts].details.headings[1].label"], "core/audits/has-hsts.js | columnSeverity": ["audits[has-hsts].details.headings[2].label"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/origin-isolation.js | columnDirective": ["audits[origin-isolation].details.headings[1].label"], "core/audits/origin-isolation.js | columnSeverity": ["audits[origin-isolation].details.headings[2].label"], "core/audits/origin-isolation.js | noCoop": ["audits[origin-isolation].details.items[0].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/clickjacking-mitigation.js | columnSeverity": ["audits[clickjacking-mitigation].details.headings[1].label"], "core/audits/trusted-types-xss.js | title": ["audits[trusted-types-xss].title"], "core/audits/trusted-types-xss.js | description": ["audits[trusted-types-xss].description"], "core/audits/trusted-types-xss.js | columnSeverity": ["audits[trusted-types-xss].details.headings[1].label"], "core/audits/trusted-types-xss.js | noTrustedTypesToMitigateXss": ["audits[trusted-types-xss].details.items[0].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/lib/i18n/i18n.js | columnFailingElem": ["audits[aria-allowed-attr].details.headings[0].label", "audits[aria-allowed-role].details.headings[0].label", "audits[aria-conditional-attr].details.headings[0].label", "audits[aria-deprecated-role].details.headings[0].label", "audits[aria-hidden-body].details.headings[0].label", "audits[aria-hidden-focus].details.headings[0].label", "audits[aria-prohibited-attr].details.headings[0].label", "audits[aria-required-attr].details.headings[0].label", "audits[aria-roles].details.headings[0].label", "audits[aria-valid-attr-value].details.headings[0].label", "audits[aria-valid-attr].details.headings[0].label", "audits[button-name].details.headings[0].label", "audits[color-contrast].details.headings[0].label", "audits[document-title].details.headings[0].label", "audits[heading-order].details.headings[0].label", "audits[html-has-lang].details.headings[0].label", "audits[html-lang-valid].details.headings[0].label", "audits[link-name].details.headings[0].label", "audits[meta-viewport].details.headings[0].label", "audits[target-size].details.headings[0].label", "audits[paste-preventing-inputs].details.headings[0].label"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | failureTitle": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | failureTitle": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | failureTitle": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | failureTitle": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | title": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 0}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/lib/i18n/i18n.js | columnCacheTTL": ["audits[uses-long-cache-ttl].details.headings[1].label", "audits[cache-insight].details.headings[1].label"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 1054398}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 3939}, "path": "audits[unminified-css].displayValue"}, {"values": {"wastedBytes": 307405}, "path": "audits[unminified-javascript].displayValue"}, {"values": {"wastedBytes": 430566}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 8773}, "path": "audits[legacy-javascript].displayValue"}], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 101}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/inspector-issues.js | columnIssueType": ["audits[inspector-issues].details.headings[0].label"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/font-size.js | displayValue": [{"values": {"decimalProportion": 1}, "path": "audits[font-size].displayValue"}], "core/audits/seo/font-size.js | columnSelector": ["audits[font-size].details.headings[1].label"], "core/audits/seo/font-size.js | columnPercentPageText": ["audits[font-size].details.headings[2].label"], "core/audits/seo/font-size.js | columnFontSize": ["audits[font-size].details.headings[3].label"], "core/audits/seo/font-size.js | legibleText": ["audits[font-size].details.items[0].source.value"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/crawlable-anchors.js | columnFailingLink": ["audits[crawlable-anchors].details.headings[0].label"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | title": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | failureTitle": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "core/audits/bf-cache.js | displayValue": [{"values": {"itemCount": 4}, "path": "audits[bf-cache].displayValue"}], "core/audits/bf-cache.js | failureReasonColumn": ["audits[bf-cache].details.headings[0].label"], "core/audits/bf-cache.js | failureTypeColumn": ["audits[bf-cache].details.headings[1].label"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocket": ["audits[bf-cache].details.items[0].reason"], "core/audits/bf-cache.js | supportPendingFailureType": ["audits[bf-cache].details.items[0].failureType"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | mainResourceHasCacheControlNoStore": ["audits[bf-cache].details.items[1].reason"], "core/audits/bf-cache.js | notActionableFailureType": ["audits[bf-cache].details.items[1].failureType", "audits[bf-cache].details.items[2].failureType", "audits[bf-cache].details.items[3].failureType"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | jsNetworkRequestReceivedCacheControlNoStoreResource": ["audits[bf-cache].details.items[2].reason"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocketSticky": ["audits[bf-cache].details.items[3].reason"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | requestColumn": ["audits[cache-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "core/lib/i18n/i18n.js | displayValueMsSavings": [{"values": {"wastedMs": 1026}, "path": "audits[document-latency-insight].displayValue"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": ["audits[document-latency-insight].details.items.noRedirects.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | failedServerResponseTime": [{"values": {"PH1": "1126 ms"}, "path": "audits[document-latency-insight].details.items.serverResponseIsFast.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | columnSource": ["audits[duplicated-javascript-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | columnDuplicatedBytes": ["audits[duplicated-javascript-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | totalReflowTime": ["audits[forced-reflow-insight].details.items[0].headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | unattributed": ["audits[forced-reflow-insight].details.items[0].items[0].source.value"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | title": ["audits[inp-breakdown-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | description": ["audits[inp-breakdown-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | title": ["audits[lcp-breakdown-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | description": ["audits[lcp-breakdown-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | subpart": ["audits[lcp-breakdown-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | timeToFirstByte": ["audits[lcp-breakdown-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | elementRenderDelay": ["audits[lcp-breakdown-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnWastedBytes": ["audits[legacy-javascript-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | protocol": ["audits[modern-http-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | preconnectOriginsTableTitle": ["audits[network-dependency-tree-insight].details.items[1].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | preconnectOriginsTableDescription": ["audits[network-dependency-tree-insight].details.items[1].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | noPreconnectOrigins": ["audits[network-dependency-tree-insight].details.items[1].value.value"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | estSavingTableTitle": ["audits[network-dependency-tree-insight].details.items[2].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | estSavingTableDescription": ["audits[network-dependency-tree-insight].details.items[2].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | noPreconnectCandidates": ["audits[network-dependency-tree-insight].details.items[2].value.value"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnThirdParty": ["audits[third-parties-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnTransferSize": ["audits[third-parties-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnMainThreadTime": ["audits[third-parties-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}