// Simple test script to verify the search API endpoints work correctly
const BASE_URL = `http://localhost:${process.env.PORT || 3000}`;

async function testSearchEndpoint() {
  console.log('Testing /api/videos/search endpoint...\n');
  
  const testCases = [
    {
      name: 'Basic search query',
      url: `${BASE_URL}/api/videos/search?query=test`,
    },
    {
      name: 'Search with type filter',
      url: `${BASE_URL}/api/videos/search?query=video&type=nwa`,
    },
    {
      name: 'Search with platform filter',
      url: `${BASE_URL}/api/videos/search?platform=youtube`,
    },
    {
      name: 'Search with pagination',
      url: `${BASE_URL}/api/videos/search?page=1&limit=5`,
    },
    {
      name: 'Search with featured filter',
      url: `${BASE_URL}/api/videos/search?featured=true`,
    },
    {
      name: 'Combined filters',
      url: `${BASE_URL}/api/videos/search?query=test&type=user&platform=youtube&page=1&limit=10`,
    },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      console.log(`URL: ${testCase.url}`);
      
      const response = await fetch(testCase.url);
      const data = await response.json();
      
      console.log(`Status: ${response.status}`);
      console.log(`Results: ${data.videos ? data.videos.length : 0} videos`);
      console.log(`Total: ${data.pagination ? data.pagination.total : data.total || 0}`);
      console.log(`Filters applied:`, data.filters);
      console.log('---\n');
      
    } catch (error) {
      console.error(`Error testing ${testCase.name}:`, error.message);
      console.log('---\n');
    }
  }
}

async function testEnhancedEndpoints() {
  console.log('Testing enhanced existing endpoints...\n');
  
  const testCases = [
    {
      name: 'Enhanced /api/videos with search',
      url: `${BASE_URL}/api/videos?query=test&page=1&limit=5`,
    },
    {
      name: 'Enhanced /api/nwa-videos with search',
      url: `${BASE_URL}/api/nwa-videos?query=video&featured=true&page=1&limit=5`,
    },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      console.log(`URL: ${testCase.url}`);
      
      const response = await fetch(testCase.url);
      const data = await response.json();
      
      console.log(`Status: ${response.status}`);
      console.log(`Results: ${data.videos ? data.videos.length : 0} videos`);
      console.log(`Pagination:`, data.pagination);
      console.log(`Filters applied:`, data.filters);
      console.log('---\n');
      
    } catch (error) {
      console.error(`Error testing ${testCase.name}:`, error.message);
      console.log('---\n');
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  console.log('🔍 Testing Search and Filtering API Endpoints\n');
  console.log(`Note: Make sure your Next.js development server is running on localhost:${process.env.PORT || 3000}\n`);
  
  testSearchEndpoint()
    .then(() => testEnhancedEndpoints())
    .then(() => {
      console.log('✅ All tests completed!');
      console.log('\nAPI endpoints implemented:');
      console.log('- GET /api/videos/search - Dedicated search endpoint');
      console.log('- GET /api/videos - Enhanced with search and filtering');
      console.log('- GET /api/nwa-videos - Enhanced with search and filtering');
      console.log('\nFeatures implemented:');
      console.log('- Title and description search');
      console.log('- Type filtering (nwa/user/all)');
      console.log('- Platform filtering (youtube/tiktok/rumble)');
      console.log('- Featured status filtering');
      console.log('- Page-based pagination');
      console.log('- Combined filtering support');
      console.log('- Proper error handling');
      console.log('- Comprehensive test coverage');
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
    });
}

module.exports = { testSearchEndpoint, testEnhancedEndpoints };