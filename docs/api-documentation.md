# NWA Promote API Documentation

## Overview

The NWA Promote platform provides a comprehensive REST API for managing video content, user engagement, notifications, and administrative functions. All API endpoints require proper authentication and authorization.

## Base URL

```
https://your-domain.com/api
```

## Authentication

Most endpoints require authentication using NextAuth.js sessions. Include session cookies in your requests.

### Authentication Headers

```http
Cookie: next-auth.session-token=your-session-token
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "details": { ... }
}
```

## API Endpoints

### Videos

#### GET /api/videos
Retrieve videos with filtering and pagination.

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10, max: 50)
- `type` (string, optional): Filter by type (`nwa`, `user`, `all`)
- `status` (string, optional): Filter by status (`published`, `draft`, `archived`)
- `platform` (string, optional): Filter by platform (`youtube`, `tiktok`, `rumble`)
- `featured` (boolean, optional): Filter featured videos
- `query` (string, optional): Search query for title/description

**Example Request:**
```http
GET /api/videos?type=nwa&limit=20&featured=true
```

**Example Response:**
```json
{
  "videos": [
    {
      "id": "video123",
      "title": "Sample Video",
      "description": "Video description",
      "platforms": {
        "youtubeUrl": "https://youtube.com/watch?v=...",
        "tiktokUrl": "https://tiktok.com/@user/video/...",
        "rumbleUrl": "https://rumble.com/..."
      },
      "thumbnailUrl": "https://...",
      "isFeatured": true,
      "status": "published",
      "createdAt": "2024-01-01T00:00:00Z",
      "user": {
        "id": "user123",
        "name": "Creator Name",
        "role": "ADMIN"
      },
      "stats": {
        "totalLikes": 15,
        "totalShares": 8,
        "totalLinks": 3
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

#### POST /api/videos
Create a new video.

**Authentication:** Required
**Authorization:** Admin/NWA_TEAM for NWA videos, any authenticated user for personal videos

**Request Body:**
```json
{
  "title": "Video Title",
  "description": "Video description",
  "platforms": {
    "youtubeUrl": "https://youtube.com/watch?v=...",
    "tiktokUrl": "https://tiktok.com/@user/video/...",
    "rumbleUrl": "https://rumble.com/..."
  },
  "thumbnailUrl": "https://...",
  "duration": 120,
  "type": "nwa"
}
```

**Example Response:**
```json
{
  "id": "video123",
  "title": "Video Title",
  "description": "Video description",
  "platforms": { ... },
  "status": "published",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### GET /api/videos/search
Search videos by title and description.

**Query Parameters:**
- `q` (string, required): Search query
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page

### Video Interactions

#### POST /api/videos/{videoId}/like
Like a video on a specific platform.

**Authentication:** Required

**Request Body:**
```json
{
  "platform": "youtube"
}
```

#### POST /api/videos/{videoId}/share
Share a video on a specific platform.

**Authentication:** Required

**Request Body:**
```json
{
  "platform": "tiktok"
}
```

#### POST /api/videos/{videoId}/complete
Mark a video as completed.

**Authentication:** Required

**Example Response:**
```json
{
  "success": true,
  "message": "Video marked as completed"
}
```

#### POST /api/videos/{videoId}/feature
Feature/unfeature a video (Admin only).

**Authentication:** Required
**Authorization:** Admin only

**Request Body:**
```json
{
  "featured": true
}
```

### Notifications

#### GET /api/notifications
Get user notifications.

**Authentication:** Required

**Query Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `unreadOnly` (boolean, optional): Filter unread notifications

**Example Response:**
```json
{
  "notifications": [
    {
      "id": "notif123",
      "title": "New Video Available",
      "message": "Check out the latest NWA video!",
      "type": "VIDEO_RELEASE",
      "isRead": false,
      "createdAt": "2024-01-01T00:00:00Z",
      "video": {
        "id": "video123",
        "title": "Video Title"
      }
    }
  ],
  "pagination": { ... }
}
```

#### GET /api/notifications/count
Get unread notification count.

**Authentication:** Required

**Example Response:**
```json
{
  "unreadCount": 5
}
```

#### POST /api/notifications/send-video
Send video notification to users (Admin only).

**Authentication:** Required
**Authorization:** Admin only

**Request Body:**
```json
{
  "videoIds": ["video123", "video456"],
  "type": "nwa"
}
```

#### GET /api/notifications/preferences
Get user notification preferences.

**Authentication:** Required

#### PUT /api/notifications/preferences
Update user notification preferences.

**Authentication:** Required

**Request Body:**
```json
{
  "nwaVideoNotifications": true,
  "userVideoNotifications": false
}
```

### User Engagement

#### POST /api/engagement/track
Track user engagement events.

**Authentication:** Required

**Request Body:**
```json
{
  "videoId": "video123",
  "action": "like",
  "platform": "youtube",
  "metadata": {
    "duration": 120,
    "completionRate": 0.8
  }
}
```

#### GET /api/engagement/user-stats
Get user engagement statistics.

**Authentication:** Required

**Example Response:**
```json
{
  "totalLikes": 25,
  "totalShares": 12,
  "totalCompletions": 8,
  "platformBreakdown": {
    "youtube": { "likes": 15, "shares": 8 },
    "tiktok": { "likes": 7, "shares": 3 },
    "rumble": { "likes": 3, "shares": 1 }
  }
}
```

### Leaderboard

#### GET /api/leaderboard
Get leaderboard rankings.

**Query Parameters:**
- `limit` (number, optional): Number of entries (default: 50, max: 100)

**Example Response:**
```json
{
  "entries": [
    {
      "rank": 1,
      "userId": "user123",
      "name": "Top User",
      "points": 150,
      "level": 5,
      "isCurrentUser": false
    }
  ],
  "currentUser": {
    "rank": 15,
    "points": 45,
    "level": 2
  }
}
```

### Admin Endpoints

#### GET /api/admin/analytics
Get platform analytics (Admin only).

**Authentication:** Required
**Authorization:** Admin only

**Query Parameters:**
- `days` (number, optional): Number of days to analyze (default: 30)

**Example Response:**
```json
{
  "platformMetrics": {
    "youtube": { "totalEngagements": 150, "likes": 100, "shares": 50 },
    "tiktok": { "totalEngagements": 120, "likes": 80, "shares": 40 },
    "rumble": { "totalEngagements": 80, "likes": 50, "shares": 30 }
  },
  "userBehavior": {
    "totalUsers": 500,
    "activeUsers": 350,
    "avgEngagementPerUser": 2.5
  },
  "topVideos": [
    {
      "id": "video123",
      "title": "Popular Video",
      "totalEngagements": 45,
      "likes": 30,
      "shares": 15
    }
  ]
}
```

#### GET /api/admin/users
Get user management data (Admin only).

**Authentication:** Required
**Authorization:** Admin only

#### GET /api/admin/moderation
Get content moderation queue (Admin only).

**Authentication:** Required
**Authorization:** Admin only

#### POST /api/admin/moderation
Moderate reported content (Admin only).

**Authentication:** Required
**Authorization:** Admin only

**Request Body:**
```json
{
  "reportId": "report123",
  "action": "approve",
  "reason": "No violation found"
}
```

#### GET /api/admin/audit-logs
Get audit logs (Admin only).

**Authentication:** Required
**Authorization:** Admin only

**Query Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `action` (string, optional): Filter by action type
- `resource` (string, optional): Filter by resource type
- `startDate` (string, optional): Start date (ISO format)
- `endDate` (string, optional): End date (ISO format)

### Content Reporting

#### POST /api/content/report
Report inappropriate content.

**Authentication:** Required

**Request Body:**
```json
{
  "videoId": "video123",
  "reason": "inappropriate",
  "description": "Detailed description of the issue"
}
```

### Health Checks

#### GET /api/health
Basic health check.

**Example Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

#### GET /api/health/cache
Cache system health check.

**Example Response:**
```json
{
  "status": "healthy",
  "redis": {
    "connected": true,
    "latency": "2ms"
  }
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **General endpoints**: 100 requests per minute
- **Authentication endpoints**: 5 requests per minute
- **Video creation**: 10 requests per minute
- **Admin endpoints**: 200 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Pagination

Paginated endpoints use consistent pagination parameters:

- `page`: Page number (1-based)
- `limit`: Items per page
- `offset`: Alternative to page-based pagination

Pagination metadata is included in responses:
```json
{
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Webhooks

The platform supports webhooks for real-time notifications:

### Video Events
- `video.created`
- `video.updated`
- `video.featured`
- `video.completed`

### User Events
- `user.registered`
- `user.engagement`

### Webhook Payload Example
```json
{
  "event": "video.created",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": {
    "videoId": "video123",
    "userId": "user123",
    "title": "New Video"
  }
}
```

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @nwa-promote/api-client
```

### Usage Example
```typescript
import { NWAPromoteClient } from '@nwa-promote/api-client';

const client = new NWAPromoteClient({
  baseUrl: 'https://your-domain.com/api',
  sessionToken: 'your-session-token'
});

const videos = await client.videos.list({
  type: 'nwa',
  limit: 20
});
```

## Support

For API support and questions:
- Documentation: [https://docs.nwa-promote.com](https://docs.nwa-promote.com)
- Support Email: <EMAIL>
- GitHub Issues: [https://github.com/nwa-promote/api/issues](https://github.com/nwa-promote/api/issues)

## Changelog

### v1.2.0 (2024-01-15)
- Added audit logging endpoints
- Enhanced content moderation APIs
- Improved error handling and validation

### v1.1.0 (2024-01-01)
- Added multi-platform video support
- Enhanced notification system
- Added real-time statistics

### v1.0.0 (2023-12-01)
- Initial API release
- Basic video management
- User authentication and authorization