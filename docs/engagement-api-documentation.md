# Engagement Tracking API Documentation

## Overview

The engagement tracking API provides endpoints for tracking user interactions with videos across different platforms (YouTube, TikTok, Rumble). It supports tracking likes, shares, and video completions, and provides comprehensive user statistics.

## Endpoints

### POST /api/engagement/track

Track platform-specific user engagement actions.

**Request Body:**
```json
{
  "videoId": "string",
  "platform": "youtube" | "tiktok" | "rumble",
  "action": "like" | "share" | "complete"
}
```

**Response:**
- `201 Created`: Engagement tracked successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: User not authenticated
- `404 Not Found`: Video not found
- `500 Internal Server Error`: Server error

**Features:**
- Validates platform and action parameters
- Prevents duplicate engagement records (upserts existing records)
- For "complete" actions, also creates a record in the CompletedVideo table
- Tracks timestamp for like actions in the `likedAt` field

### GET /api/engagement/user-stats

Get comprehensive user engagement statistics.

**Query Parameters:**
- `userId` (optional): Get stats for a specific user (defaults to current user)

**Response:**
```json
{
  "totalLikes": 10,
  "totalShares": 5,
  "totalVideosCompleted": 3,
  "leaderboardPosition": 15,
  "platformBreakdown": {
    "youtube": {
      "likes": 6,
      "shares": 3,
      "completed": 2
    },
    "tiktok": {
      "likes": 4,
      "shares": 2,
      "completed": 1
    },
    "rumble": {
      "likes": 0,
      "shares": 0,
      "completed": 0
    }
  }
}
```

**Features:**
- Calculates total engagement counts across all platforms
- Provides platform-specific breakdown of engagement
- Calculates user's leaderboard position based on total engagement
- Includes completed videos count from CompletedVideo table

## Enhanced Like/Share Endpoints

The existing like and share endpoints have been enhanced to track platform information:

### POST /api/videos/[videoId]/like
### POST /api/videos/[videoId]/share
### POST /api/video-links/[videoLinkId]/like
### POST /api/video-links/[videoLinkId]/share

**Enhanced Request Body:**
```json
{
  "platform": "youtube" | "tiktok" | "rumble"
}
```

**Features:**
- Maintains backward compatibility (platform is optional)
- Automatically tracks engagement in UserEngagement table when platform is provided
- For video-link endpoints, uses the platform from the VideoLink record

## Database Schema

### UserEngagement Table
```sql
model UserEngagement {
  id        String    @id @default(cuid())
  userId    String
  videoId   String
  platform  String    // youtube, tiktok, rumble
  action    String    // like, share, complete
  likedAt   DateTime?
  createdAt DateTime  @default(now())
  
  @@unique([userId, videoId, platform, action])
}
```

### CompletedVideo Table
```sql
model CompletedVideo {
  id          String   @id @default(cuid())
  userId      String
  videoId     String
  completedAt DateTime @default(now())
  
  @@unique([userId, videoId])
}
```

## Usage Examples

### Track a YouTube like
```javascript
fetch('/api/engagement/track', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    videoId: 'video123',
    platform: 'youtube',
    action: 'like'
  })
});
```

### Track video completion
```javascript
fetch('/api/engagement/track', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    videoId: 'video123',
    platform: 'youtube',
    action: 'complete'
  })
});
```

### Get user statistics
```javascript
fetch('/api/engagement/user-stats')
  .then(response => response.json())
  .then(stats => {
    console.log(`Total likes: ${stats.totalLikes}`);
    console.log(`Leaderboard position: ${stats.leaderboardPosition}`);
  });
```

### Like a video with platform tracking
```javascript
fetch('/api/videos/video123/like', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    platform: 'youtube'
  })
});
```

## Error Handling

All endpoints return structured error responses:

```json
{
  "message": "Error description"
}
```

Common error scenarios:
- `401 Unauthorized`: User session expired or invalid
- `400 Bad Request`: Invalid platform or action parameters
- `404 Not Found`: Video or video link not found
- `409 Conflict`: Duplicate action (for like/share endpoints)
- `500 Internal Server Error`: Database or server errors

## Platform Validation

Valid platforms: `youtube`, `tiktok`, `rumble`
Valid actions: `like`, `share`, `complete`

Platform validation is case-insensitive and automatically converted to lowercase.

## Leaderboard Calculation

The leaderboard position is calculated by counting users with higher total engagement scores (likes + shares + completions) and adding 1. This provides a real-time ranking system for user engagement.