# OAuth JWT Authentication Fix

## Root Cause Analysis

The JWT decryption failure in OAuth authentication is caused by:

1. **Secret Clarification**: NextAuth.js v4 uses `NEXTAUTH_SECRET` for ALL JWT operations (signing, verification, encryption)
2. **No Separate JWT Secret**: There is no separate `jwt` secret - only `NEXTAUTH_SECRET` is required
3. **Environment Inconsistency**: Different secrets across environment files causing token validation failures

## Current Configuration

**Environment Variables:**
- `.env`: `NEXTAUTH_SECRET="b3a2c2e4d8f7a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6"`
- `.env.local`: `NEXTAUTH_SECRET="fd957fd756f19af3bddc3d6218175ff514ca680fff8ed74ab5cbde5e962d053a"`

**NextAuth Configuration:**
- Uses `NEXTAUTH_SECRET` for JWT operations
- Session strategy: JWT
- JWT maxAge: 30 days

## Production-Grade Fix

### 1. Environment Configuration Fix

```bash
# Ensure consistent secrets across all environments
# Use the same NEXTAUTH_SECRET in all environment files
NEXTAUTH_SECRET="fd957fd756f19af3bddc3d6218175ff514ca680fff8ed74ab5cbde5e962d053a"
```

### 2. Enhanced NextAuth Configuration

Create a new file `src/lib/auth-config.ts` with improved JWT handling:

```typescript
import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';

export const createAuthOptions = (): NextAuthOptions => {
  const secret = process.env.NEXTAUTH_SECRET;

  if (!secret) {
    throw new Error('NEXTAUTH_SECRET is required for authentication');
  }

  if (secret.length < 32) {
    throw new Error('NEXTAUTH_SECRET must be at least 32 characters long');
  }

  return {
    adapter: PrismaAdapter(prisma),
    providers: [
      {
        id: 'member-portal',
        name: 'Member Portal',
        type: 'oauth',
        version: '2.0',
        authorization: {
          url: `${process.env.MEMBER_PORTAL_URL}/api/oauth/authorize`,
          params: {
            scope: 'read:profile openid email',
            response_type: 'code'
          },
        },
        token: `${process.env.MEMBER_PORTAL_URL}/api/oauth/token`,
        userinfo: `${process.env.MEMBER_PORTAL_URL}/api/oauth/userinfo`,
        clientId: process.env.CLIENT_ID!,
        clientSecret: process.env.CLIENT_SECRET!,
        profile(profile) {
          console.log('OAuth profile received:', profile);
          try {
            return {
              id: profile.sub || profile.id,
              name: profile.name || profile.username || `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Unknown User',
              email: profile.email,
              image: profile.picture || profile.avatar_url || null,
              role: profile.role || profile.user_role || 'USER',
            };
          } catch (error) {
            console.error('Error mapping OAuth profile:', error, profile);
            throw new Error('Failed to process user profile');
          }
        },
      },
    ],
    callbacks: {
      async jwt({ token, user, account }) {
        try {
          if (account && user) {
            token.accessToken = account.access_token;
            token.id = user.id;
            token.role = user.role;
          }
          return token;
        } catch (error) {
          console.error('JWT callback error:', error);
          throw new Error('Authentication processing failed. Please try again.');
        }
      },
      async session({ session, token }) {
        try {
          if (session.user) {
            session.user.id = token.id as string;
            session.user.role = token.role as string;
          }
          return session;
        } catch (error) {
          console.error('Session callback error:', error);
          throw new Error('Session initialization failed. Please try again.');
        }
      },
      async redirect({ url, baseUrl }) {
        try {
          if (!url) return baseUrl;

          // Handle relative URLs first (avoid parsing with new URL)
          if (url.startsWith('/')) {
            return `${baseUrl}${url}`;
          }

          // Only attempt to parse absolute URLs
          try {
            const parsed = new URL(url);
            if (parsed.origin === baseUrl) return url;
          } catch {
            // Non-absolute or invalid URL, fallback
            return baseUrl;
          }

          // Fallback to base URL for all other cases
          return baseUrl;
        } catch (error) {
          console.error('Redirect callback error:', error);
          return baseUrl;
        }
      },
    },
    pages: {
      signIn: '/auth/signin',
      signOut: '/auth/signout',
      error: '/auth/error',
    },
    session: {
      strategy: 'jwt',
      maxAge: 30 * 24 * 60 * 60, // 30 days
    },
    debug: process.env.NODE_ENV === 'development',
    secret: secret,
    events: {
      async signIn(message) {
        console.log('User signed in:', message.user?.email);
      },
      async signOut(message) {
        console.log('User signed out:', message.token?.email);
      },
      async error(message) {
        console.error('NextAuth error:', message);
      },
    },
  };
};
```

### 3. Update Auth Configuration

Update `src/lib/auth.ts`:

```typescript
import { createAuthOptions } from './auth-config';

export const authOptions = createAuthOptions();
```

### 4. Enhanced JWT Validation Service

Create `src/services/JwtValidationService.ts`:

```typescript
import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';

export class JwtValidationService {
  static async validateToken(request: NextRequest) {
    try {
      const secret = process.env.NEXTAUTH_SECRET;

      if (!secret) {
        throw new Error('NEXTAUTH_SECRET is not configured');
      }

      const token = await getToken({
        req: request,
        secret: secret,
        secureCookie: process.env.NODE_ENV === 'production',
      });

      if (!token) {
        return { valid: false, error: 'No token found' };
      }

      // Check if token is expired
      if (token.exp && Date.now() >= token.exp * 1000) {
        return { valid: false, error: 'Token expired' };
      }

      return {
        valid: true,
        token,
        user: {
          id: token.id as string,
          email: token.email as string,
          name: token.name as string,
          role: token.role as string,
        }
      };
    } catch (error) {
      console.error('JWT validation error:', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  static async debugToken(request: NextRequest) {
    const secret = process.env.NEXTAUTH_SECRET;
    console.log('JWT Debug Info:');
    console.log('- Secret configured:', !!secret);
    console.log('- Secret length:', secret?.length);
    console.log('- Environment:', process.env.NODE_ENV);

    try {
      const token = await getToken({
        req: request,
        secret: secret,
      });

      console.log('- Token found:', !!token);
      if (token) {
        console.log('- Token keys:', Object.keys(token));
        console.log('- Token exp:', token.exp);
        console.log('- Current time:', Date.now() / 1000);
        console.log('- Token expired:', token.exp ? Date.now() >= token.exp * 1000 : 'No exp');
      }
    } catch (error) {
      console.error('- Token parsing error:', error);
    }
  }
}
```

### 5. Update Middleware

Update `src/middleware.ts` with better error handling:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { applySecurityHeaders, getSecurityConfig } from '@/lib/security-headers';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  try {
    // Apply security headers to all responses
    const response = NextResponse.next();
    const securityConfig = getSecurityConfig();
    applySecurityHeaders(response, securityConfig);

    const pathname = request.nextUrl.pathname;

    // Public routes that don't require authentication
    const publicRoutes = [
      '/',
      '/auth/signin',
      '/auth/signout',
      '/auth/error',
      '/api/auth', // Allow access to all NextAuth endpoints including OAuth callbacks
      '/api/permissions', // Allow access to permissions endpoint for member portal
      '/api/health',
      '/api/webhook',
      '/_next',
      '/favicon.ico',
      '/public'
    ];

    // Check if the route is public
    const isPublicRoute = publicRoutes.some(route =>
      pathname === route || pathname.startsWith(route + '/') || pathname.startsWith(route + '?')
    );

    // If it's a public route, allow access
    if (isPublicRoute) {
      return response;
    }

    // For protected routes, check if user is authenticated
    const secret = process.env.NEXTAUTH_SECRET;

    if (!secret) {
      console.error('NEXTAUTH_SECRET is not configured');
      const signInUrl = new URL('/auth/error', request.url);
      signInUrl.searchParams.set('error', 'ConfigurationError');
      return NextResponse.redirect(signInUrl);
    }

    const token = await getToken({
      req: request,
      secret: secret,
      secureCookie: process.env.NODE_ENV === 'production'
    });

    // If user is not authenticated, redirect to signin with callbackUrl
    if (!token) {
      console.log('No token found, redirecting to signin');
      const signInUrl = new URL('/api/auth/signin/member-portal', request.url);
      signInUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(signInUrl);
    }

    // Check if token is expired
    if (token.exp && Date.now() >= token.exp * 1000) {
      console.log('Token expired, redirecting to signin');
      const signInUrl = new URL('/api/auth/signin/member-portal', request.url);
      signInUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(signInUrl);
    }

    console.log('Authentication successful for:', token.email);

    return response;
  } catch (error) {
    console.error('Middleware error:', error);

    // Redirect to error page with error information
    const errorUrl = new URL('/auth/error', request.url);
    errorUrl.searchParams.set('error', 'ServerError');
    errorUrl.searchParams.set('callbackUrl', request.nextUrl.pathname);
    return NextResponse.redirect(errorUrl);
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
```

### 6. Environment Configuration

Update `.env` to use consistent secrets:

```bash
# Use the same secret across all environments
NEXTAUTH_SECRET="fd957fd756f19af3bddc3d6218175ff514ca680fff8ed74ab5cbde5e962d053a"
```

## Testing and Validation

### 1. Test Script

Create `scripts/test-oauth-jwt.ts`:

```typescript
import { JwtValidationService } from '../src/services/JwtValidationService';

async function testJwtValidation() {
  console.log('Testing JWT validation...');

  // Mock request object
  const mockRequest = {
    cookies: {
      get: (name: string) => ({
        value: process.env[`COOKIE_${name.toUpperCase()}`] || ''
      })
    },
    headers: {
      get: (name: string) => process.env[`HEADER_${name.toUpperCase()}`] || ''
    }
  } as any;

  const result = await JwtValidationService.validateToken(mockRequest);

  console.log('Validation result:', result);

  if (!result.valid) {
    console.error('JWT validation failed:', result.error);
    process.exit(1);
  }

  console.log('JWT validation successful!');
  console.log('User:', result.user);
}

testJwtValidation().catch(console.error);
```

### 2. Debug Command

Add to `package.json`:

```json
{
  "scripts": {
    "debug:jwt": "ts-node scripts/test-oauth-jwt.ts"
  }
}
```

## Deployment Checklist

1. ✅ Update environment variables with consistent `NEXTAUTH_SECRET`
2. ✅ Deploy enhanced NextAuth configuration
3. ✅ Deploy JWT validation service
4. ✅ Update middleware with better error handling
5. ✅ Test OAuth flow in staging environment
6. ✅ Monitor authentication logs for issues

## Monitoring and Alerts

### 1. Key Metrics to Monitor

- Authentication success rate
- JWT validation errors
- OAuth callback failures
- Token expiration events

### 2. Alert Conditions

- Authentication success rate < 95%
- JWT validation error rate > 1%
- OAuth callback failure rate > 0.1%

## Documentation Updates

### 1. Update README

Add section about authentication:

```markdown
## Authentication

This application uses NextAuth.js v4 with JWT-based authentication for OAuth flows.

### Configuration

- **Secret**: Uses `NEXTAUTH_SECRET` environment variable
- **Strategy**: JWT with 30-day expiration
- **Provider**: Custom OAuth provider for Member Portal

### Environment Variables

```bash
NEXTAUTH_SECRET="your-secret-here"  # Minimum 32 characters
NEXTAUTH_URL="http://localhost:3002"  # Your application URL
```

### Troubleshooting

If JWT decryption fails:

1. Verify `NEXTAUTH_SECRET` is set and consistent across environments
2. Check token expiration (30-day limit)
3. Review NextAuth logs for detailed error messages
4. Use `npm run debug:jwt` for token validation testing
```

### 2. Update Workflow State

Mark task as completed with findings.