# New Admin Analytics Endpoints

This document describes the new analytics endpoints created to enhance admin visibility into user engagement and activity.

## 🆕 New Endpoints

### 1. `/api/admin/users/inactive` - Inactive Users Report

**Purpose**: Identify users with low engagement, no activity, or who haven't logged in recently.

**Method**: GET  
**Authentication**: Admin only

**Query Parameters**:
- `inactivityDays` (default: 30) - Days since last activity to consider inactive
- `minEngagementScore` (default: 5) - Minimum engagement score threshold  
- `includeNeverLoggedIn` (default: true) - Include users who never logged in
- `limit` (default: 100) - Maximum number of results

**Response**:
```json
{
  "users": [
    {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2025-01-01T00:00:00Z",
      "lastActivityAt": "2025-01-15T00:00:00Z",
      "totalEngagements": 2,
      "totalLikes": 1,
      "totalShares": 0,
      "totalCompletions": 1,
      "daysSinceLastActivity": 35,
      "daysSinceRegistration": 45,
      "engagementScore": 3,
      "status": "low_engagement"
    }
  ],
  "summary": {
    "totalInactiveUsers": 15,
    "neverLoggedIn": 5,
    "inactiveUsers": 4,
    "lowEngagementUsers": 4,
    "noEngagementUsers": 2
  },
  "filters": {
    "inactivityDays": 30,
    "minEngagementScore": 5,
    "includeNeverLoggedIn": true
  }
}
```

**User Status Types**:
- `never_logged_in` - User registered but never logged in
- `inactive` - User inactive for more than specified days
- `low_engagement` - User has engagement score below threshold
- `no_engagement` - User has zero engagement activities

---

### 2. `/api/admin/analytics/user-activity` - User Activity Analysis

**Purpose**: Detailed user engagement analysis and ranking with activity trends.

**Method**: GET  
**Authentication**: Admin only

**Query Parameters**:
- `days` (default: 30) - Time period for analysis
- `sortBy` (default: engagementScore) - Sort criteria: engagementScore, lastActivity, totalShares, totalLikes
- `limit` (default: 50) - Maximum number of results
- `minEngagement` (default: 0) - Minimum engagement score filter

**Response**:
```json
{
  "users": [
    {
      "id": "user_id",
      "name": "Active User",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2025-01-01T00:00:00Z",
      "lastActivityAt": "2025-01-20T00:00:00Z",
      "totalEngagements": 45,
      "totalLikes": 15,
      "totalShares": 10,
      "totalCompletions": 20,
      "engagementScore": 75,
      "leaderboardPosition": 1,
      "platformBreakdown": {
        "youtube": { "likes": 8, "shares": 5, "completions": 12 },
        "tiktok": { "likes": 7, "shares": 5, "completions": 8 }
      },
      "recentActivity": [
        {
          "date": "2025-01-20T15:30:00Z",
          "action": "share",
          "platform": "youtube",
          "videoId": "video_123"
        }
      ],
      "activityTrend": {
        "2025-01-20": { "likes": 3, "shares": 2, "completions": 1 },
        "2025-01-19": { "likes": 1, "shares": 1, "completions": 0 }
      },
      "daysSinceLastActivity": 1,
      "isActive": true
    }
  ],
  "summary": {
    "totalUsers": 150,
    "activeUsers": 75,
    "topPerformers": 12,
    "lowEngagementUsers": 25,
    "averageEngagementScore": 15.5,
    "totalPlatformEngagements": {
      "youtube": 450,
      "tiktok": 320,
      "rumble": 150
    }
  }
}
```

---

### 3. `/api/admin/analytics/user-engagement` - Comprehensive Engagement Report

**Purpose**: Detailed video link performance and user engagement analysis across platforms.

**Method**: GET  
**Authentication**: Admin only

**Query Parameters**:
- `days` (default: 30) - Time period for analysis
- `limit` (default: 20) - Maximum number of results per section

**Response**:
```json
{
  "videoLinkPerformance": [
    {
      "videoLinkId": "link_123",
      "title": "Video Title",
      "url": "https://youtube.com/watch?v=123",
      "platform": "youtube",
      "totalLikes": 45,
      "totalShares": 20,
      "totalCompletions": 0,
      "totalEngagements": 65,
      "uniqueUsers": 35,
      "topEngagers": [
        {
          "userId": "user_123",
          "userName": "Top User",
          "userEmail": "<EMAIL>",
          "engagements": 5,
          "likes": 3,
          "shares": 2
        }
      ]
    }
  ],
  "userEngagementSummary": [
    {
      "userId": "user_123",
      "userName": "Power User",
      "userEmail": "<EMAIL>",
      "userRole": "USER",
      "totalVideoLinksEngaged": 15,
      "totalLikes": 25,
      "totalShares": 18,
      "totalCompletions": 12,
      "engagementScore": 122,
      "favoriteVideos": [
        {
          "videoLinkId": "link_456",
          "title": "Popular Video",
          "engagements": 8
        }
      ],
      "favoritePlatforms": [
        { "platform": "youtube", "engagements": 30 },
        { "platform": "tiktok", "engagements": 25 }
      ],
      "lastEngagementDate": "2025-01-20T15:30:00Z",
      "engagementFrequency": 2.5
    }
  ],
  "platformInsights": {
    "youtube": {
      "totalEngagements": 450,
      "totalLikes": 200,
      "totalShares": 150,
      "totalCompletions": 100,
      "uniqueUsers": 75,
      "topVideoLinks": [
        {
          "videoLinkId": "link_123",
          "title": "Top Video",
          "engagements": 65
        }
      ]
    }
  },
  "timeBasedInsights": {
    "2025-01-20": {
      "totalEngagements": 35,
      "uniqueUsers": 20,
      "newEngagers": 0
    }
  },
  "summary": {
    "totalVideoLinks": 50,
    "totalEngagements": 920,
    "totalUniqueEngagers": 85,
    "averageEngagementsPerVideoLink": 18.4,
    "averageEngagementsPerUser": 10.8,
    "topPlatform": "youtube",
    "mostEngagedVideoLink": "Popular Video Title"
  }
}
```

---

## 📊 Use Cases

### 1. **Identify Inactive Users**
```bash
GET /api/admin/users/inactive?inactivityDays=60&minEngagementScore=3
```
Find users who haven't been active in 60 days or have very low engagement.

### 2. **Top Performers Analysis**
```bash
GET /api/admin/analytics/user-activity?sortBy=engagementScore&limit=10
```
Get the top 10 most engaged users with detailed activity breakdown.

### 3. **Platform Performance**
```bash
GET /api/admin/analytics/user-engagement?days=7
```
Weekly report on which platforms are performing best and which video links are getting the most engagement.

### 4. **User Re-engagement Campaign**
```bash
GET /api/admin/users/inactive?status=low_engagement&limit=50
```
Get users with low engagement for targeted re-engagement campaigns.

---

## 🔧 Integration with Existing Analytics

These endpoints complement the existing `/api/admin/analytics` endpoint by providing:

1. **User-Level Granularity** - Individual user performance tracking
2. **Actionable Insights** - Specific users to target for re-engagement
3. **Platform Comparison** - Cross-platform performance analysis  
4. **Time-Based Trends** - Daily activity patterns
5. **Engagement Scoring** - Weighted scoring system for user activity

---

## 🚀 Next Steps

1. **Dashboard Integration** - Add these insights to the admin dashboard
2. **Automated Reports** - Schedule regular inactive user reports
3. **Re-engagement Campaigns** - Use data to target inactive users
4. **Performance Monitoring** - Track improvements in user engagement over time

---

## 📈 Scoring System

**Engagement Score Calculation**:
- Likes: 2 points each
- Shares: 3 points each  
- Completions: 1 point each

This weighted system prioritizes shares (most valuable) over likes and completions.
