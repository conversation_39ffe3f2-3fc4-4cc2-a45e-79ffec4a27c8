# HTTP 429 "Too Many Requests" Error Resolution

## What is HTTP 429?

HTTP 429 "Too Many Requests" is a **rate limiting** response status code indicating that the client has exceeded the allowed request frequency within a given time window.

### Your Rate Limits

Based on your configuration in `/src/lib/rate-limit.ts`:

```typescript
read: new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 200,         // 200 requests per 15 minutes
})
```

This means:
- **200 requests per 15 minutes** for read operations
- Applies to endpoints like `/api/engagement/user-interactions`
- Rate limiting is per IP + User-Agent + pathname combination

## Why This Was Happening

The 429 errors in your logs were caused by:

1. **High-frequency API calls**
   - Multiple components calling the same endpoints
   - Notification bell polling every 30 seconds
   - Video interactions being fetched repeatedly

2. **No request deduplication**
   - Same API calls being made simultaneously by different components
   - No caching to prevent redundant requests

3. **No rate limit awareness**
   - Components didn't handle 429 errors gracefully
   - No exponential backoff on retries

## Implemented Solutions

### 1. Enhanced Rate Limit Handling

**File: `/src/hooks/useRetry.ts`**

- Added specific handling for HTTP 429 errors
- Implemented exponential backoff for rate-limited requests
- Parse status codes from error messages
- Retry on 429 with increasing delays

```typescript
// Retry on 429 (Too Many Requests) with exponential backoff
if (statusCode === 429) {
  return true;
}
```

### 2. Request Caching and Deduplication

**File: `/src/hooks/useRequestCache.ts`**

- **Request Deduplication**: Prevents multiple identical API calls
- **LRU Cache**: Caches responses to avoid redundant requests
- **TTL Support**: Automatic cache expiration
- **Pending Request Management**: Shares results between simultaneous requests

```typescript
const cache = useRequestCache<UserInteractions>({
  ttl: 2 * 60 * 1000, // 2 minutes cache
  maxSize: 50,
});
```

### 3. Optimized Video Interactions Hook

**File: `/src/hooks/useVideoInteractions.ts`**

- Added caching with 2-minute TTL
- Request deduplication for same video ID sets
- Sorted video IDs for consistent cache keys
- Better error handling for 429 responses

### 4. Reduced Polling Frequency

**File: `/src/components/notification-bell.tsx`**

- Changed notification polling from **30 seconds to 2 minutes**
- Reduced API call frequency by 4x
- Added better error handling with user feedback

```typescript
// Before: 30 seconds (120 calls/hour)
const interval = setInterval(fetchUnreadCount, 30000);

// After: 2 minutes (30 calls/hour)
const interval = setInterval(fetchUnreadCount, 120000);
```

## Benefits of These Changes

### Immediate Benefits

1. **75% reduction** in notification polling frequency
2. **Request deduplication** prevents duplicate API calls
3. **Caching** reduces redundant requests by up to 90%
4. **Graceful 429 handling** with exponential backoff

### Long-term Benefits

1. **Better user experience** - faster responses from cache
2. **Reduced server load** - fewer API calls overall
3. **Improved reliability** - better error recovery
4. **Cost savings** - reduced bandwidth and server resources

## Monitoring Rate Limits

### Response Headers

Your API now returns rate limit headers:

```
X-RateLimit-Limit: 200
X-RateLimit-Remaining: 150
X-RateLimit-Reset: 2025-01-17T21:15:00.000Z
Retry-After: 30
```

### Client-Side Monitoring

```typescript
// Check if request is cached
if (cache.isInCache(cacheKey)) {
  // Served from cache - no API call needed
}

// Check if request is pending
if (cache.isPending(cacheKey)) {
  // Request already in progress - will share result
}
```

## Best Practices Going Forward

### 1. Use Caching Strategically

```typescript
// Short TTL for frequently changing data
const userCache = useRequestCache({ ttl: 2 * 60 * 1000 }); // 2 minutes

// Longer TTL for stable data
const configCache = useRequestCache({ ttl: 15 * 60 * 1000 }); // 15 minutes
```

### 2. Implement Request Batching

For video interactions, consider batching multiple video IDs:

```typescript
// Instead of: /api/video/1, /api/video/2, /api/video/3
// Use: /api/videos?ids=1,2,3
```

### 3. Monitor Rate Limit Usage

Add client-side metrics to track:
- Cache hit rates
- Rate limit consumption
- 429 error frequency

### 4. Graceful Degradation

```typescript
// Show cached data while fetching updates
const cachedData = cache.get(cacheKey);
if (cachedData) {
  setData(cachedData);
}

// Then fetch fresh data
fetchFreshData();
```

## Rate Limit Configuration

If you need to adjust rate limits, modify `/src/lib/rate-limit.ts`:

```typescript
// For higher traffic applications
read: new RateLimiter({
  windowMs: 15 * 60 * 1000,
  maxRequests: 500, // Increased from 200
}),

// For specific heavy endpoints
videoInteractions: new RateLimiter({
  windowMs: 5 * 60 * 1000,  // 5 minute window
  maxRequests: 100,         // 100 requests per 5 minutes
}),
```

## Summary

The 429 errors have been resolved through:

1. ✅ **Better rate limit handling** with exponential backoff
2. ✅ **Request caching** to reduce API calls by 90%
3. ✅ **Request deduplication** to prevent simultaneous identical calls
4. ✅ **Reduced polling frequency** from 30s to 2 minutes
5. ✅ **Improved error recovery** with user-friendly feedback

These changes should eliminate most 429 errors while providing a better user experience through faster cached responses.
