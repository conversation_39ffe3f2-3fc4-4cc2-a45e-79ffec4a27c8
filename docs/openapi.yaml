openapi: 3.0.3
info:
  title: NWA Promote API
  description: |
    The NWA Promote platform API for managing video content, user engagement, 
    notifications, and administrative functions.
  version: 1.2.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.nwa-promote.com
    description: Production server
  - url: https://staging-api.nwa-promote.com
    description: Staging server
  - url: `http://localhost:${process.env.PORT || 3000}/api`
    description: Local development server

security:
  - sessionAuth: []

paths:
  /videos:
    get:
      summary: List videos
      description: Retrieve videos with filtering and pagination
      tags:
        - Videos
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 10
        - name: type
          in: query
          schema:
            type: string
            enum: [nwa, user, all]
        - name: status
          in: query
          schema:
            type: string
            enum: [published, draft, archived]
        - name: platform
          in: query
          schema:
            type: string
            enum: [youtube, tiktok, rumble]
        - name: featured
          in: query
          schema:
            type: boolean
        - name: query
          in: query
          description: Search query for title/description
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VideoListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      summary: Create video
      description: Create a new video
      tags:
        - Videos
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVideoRequest'
      responses:
        '201':
          description: Video created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Video'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /videos/{videoId}/like:
    post:
      summary: Like video
      description: Like a video on a specific platform
      tags:
        - Video Interactions
      security:
        - sessionAuth: []
      parameters:
        - name: videoId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                platform:
                  type: string
                  enum: [youtube, tiktok, rumble]
              required:
                - platform
      responses:
        '200':
          description: Video liked successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /videos/{videoId}/share:
    post:
      summary: Share video
      description: Share a video on a specific platform
      tags:
        - Video Interactions
      security:
        - sessionAuth: []
      parameters:
        - name: videoId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                platform:
                  type: string
                  enum: [youtube, tiktok, rumble]
              required:
                - platform
      responses:
        '200':
          description: Video shared successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /videos/{videoId}/complete:
    post:
      summary: Complete video
      description: Mark a video as completed
      tags:
        - Video Interactions
      security:
        - sessionAuth: []
      parameters:
        - name: videoId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Video marked as completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /notifications:
    get:
      summary: Get notifications
      description: Retrieve user notifications
      tags:
        - Notifications
      security:
        - sessionAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
        - name: unreadOnly
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /notifications/count:
    get:
      summary: Get unread count
      description: Get unread notification count
      tags:
        - Notifications
      security:
        - sessionAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  unreadCount:
                    type: integer
                    example: 5

  /notifications/preferences:
    get:
      summary: Get notification preferences
      description: Get user notification preferences
      tags:
        - Notifications
      security:
        - sessionAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationPreferences'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      summary: Update notification preferences
      description: Update user notification preferences
      tags:
        - Notifications
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationPreferences'
      responses:
        '200':
          description: Preferences updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /leaderboard:
    get:
      summary: Get leaderboard
      description: Retrieve leaderboard rankings
      tags:
        - Leaderboard
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeaderboardResponse'

  /engagement/track:
    post:
      summary: Track engagement
      description: Track user engagement events
      tags:
        - Engagement
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EngagementTrackRequest'
      responses:
        '200':
          description: Engagement tracked successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /engagement/user-stats:
    get:
      summary: Get user stats
      description: Get user engagement statistics
      tags:
        - Engagement
      security:
        - sessionAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserStatsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /admin/analytics:
    get:
      summary: Get analytics
      description: Get platform analytics (Admin only)
      tags:
        - Admin
      security:
        - sessionAuth: []
      parameters:
        - name: days
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 365
            default: 30
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/audit-logs:
    get:
      summary: Get audit logs
      description: Get audit logs (Admin only)
      tags:
        - Admin
      security:
        - sessionAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
        - name: action
          in: query
          schema:
            type: string
        - name: resource
          in: query
          schema:
            type: string
        - name: startDate
          in: query
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLogResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /content/report:
    post:
      summary: Report content
      description: Report inappropriate content
      tags:
        - Content Moderation
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContentReportRequest'
      responses:
        '200':
          description: Report submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /health:
    get:
      summary: Health check
      description: Basic health check
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: 1.2.0

components:
  securitySchemes:
    sessionAuth:
      type: apiKey
      in: cookie
      name: next-auth.session-token

  schemas:
    Video:
      type: object
      properties:
        id:
          type: string
          example: video123
        title:
          type: string
          example: Sample Video
        description:
          type: string
          example: Video description
        platforms:
          $ref: '#/components/schemas/PlatformUrls'
        thumbnailUrl:
          type: string
          format: uri
        isFeatured:
          type: boolean
        status:
          type: string
          enum: [published, draft, archived]
        createdAt:
          type: string
          format: date-time
        user:
          $ref: '#/components/schemas/User'
        stats:
          $ref: '#/components/schemas/VideoStats'

    PlatformUrls:
      type: object
      properties:
        youtubeUrl:
          type: string
          format: uri
          example: https://youtube.com/watch?v=...
        tiktokUrl:
          type: string
          format: uri
          example: https://tiktok.com/@user/video/...
        rumbleUrl:
          type: string
          format: uri
          example: https://rumble.com/...

    VideoStats:
      type: object
      properties:
        totalLikes:
          type: integer
        totalShares:
          type: integer
        totalLinks:
          type: integer

    User:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        role:
          type: string
          enum: [USER, ADMIN, NWA_TEAM]

    CreateVideoRequest:
      type: object
      required:
        - title
        - platforms
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 2000
        platforms:
          $ref: '#/components/schemas/PlatformUrls'
        thumbnailUrl:
          type: string
          format: uri
        duration:
          type: integer
          minimum: 1
        type:
          type: string
          enum: [nwa, user]
          default: user

    VideoListResponse:
      type: object
      properties:
        videos:
          type: array
          items:
            $ref: '#/components/schemas/Video'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        totalPages:
          type: integer
        hasNextPage:
          type: boolean
        hasPreviousPage:
          type: boolean

    Notification:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        message:
          type: string
        type:
          type: string
        isRead:
          type: boolean
        createdAt:
          type: string
          format: date-time
        video:
          type: object
          properties:
            id:
              type: string
            title:
              type: string

    NotificationListResponse:
      type: object
      properties:
        notifications:
          type: array
          items:
            $ref: '#/components/schemas/Notification'
        pagination:
          $ref: '#/components/schemas/Pagination'

    NotificationPreferences:
      type: object
      properties:
        nwaVideoNotifications:
          type: boolean
        userVideoNotifications:
          type: boolean

    LeaderboardEntry:
      type: object
      properties:
        rank:
          type: integer
        userId:
          type: string
        name:
          type: string
        points:
          type: integer
        level:
          type: integer
        isCurrentUser:
          type: boolean

    LeaderboardResponse:
      type: object
      properties:
        entries:
          type: array
          items:
            $ref: '#/components/schemas/LeaderboardEntry'
        currentUser:
          type: object
          properties:
            rank:
              type: integer
            points:
              type: integer
            level:
              type: integer

    EngagementTrackRequest:
      type: object
      required:
        - videoId
        - action
        - platform
      properties:
        videoId:
          type: string
        action:
          type: string
          enum: [like, share, complete, view]
        platform:
          type: string
          enum: [youtube, tiktok, rumble]
        metadata:
          type: object
          additionalProperties: true

    UserStatsResponse:
      type: object
      properties:
        totalLikes:
          type: integer
        totalShares:
          type: integer
        totalCompletions:
          type: integer
        platformBreakdown:
          type: object
          additionalProperties:
            type: object
            properties:
              likes:
                type: integer
              shares:
                type: integer

    AnalyticsResponse:
      type: object
      properties:
        platformMetrics:
          type: object
          additionalProperties:
            type: object
            properties:
              totalEngagements:
                type: integer
              likes:
                type: integer
              shares:
                type: integer
        userBehavior:
          type: object
          properties:
            totalUsers:
              type: integer
            activeUsers:
              type: integer
            avgEngagementPerUser:
              type: number
        topVideos:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              title:
                type: string
              totalEngagements:
                type: integer

    AuditLog:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        action:
          type: string
        resource:
          type: string
        resourceId:
          type: string
        details:
          type: object
        timestamp:
          type: string
          format: date-time
        user:
          $ref: '#/components/schemas/User'

    AuditLogResponse:
      type: object
      properties:
        logs:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ContentReportRequest:
      type: object
      required:
        - reason
      properties:
        videoId:
          type: string
        videoLinkId:
          type: string
        reason:
          type: string
          enum: [inappropriate, spam, copyright, other]
        description:
          type: string
          maxLength: 1000

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Operation completed successfully

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
        details:
          type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

tags:
  - name: Videos
    description: Video management operations
  - name: Video Interactions
    description: User interactions with videos
  - name: Notifications
    description: Notification management
  - name: Leaderboard
    description: User rankings and leaderboard
  - name: Engagement
    description: User engagement tracking
  - name: Admin
    description: Administrative operations
  - name: Content Moderation
    description: Content reporting and moderation
  - name: Health
    description: System health checks