#!/bin/bash

# Script to convert MySQL migration files to PostgreSQL-compatible syntax

MIGRATIONS_DIR="/home/<USER>/Websites/NWAPromote/prisma/migrations"

echo "Converting MySQL migration files to PostgreSQL-compatible syntax..."

# Process each migration.sql file
find "$MIGRATIONS_DIR" -name "migration.sql" | while read -r file; do
    echo "Processing $file..."
    
    # Create a backup
    cp "$file" "${file}.backup"
    
    # Convert MySQL syntax to PostgreSQL syntax
    sed -i \
        -e 's/`/"/g' \
        -e 's/DATETIME(3)/TIMESTAMP(3)/g' \
        -e 's/CURRENT_TIMESTAMP(3)/CURRENT_TIMESTAMP/g' \
        -e 's/DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci//g' \
        -e 's/VARCHAR(191)/TEXT/g' \
        -e 's/INTEGER/INT/g' \
        -e 's/ON DELETE SET NULL ON UPDATE CASCADE/ON DELETE SET NULL/g' \
        -e 's/ON DELETE RESTRICT ON UPDATE CASCADE/ON DELETE RESTRICT/g' \
        -e 's/ON DELETE CASCADE ON UPDATE CASCADE/ON DELETE CASCADE/g' \
        "$file"
    
    echo "Converted $file"
done

echo "Migration files converted. Creating new PostgreSQL-compatible migrations..."