#!/bin/bash

# Claude Code Command: Agent OS Manager
# This command provides access to all Agent OS instructions

echo "=== Agent OS Command Manager ==="
echo ""

# Display available commands
echo "Available commands:"
echo "1. execute-instruction - Execute any instruction file"
echo "2. execute-tasks      - Execute tasks following Agent OS rules"
echo "3. plan-product       - Plan product features following Agent OS rules"
echo "4. create-spec        - Create specification following Agent OS rules"
echo "5. analyze-product    - Analyze product following Agent OS rules"
echo "6. shadcn             - Manage ShadCN UI components"
echo ""
echo "Enter command number (1-6) or name:"
read -r command_input

case $command_input in
    1|execute-instruction)
        ./.claude/commands/execute-instruction.sh
        ;;
    2|execute-tasks)
        ./.claude/commands/execute-tasks.sh
        ;;
    3|plan-product)
        ./.claude/commands/plan-product.sh
        ;;
    4|create-spec)
        ./.claude/commands/create-spec.sh
        ;;
    5|analyze-product)
        ./.claude/commands/analyze-product.sh
        ;;
    6|shadcn)
        ./.claude/commands/shadcn.sh
        ;;
    *)
        echo "Invalid command. Please enter a number (1-6) or command name."
        ;;
esac