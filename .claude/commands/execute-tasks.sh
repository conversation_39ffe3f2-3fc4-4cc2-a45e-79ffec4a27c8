#!/bin/bash

# Claude Code Command: Execute Tasks
# This command follows the Task Execution Rules from .agent-os/instructions/execute-tasks.md

echo "=== Agent OS Task Execution ==="
echo "Following the Task Execution Rules from execute-tasks.md"
echo ""

# Display the execution process
echo "1. Task Assignment"
echo "   - Identifying tasks to execute"
echo "   - Selecting next uncompleted parent task if not specified"
echo "   - Confirming task selection with user"
echo ""

echo "2. Context Analysis"
echo "   - Reading spec documentation thoroughly"
echo "   - Analyzing requirements and specifications"
echo "   - Understanding how task fits into overall spec goals"
echo ""

echo "3. Implementation Planning"
echo "   - Creating detailed execution plan"
echo "   - Displaying plan to user for review"
echo "   - Waiting for explicit approval before proceeding"
echo ""

echo "4. Development Execution"
echo "   - Executing development plan systematically"
echo "   - Following all coding standards and specifications"
echo "   - Implementing TDD approach throughout"
echo ""

echo "5. Task Status Updates"
echo "   - Updating tasks.md after each task completion"
echo "   - Marking completed items immediately"
echo "   - Documenting blocking issues with ⚠️ emoji"
echo ""

echo "6. Test Suite Verification"
echo "   - Running complete test suite"
echo "   - Verifying all tests pass including new ones"
echo "   - Fixing any test failures before continuing"
echo ""

echo "7. Git Workflow"
echo "   - Committing all changes with descriptive message"
echo "   - Pushing to GitHub on spec branch"
echo "   - Creating pull request with detailed description"
echo ""

echo "Would you like to proceed with task execution? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "Starting task execution process..."
    echo "Please specify which task(s) you want to execute:"
    read -r task_spec
    
    if [ -n "$task_spec" ]; then
        echo "Executing task(s): $task_spec"
        # Here you would add the actual task execution logic
        echo "Task execution logic would go here"
    else
        echo "No task specified. Selecting next uncompleted parent task."
        # Logic to find and execute next uncompleted task
        echo "Finding and executing next uncompleted task..."
    fi
else
    echo "Task execution cancelled"
fi