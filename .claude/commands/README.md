# Agent OS Commands for Claude Code

This directory contains custom commands for Claude Code that implement the Agent OS instruction framework.

## Available Commands

### Main Commands
- `agent-os.sh` - Main command manager that provides access to all other commands

### Individual Commands
- `execute-instruction.sh` - Execute any instruction file from `.agent-os/instructions/`
- `execute-tasks.sh` - Execute tasks following the Agent OS Task Execution Rules
- `plan-product.sh` - Plan product features following the Agent OS Product Planning Rules
- `create-spec.sh` - Create specifications following the Agent OS Spec Creation Rules
- `analyze-product.sh` - Analyze product following the Agent OS Product Analysis Rules
- `shadcn.sh` - Manage ShadCN UI components following the Agent OS guidelines

## Usage

To use these commands with Claude Code:

1. Make sure the commands are executable:
   ```bash
   chmod +x .claude/commands/*.sh
   ```

2. Run the main command:
   ```bash
   .claude/commands/agent-os.sh
   ```

3. Or run individual commands directly:
   ```bash
   .claude/commands/execute-tasks.sh
   ```

## Customization

You can customize these commands by modifying the shell scripts in this directory. Each script follows the guidelines specified in the corresponding instruction file in `.agent-os/instructions/`.