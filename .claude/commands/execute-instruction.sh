#!/bin/bash

# Claude Code Command: Execute Agent OS Instruction
# This command allows you to execute any instruction file from .agent-os/instructions/

INSTRUCTIONS_DIR=".agent-os/instructions"

# Check if instruction file is provided
if [ $# -eq 0 ]; then
    echo "Usage: execute-instruction <instruction-file>"
    echo "Available instruction files:"
    ls -1 "$INSTRUCTIONS_DIR" 2>/dev/null || echo "No instruction files found"
    exit 1
fi

INSTRUCTION_FILE="$1"

# Check if the instruction file exists
if [ ! -f "$INSTRUCTIONS_DIR/$INSTRUCTION_FILE" ]; then
    echo "Error: Instruction file '$INSTRUCTION_FILE' not found in $INSTRUCTIONS_DIR"
    echo "Available instruction files:"
    ls -1 "$INSTRUCTIONS_DIR" 2>/dev/null || echo "No instruction files found"
    exit 1
fi

# Display the instruction file content
echo "=== Executing Instruction: $INSTRUCTION_FILE ==="
echo ""
cat "$INSTRUCTIONS_DIR/$INSTRUCTION_FILE"
echo ""
echo "=== End of Instruction ==="

# Ask user if they want to proceed with execution
echo ""
echo "Would you like to proceed with executing this instruction? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "Executing instruction: $INSTRUCTION_FILE"
    # Here you would add the actual execution logic based on the instruction
    # For now, we'll just acknowledge the request
    echo "Execution logic for '$INSTRUCTION_FILE' would go here"
else
    echo "Execution cancelled"
fi