#!/bin/bash

# Claude Code Command: Create Spec
# This command follows the Spec Creation Rules from .agent-os/instructions/create-spec.md

echo "=== Agent OS Spec Creation ==="
echo "Following the Spec Creation Rules from create-spec.md"
echo ""

# Display the spec creation process
echo "1. Specification Structure"
echo "   - Creating standardized spec directory"
echo "   - Setting up required spec files"
echo "   - Organizing sub-specs if needed"
echo ""

echo "2. Requirements Documentation"
echo "   - Documenting functional requirements"
echo "   - Defining acceptance criteria"
echo "   - Identifying success metrics"
echo ""

echo "3. Technical Specification"
echo "   - Defining architecture and components"
echo "   - Specifying API endpoints"
echo "   - Planning database schema"
echo ""

echo "4. Implementation Planning"
echo "   - Breaking down into tasks"
echo "   - Estimating effort and timeline"
echo "   - Identifying dependencies"
echo ""

echo "5. Testing Strategy"
echo "   - Defining test scenarios"
echo "   - Planning test coverage"
echo "   - Setting quality standards"
echo ""

echo "Would you like to proceed with spec creation? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "Starting spec creation process..."
    echo "Please specify the name for the new spec:"
    read -r spec_name
    
    if [ -n "$spec_name" ]; then
        echo "Creating spec: $spec_name"
        # Create the spec directory structure
        SPEC_DIR=".agent-os/specs/$(date +%Y-%m-%d)-$spec_name"
        mkdir -p "$SPEC_DIR/sub-specs"
        
        echo "Created spec directory: $SPEC_DIR"
        
        # Create the main spec file
        cat > "$SPEC_DIR/spec.md" << EOF
# $spec_name

## Overview

[Description of the feature or component]

## Requirements

1. [REQUIREMENT_1]
2. [REQUIREMENT_2]

## Technical Specification

### Architecture
[Technical architecture details]

### API Endpoints
[API endpoint specifications]

### Database Schema
[Database schema changes if any]

## Implementation Plan

1. [TASK_1]
2. [TASK_2]

## Testing Strategy

### Unit Tests
[Unit test scenarios]

### Integration Tests
[Integration test scenarios]

### Acceptance Criteria
[Acceptance criteria for the feature]
EOF
        
        # Create the tasks file
        cat > "$SPEC_DIR/tasks.md" << EOF
# Tasks for $spec_name

## Implementation Tasks

- [ ] Task 1: [Description]
- [ ] Task 2: [Description]
- [ ] Task 3: [Description]

## Testing Tasks

- [ ] Unit Tests: [Description]
- [ ] Integration Tests: [Description]
- [ ] Acceptance Tests: [Description]
EOF
        
        echo "Created spec files:"
        echo "  - $SPEC_DIR/spec.md"
        echo "  - $SPEC_DIR/tasks.md"
        echo ""
        echo "Spec creation complete. You can now:"
        echo "1. Edit the spec files to add details"
        echo "2. Use 'execute-tasks' to implement the spec"
        echo "3. Commit the changes to version control"
    else
        echo "No spec name provided. Spec creation cancelled."
    fi
else
    echo "Spec creation cancelled"
fi