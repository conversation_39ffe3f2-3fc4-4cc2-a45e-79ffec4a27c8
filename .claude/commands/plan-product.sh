#!/bin/bash

# Claude Code Command: Plan Product
# This command follows the Product Planning Rules from .agent-os/instructions/plan-product.md

echo "=== Agent OS Product Planning ==="
echo "Following the Product Planning Rules from plan-product.md"
echo ""

# Display the planning process
echo "1. Mission Alignment"
echo "   - Reviewing product mission and vision"
echo "   - Ensuring new features align with overall goals"
echo "   - Considering long-term strategic impact"
echo ""

echo "2. Requirement Analysis"
echo "   - Gathering and documenting requirements"
echo "   - Identifying user needs and pain points"
echo "   - Prioritizing features based on value"
echo ""

echo "3. Technical Feasibility"
echo "   - Assessing technical complexity"
echo "   - Identifying potential challenges"
echo "   - Evaluating resource requirements"
echo ""

echo "4. Implementation Strategy"
echo "   - Breaking down features into manageable tasks"
echo "   - Creating implementation roadmap"
echo "   - Defining success criteria and metrics"
echo ""

echo "5. Risk Assessment"
echo "   - Identifying potential risks and blockers"
echo "   - Developing mitigation strategies"
echo "   - Planning for contingencies"
echo ""

echo "Would you like to proceed with product planning? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "Starting product planning process..."
    echo "Please specify the product area or feature to plan:"
    read -r feature_spec
    
    if [ -n "$feature_spec" ]; then
        echo "Planning feature: $feature_spec"
        # Here you would add the actual planning logic
        echo "Product planning logic for '$feature_spec' would go here"
        echo ""
        echo "Example planning output:"
        echo "## Implementation Plan for $feature_spec"
        echo ""
        echo "1. **Research and Analysis**"
        echo "   - Review existing solutions"
        echo "   - Identify user requirements"
        echo "   - Analyze technical constraints"
        echo ""
        echo "2. **Design Phase**"
        echo "   - Create wireframes/mockups"
        echo "   - Define API specifications"
        echo "   - Plan database schema changes"
        echo ""
        echo "3. **Implementation**"
        echo "   - Set up development environment"
        echo "   - Implement core functionality"
        echo "   - Add tests and documentation"
        echo ""
        echo "4. **Testing and Validation**"
        echo "   - Run unit and integration tests"
        echo "   - Perform user acceptance testing"
        echo "   - Validate against requirements"
        echo ""
        echo "Dependencies to Install:"
        echo " - [LIBRARY_NAME] - [PURPOSE]"
        echo ""
        echo "Test Strategy:"
        echo " - Unit tests for core logic"
        echo " - Integration tests for API endpoints"
        echo " - End-to-end tests for user flows"
    else
        echo "No feature specified. Please provide a feature or product area to plan."
    fi
else
    echo "Product planning cancelled"
fi