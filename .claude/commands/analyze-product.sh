#!/bin/bash

# Claude Code Command: Analyze Product
# This command follows the Product Analysis Rules from .agent-os/instructions/analyze-product.md

echo "=== Agent OS Product Analysis ==="
echo "Following the Product Analysis Rules from analyze-product.md"
echo ""

# Display the analysis process
echo "1. Current State Assessment"
echo "   - Reviewing existing product features"
echo "   - Analyzing user feedback and metrics"
echo "   - Identifying pain points and issues"
echo ""

echo "2. Market Analysis"
echo "   - Researching competitor solutions"
echo "   - Identifying market opportunities"
echo "   - Analyzing user needs and trends"
echo ""

echo "3. Technical Evaluation"
echo "   - Assessing code quality and architecture"
echo "   - Identifying technical debt"
echo "   - Evaluating performance and scalability"
echo ""

echo "4. User Experience Review"
echo "   - Analyzing user journeys"
echo "   - Identifying UX friction points"
echo "   - Evaluating accessibility and usability"
echo ""

echo "5. Business Impact Analysis"
echo "   - Assessing feature value and ROI"
echo "   - Analyzing user engagement metrics"
echo "   - Evaluating business objectives alignment"
echo ""

echo "Would you like to proceed with product analysis? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "Starting product analysis process..."
    echo "Please specify the product area or feature to analyze:"
    read -r feature_spec
    
    if [ -n "$feature_spec" ]; then
        echo "Analyzing feature: $feature_spec"
        # Here you would add the actual analysis logic
        echo "Product analysis logic for '$feature_spec' would go here"
        echo ""
        echo "Example analysis output:"
        echo "## Analysis of $feature_spec"
        echo ""
        echo "### Current State"
        echo "- [CURRENT_STATE_DESCRIPTION]"
        echo ""
        echo "### Identified Issues"
        echo "1. [ISSUE_1] - [IMPACT]"
        echo "2. [ISSUE_2] - [IMPACT]"
        echo ""
        echo "### Improvement Opportunities"
        echo "1. [OPPORTUNITY_1] - [BENEFIT]"
        echo "2. [OPPORTUNITY_2] - [BENEFIT]"
        echo ""
        echo "### Recommendations"
        echo "- [RECOMMENDATION_1]"
        echo "- [RECOMMENDATION_2]"
    else
        echo "No feature specified. Please provide a feature or product area to analyze."
    fi
else
    echo "Product analysis cancelled"
fi