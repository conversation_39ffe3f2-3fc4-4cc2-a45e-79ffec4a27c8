-- Create<PERSON>num
CREATE TYPE "public"."Role" AS ENUM ('USER', 'ADMIN', 'NWA_TEAM');

-- CreateEnum
CREATE TYPE "public"."NotificationPreference" AS ENUM ('ALL', 'NWA_ONLY');

-- DropFore<PERSON>Key
ALTER TABLE "public"."Account" DROP CONSTRAINT "Account_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."AuditLog" DROP CONSTRAINT "AuditLog_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."CompletedLink" DROP CONSTRAINT "CompletedLink_linkId_fkey";

-- DropForeignKey
ALTER TABLE "public"."CompletedLink" DROP CONSTRAINT "CompletedLink_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."CompletedVideo" DROP CONSTRAINT "CompletedVideo_userId_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "public"."CompletedVideo" DROP CONSTRAINT "CompletedVideo_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ContentReport" DROP CONSTRAINT "ContentReport_reporterId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ContentReport" DROP CONSTRAINT "ContentReport_reviewedBy_fkey";

-- DropForeignKey
ALTER TABLE "public"."ContentReport" DROP CONSTRAINT "ContentReport_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ContentReport" DROP CONSTRAINT "ContentReport_videoLinkId_fkey";

-- DropForeignKey
ALTER TABLE "public"."EmailPreference" DROP CONSTRAINT "EmailPreference_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."EmailQueue" DROP CONSTRAINT "EmailQueue_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Like" DROP CONSTRAINT "Like_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Like" DROP CONSTRAINT "Like_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ModerationAction" DROP CONSTRAINT "ModerationAction_moderatorId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ModerationAction" DROP CONSTRAINT "ModerationAction_reportId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ModerationAction" DROP CONSTRAINT "ModerationAction_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."ModerationAction" DROP CONSTRAINT "ModerationAction_videoLinkId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Notification" DROP CONSTRAINT "Notification_sentBy_fkey";

-- DropForeignKey
ALTER TABLE "public"."Notification" DROP CONSTRAINT "Notification_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Notification" DROP CONSTRAINT "Notification_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Session" DROP CONSTRAINT "Session_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Share" DROP CONSTRAINT "Share_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Share" DROP CONSTRAINT "Share_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserAchievement" DROP CONSTRAINT "UserAchievement_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserBadge" DROP CONSTRAINT "UserBadge_badgeId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserBadge" DROP CONSTRAINT "UserBadge_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserEngagement" DROP CONSTRAINT "UserEngagement_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserEngagement" DROP CONSTRAINT "UserEngagement_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserEngagementStreak" DROP CONSTRAINT "UserEngagementStreak_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserMilestone" DROP CONSTRAINT "UserMilestone_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserNotification" DROP CONSTRAINT "UserNotification_notificationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserNotification" DROP CONSTRAINT "UserNotification_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserSession" DROP CONSTRAINT "UserSession_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Video" DROP CONSTRAINT "Video_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."VideoLink" DROP CONSTRAINT "VideoLink_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."VideoLink" DROP CONSTRAINT "VideoLink_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."VideoLinkLike" DROP CONSTRAINT "VideoLinkLike_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."VideoLinkLike" DROP CONSTRAINT "VideoLinkLike_videoLinkId_fkey";

-- DropForeignKey
ALTER TABLE "public"."VideoLinkShare" DROP CONSTRAINT "VideoLinkShare_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."VideoLinkShare" DROP CONSTRAINT "VideoLinkShare_videoLinkId_fkey";

-- DropIndex
DROP INDEX "public"."Like_userId_videoId_key";

-- DropIndex
DROP INDEX "public"."Share_userId_videoId_key";

-- DropIndex
DROP INDEX "public"."UserEngagementStreak_userId_idx";

-- AlterTable
ALTER TABLE "public"."Like" ADD COLUMN     "mediaId" TEXT NOT NULL,
ALTER COLUMN "videoId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."Share" ADD COLUMN     "mediaId" TEXT NOT NULL,
ALTER COLUMN "videoId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."User" DROP COLUMN "role",
ADD COLUMN     "role" "public"."Role" NOT NULL DEFAULT 'USER',
DROP COLUMN "notificationPreference",
ADD COLUMN     "notificationPreference" "public"."NotificationPreference" NOT NULL DEFAULT 'ALL';

-- DropTable
DROP TABLE "public"."CompletedVideo";

-- CreateIndex
CREATE INDEX "Like_mediaId_idx" ON "public"."Like"("mediaId");

-- CreateIndex
CREATE UNIQUE INDEX "Like_userId_mediaId_videoId_key" ON "public"."Like"("userId", "mediaId", "videoId");

-- CreateIndex
CREATE INDEX "Share_mediaId_idx" ON "public"."Share"("mediaId");

-- CreateIndex
CREATE UNIQUE INDEX "Share_userId_mediaId_videoId_key" ON "public"."Share"("userId", "mediaId", "videoId");

-- CreateIndex
CREATE INDEX "UserAchievement_earnedAt_idx" ON "public"."UserAchievement"("earnedAt");

-- CreateIndex
CREATE INDEX "UserEngagementStreak_currentStreak_idx" ON "public"."UserEngagementStreak"("currentStreak");

-- CreateIndex
CREATE INDEX "UserEngagementStreak_longestStreak_idx" ON "public"."UserEngagementStreak"("longestStreak");

-- CreateIndex
CREATE INDEX "UserEngagementStreak_lastEngagementDate_idx" ON "public"."UserEngagementStreak"("lastEngagementDate");

-- CreateIndex
CREATE INDEX "UserNotification_notificationId_unique" ON "public"."UserNotification"("notificationId");

-- AddForeignKey
ALTER TABLE "public"."EmailPreference" ADD CONSTRAINT "EmailPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."EmailQueue" ADD CONSTRAINT "EmailQueue_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Share" ADD CONSTRAINT "Share_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Share" ADD CONSTRAINT "Share_mediaId_fkey" FOREIGN KEY ("mediaId") REFERENCES "public"."Media"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Share" ADD CONSTRAINT "Share_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Like" ADD CONSTRAINT "Like_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Like" ADD CONSTRAINT "Like_mediaId_fkey" FOREIGN KEY ("mediaId") REFERENCES "public"."Media"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Like" ADD CONSTRAINT "Like_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserBadge" ADD CONSTRAINT "UserBadge_badgeId_fkey" FOREIGN KEY ("badgeId") REFERENCES "public"."Badge"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserBadge" ADD CONSTRAINT "UserBadge_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Video" ADD CONSTRAINT "Video_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Notification" ADD CONSTRAINT "Notification_sentBy_fkey" FOREIGN KEY ("sentBy") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Notification" ADD CONSTRAINT "Notification_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserNotification" ADD CONSTRAINT "UserNotification_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "public"."Notification"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserNotification" ADD CONSTRAINT "UserNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserEngagement" ADD CONSTRAINT "UserEngagement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserEngagement" ADD CONSTRAINT "UserEngagement_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserMilestone" ADD CONSTRAINT "UserMilestone_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserSession" ADD CONSTRAINT "UserSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CompletedLink" ADD CONSTRAINT "CompletedLink_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CompletedLink" ADD CONSTRAINT "CompletedLink_linkId_fkey" FOREIGN KEY ("linkId") REFERENCES "public"."VideoLink"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VideoLink" ADD CONSTRAINT "VideoLink_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VideoLink" ADD CONSTRAINT "VideoLink_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VideoLinkLike" ADD CONSTRAINT "VideoLinkLike_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VideoLinkLike" ADD CONSTRAINT "VideoLinkLike_videoLinkId_fkey" FOREIGN KEY ("videoLinkId") REFERENCES "public"."VideoLink"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VideoLinkShare" ADD CONSTRAINT "VideoLinkShare_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VideoLinkShare" ADD CONSTRAINT "VideoLinkShare_videoLinkId_fkey" FOREIGN KEY ("videoLinkId") REFERENCES "public"."VideoLink"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ContentReport" ADD CONSTRAINT "ContentReport_reporterId_fkey" FOREIGN KEY ("reporterId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ContentReport" ADD CONSTRAINT "ContentReport_reviewedBy_fkey" FOREIGN KEY ("reviewedBy") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ContentReport" ADD CONSTRAINT "ContentReport_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ContentReport" ADD CONSTRAINT "ContentReport_videoLinkId_fkey" FOREIGN KEY ("videoLinkId") REFERENCES "public"."VideoLink"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ModerationAction" ADD CONSTRAINT "ModerationAction_moderatorId_fkey" FOREIGN KEY ("moderatorId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ModerationAction" ADD CONSTRAINT "ModerationAction_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "public"."ContentReport"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ModerationAction" ADD CONSTRAINT "ModerationAction_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."Video"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ModerationAction" ADD CONSTRAINT "ModerationAction_videoLinkId_fkey" FOREIGN KEY ("videoLinkId") REFERENCES "public"."VideoLink"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserAchievement" ADD CONSTRAINT "UserAchievement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserEngagementStreak" ADD CONSTRAINT "UserEngagementStreak_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- RenameIndex
ALTER INDEX "public"."Like_videoId_fkey" RENAME TO "Like_videoId_idx";

-- RenameIndex
ALTER INDEX "public"."UserSession_userId_fkey" RENAME TO "UserSession_userId_unique";

