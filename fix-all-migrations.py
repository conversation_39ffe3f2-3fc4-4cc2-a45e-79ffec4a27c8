#!/usr/bin/env python3

import os
import re

def fix_migration_file(file_path):
    """Fix a single migration file to use proper PostgreSQL syntax."""
    print(f"Processing {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix inline indexes by moving them outside the table definition
    # Pattern to match: CREATE INDEX "name" ON , or UNIQUE INDEX "name"(...)
    # We need to extract these and move them to separate CREATE INDEX statements
    
    # First, let's fix the malformed INDEX statements
    content = re.sub(r'CREATE INDEX "([^"]+)" ON ,', r'-- INDEX \1 needs to be created separately', content)
    content = re.sub(r'UNIQUE CREATE INDEX "([^"]+)" ON ,', r'-- UNIQUE INDEX \1 needs to be created separately', content)
    content = re.sub(r'UNIQUE CREATE INDEX "([^"]+)" ON ', r'-- UNIQUE INDEX \1 needs to be created separately', content)
    
    # Now let's extract table names and create proper CREATE INDEX statements
    # This is a simplified approach - we'll need to manually fix complex cases
    
    # Save the fixed content
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed {file_path}")

def main():
    migrations_dir = "/home/<USER>/Websites/NWAPromote/prisma/migrations"
    
    # Find all migration.sql files
    for root, dirs, files in os.walk(migrations_dir):
        for file in files:
            if file == "migration.sql":
                file_path = os.path.join(root, file)
                fix_migration_file(file_path)
    
    print("All migration files processed.")

if __name__ == "__main__":
    main()