name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  CACHE_KEY_PREFIX: 'nwa-promote'

jobs:
  # Code Quality Checks
  lint-and-format:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Check TypeScript
        run: npx tsc --noEmit

      - name: Check Prettier formatting
        run: npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

  # Security Scanning
  security-scan:
    name: <PERSON> Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Run security audit script
        run: npm run security:audit
        continue-on-error: true

      - name: Upload security report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-report
          path: security-report.json
          retention-days: 30

  # Unit and Integration Tests
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-type: [unit, integration]
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: postgres
          POSTGRES_DB: nwapromote_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U postgres"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Setup test environment
        run: |
          cp .env.example .env.test
          echo "DATABASE_URL=postgresql://postgres:testpassword@localhost:5432/nwapromote_test" >> .env.test
          echo "REDIS_URL=redis://localhost:6379" >> .env.test

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: postgresql://postgres:testpassword@localhost:5432/nwapromote_test

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        run: npm run test:unit -- --coverage --watchAll=false
        env:
          NODE_ENV: test

      - name: Run integration tests
        if: matrix.test-type == 'integration'
        run: npm run test:integration -- --watchAll=false
        env:
          NODE_ENV: test

      - name: Upload coverage reports
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Build and Bundle Analysis
  build:
    name: Build and Bundle Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production

      - name: Analyze bundle size
        run: |
          npx next-bundle-analyzer
          npm run bundle:analyze
        continue-on-error: true

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            .next/
            bundle-analysis.json
          retention-days: 7

  # Performance Testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Run performance audit
        run: npm run performance:audit
        continue-on-error: true

      - name: Upload performance report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-report
          path: audit-report.json
          retention-days: 30

  # Database Schema Validation
  database-check:
    name: Database Schema Check
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: postgres
          POSTGRES_DB: nwapromote_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U postgres"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Validate Prisma schema
        run: npx prisma validate

      - name: Check for schema drift
        run: npx prisma migrate diff --from-migrations ./prisma/migrations --to-schema-datamodel ./prisma/schema.prisma
        env:
          DATABASE_URL: postgresql://postgres:testpassword@localhost:5432/nwapromote_test

      - name: Generate Prisma client
        run: npx prisma generate

  # Accessibility Testing
  accessibility:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Run accessibility tests
        run: npm run test:a11y
        continue-on-error: true

      - name: Upload accessibility report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-report
          path: accessibility-report.json
          retention-days: 30

  # Dependency Check
  dependency-check:
    name: Dependency Vulnerability Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Check for known vulnerabilities
        run: npm audit --audit-level=high

      - name: Check for outdated dependencies
        run: npm outdated
        continue-on-error: true

      - name: License compliance check
        run: npx license-checker --summary
        continue-on-error: true

  # Code Coverage Report
  coverage-report:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [test]
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download coverage artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: coverage-*
          merge-multiple: true

      - name: Comment coverage on PR
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          recreate: true
          path: coverage-summary.md

  # Quality Gate
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [lint-and-format, security-scan, test, build, database-check]
    if: always()
    steps:
      - name: Check job results
        run: |
          if [[ "${{ needs.lint-and-format.result }}" != "success" ]]; then
            echo "❌ Lint and format checks failed"
            exit 1
          fi
          
          if [[ "${{ needs.test.result }}" != "success" ]]; then
            echo "❌ Tests failed"
            exit 1
          fi
          
          if [[ "${{ needs.build.result }}" != "success" ]]; then
            echo "❌ Build failed"
            exit 1
          fi
          
          if [[ "${{ needs.database-check.result }}" != "success" ]]; then
            echo "❌ Database checks failed"
            exit 1
          fi
          
          echo "✅ All quality checks passed!"

      - name: Update commit status
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.payload.pull_request.head.sha,
              state: 'success',
              context: 'Quality Gate',
              description: 'All quality checks passed'
            });
