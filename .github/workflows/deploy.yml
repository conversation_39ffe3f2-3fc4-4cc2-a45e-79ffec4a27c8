name: Deploy to Production

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Pre-deployment checks
  pre-deploy-checks:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest
    outputs:
      deploy-env: ${{ steps.set-env.outputs.environment }}
      version: ${{ steps.version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set deployment environment
        id: set-env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          else
            echo "environment=production" >> $GITHUB_OUTPUT
          fi

      - name: Generate version
        id: version
        run: |
          VERSION=$(date +%Y%m%d)-${GITHUB_SHA::8}
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run pre-deployment tests
        run: |
          npm run test:unit -- --watchAll=false --passWithNoTests
          npm run lint
          npm run build

  # Build and push Docker image
  build-image:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [pre-deploy-checks]
    permissions:
      contents: read
      packages: write
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=${{ needs.pre-deploy-checks.outputs.version }}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # Database migrations
  database-migration:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: [pre-deploy-checks]
    environment: ${{ needs.pre-deploy-checks.outputs.deploy-env }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Generate Prisma client
        run: npx prisma generate

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [pre-deploy-checks, build-image, database-migration]
    if: needs.pre-deploy-checks.outputs.deploy-env == 'staging'
    environment:
      name: staging
      url: https://staging.nwa-promote.com
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-deploy-checks.outputs.version }}"
          # Add actual deployment commands here
          # e.g., kubectl, helm, or cloud provider CLI commands

      - name: Run smoke tests
        run: |
          echo "Running smoke tests against staging..."
          # Add smoke test commands here

      - name: Notify deployment
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: context.payload.deployment.id,
              state: 'success',
              environment_url: 'https://staging.nwa-promote.com',
              description: 'Deployment to staging successful'
            });

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [pre-deploy-checks, build-image, database-migration]
    if: needs.pre-deploy-checks.outputs.deploy-env == 'production'
    environment:
      name: production
      url: https://nwa-promote.com
    steps:
      - name: Create deployment
        id: deployment
        uses: actions/github-script@v7
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'production',
              description: 'Deploy version ${{ needs.pre-deploy-checks.outputs.version }}',
              auto_merge: false
            });
            return deployment.data.id;

      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-deploy-checks.outputs.version }}"
          # Add actual deployment commands here

      - name: Run health checks
        run: |
          echo "Running health checks..."
          # Add health check commands here
          curl -f https://nwa-promote.com/api/health || exit 1

      - name: Update deployment status
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const state = '${{ job.status }}' === 'success' ? 'success' : 'failure';
            github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ steps.deployment.outputs.result }},
              state: state,
              environment_url: 'https://nwa-promote.com',
              description: state === 'success' ? 'Production deployment successful' : 'Production deployment failed'
            });

  # Post-deployment monitoring
  post-deploy-monitoring:
    name: Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-production, deploy-staging]
    if: always() && (needs.deploy-production.result == 'success' || needs.deploy-staging.result == 'success')
    steps:
      - name: Setup monitoring alerts
        run: |
          echo "Setting up post-deployment monitoring..."
          # Add monitoring setup commands here

      - name: Run performance tests
        run: |
          echo "Running performance tests..."
          # Add performance test commands here

      - name: Send deployment notification
        uses: actions/github-script@v7
        with:
          script: |
            const environment = '${{ needs.pre-deploy-checks.outputs.deploy-env }}';
            const version = '${{ needs.pre-deploy-checks.outputs.version }}';
            
            // Send notification to team (Slack, Discord, etc.)
            console.log(`Deployment to ${environment} completed successfully!`);
            console.log(`Version: ${version}`);

  # Rollback capability
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && (needs.deploy-production.result == 'failure' || needs.deploy-staging.result == 'failure')
    needs: [pre-deploy-checks, deploy-production, deploy-staging]
    environment: ${{ needs.pre-deploy-checks.outputs.deploy-env }}
    steps:
      - name: Rollback deployment
        run: |
          echo "Rolling back deployment..."
          # Add rollback commands here
          # This could involve deploying the previous version
          # or reverting database migrations if necessary

      - name: Notify rollback
        uses: actions/github-script@v7
        with:
          script: |
            console.log('Deployment rolled back due to failure');
            // Send alert to team about rollback