Instructions for NWAPromote OAuth Implementation:

  1. Remove Any Direct JWT Token Sharing

  - Do NOT try to read or validate Member Portal cookies directly
  - Do NOT use Member Portal's NEXTAUTH_SECRET
  - Each application should only use its own secret

  2. Implement Proper Token Exchange Flow

  After receiving the authorization code from Member Portal (/api/auth/callback/member-portal):

  // In NWAPromote's callback handler
  export async function GET(request) {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');

    if (code) {
      // Exchange authorization code for tokens
      const tokenResponse = await fetch('http://localhost:3001/api/oauth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: code,
          redirect_uri: 'http://localhost:3002/api/auth/callback/member-portal',
          client_id: process.env.CLIENT_ID,
          client_secret: process.env.CLIENT_SECRET,
        }),
      });

      const tokens = await tokenResponse.json();

      // Get user info using access token
      const userResponse = await fetch('http://localhost:3001/api/userinfo', {
        headers: {
          'Authorization': `Bearer ${tokens.access_token}`,
        },
      });

      const userInfo = await userResponse.json();

      // Create NWAPromote's own session with its own secret
      // This will use NWAPromote's NEXTAUTH_SECRET correctly
      const session = await createSession(userInfo, tokens);

      // Redirect to app with proper session
      return redirect('/dashboard');
    }
  }

  3. Clear Browser Cookies

  - Clear all cookies for both localhost:3001 and localhost:3002
  - This prevents any cross-site cookie confusion

  4. Verify Environment Variables

  Make sure NWAPromote has the correct configuration:
  CLIENT_ID="nwapromote-client-local"
  CLIENT_SECRET="da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff"
  MEMBER_PORTAL_URL="http://localhost:3001"
  NEXTAUTH_SECRET="b3a2c2e4d8f7a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6"  # Keep this unique!

  5. Test the Flow

  1. Click "Login with Member Portal" on NWAPromote
  2. Get redirected to Member Portal for authentication
  3. Successfully authenticate (this now works thanks to our fix)
  4. Get redirected back to NWAPromote with authorization code
  5. NWAPromote exchanges code for tokens with Member Portal
  6. NWAPromote creates its own independent session

  This approach ensures that:
  - Each application uses only its own NEXTAUTH_SECRET
  - No cross-site cookie issues
  - Proper OAuth security boundaries
  - The JWT decryption error will be resolved

