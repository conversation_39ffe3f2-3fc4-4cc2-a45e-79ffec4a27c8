services:
  # PostgreSQL Database
  postgres:
    image: postgres:16
    container_name: nwapromote-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: nwapromote
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - '5434:5432'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 20

  # Redis Cache
  redis:
    image: redis:7
    container_name: nwapromote-redis
    restart: unless-stopped
    command: ["redis-server", "--appendonly", "yes"]
    ports:
      - '6380:6379'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 20

volumes:
  pgdata: