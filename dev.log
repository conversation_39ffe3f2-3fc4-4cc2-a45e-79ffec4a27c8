nohup: ignoring input

> nwapromote@0.1.0 dev
> next dev -p ${PORT:-3002}

   ▲ Next.js 15.4.5
   - Local:        http://localhost:3002
   - Network:      http://*************:3002
   - Environments: .env.local, .env
   - Experiments (use with caution):
     · serverActions
     ✓ webpackBuildWorker

 ✓ Starting...
Warning: Reverting webpack devtool to 'false'.
Changing the webpack devtool in development mode will cause severe performance regressions.
Read more: https://nextjs.org/docs/messages/improper-devtool
 ✓ Ready in 1892ms
 ○ Compiling /middleware ...
 ✓ Compiled /middleware in 958ms (253 modules)
 ○ Compiling / ...
 ✓ Compiled / in 4.9s (1079 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET / 200 in 5537ms
No token found, redirecting to signin
No token found, redirecting to signin
No token found, redirecting to signin
 ○ Compiling /api/auth/[...nextauth] ...
 ✓ Compiled /api/auth/[...nextauth] in 570ms (687 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET /api/auth/signin/member-portal 200 in 1410ms
 ✓ Compiled / in 165ms (761 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET / 200 in 661ms
 ✓ Compiled in 367ms (308 modules)
 ✓ Compiled in 275ms (308 modules)
 ✓ Compiled in 273ms (308 modules)
 ✓ Compiled in 242ms (308 modules)
No token found, redirecting to signin
No token found, redirecting to signin
 ✓ Compiled / in 272ms (754 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET / 200 in 734ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 914ms (1016 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js 404 in 1571ms
 GET /_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js 404 in 1576ms
 GET /_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js 404 in 1572ms
 GET /_next/static/chunks/node_modules_next_dist_445d8acf._.js 404 in 1561ms
 GET /_next/static/chunks/node_modules_9b292ba4._.js.map 404 in 1378ms
 GET /_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_6aaa83c7._.js 404 in 1585ms
 GET /_next/static/chunks/node_modules_next_dist_client_20b209c9._.js 404 in 1563ms
 GET /_next/static/chunks/src_0d525f49._.js.map 404 in 1320ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/node_modules_%40swc_helpers_cjs_8e433861._.js 404 in 274ms
 GET /_next/static/chunks/node_modules_next_dist_01fcdebf._.js 404 in 269ms
 GET /_next/static/chunks/node_modules_b55780ac._.js 404 in 268ms
 GET /_next/static/chunks/_01f48b92._.js 404 in 272ms
 GET /_next/static/chunks/_e69f0d32._.js 404 in 276ms
 GET /_next/static/chunks/src_app_favicon_ico_mjs_f9cadd25._.js 404 in 273ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Authentication successful for: <EMAIL>
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/src_55585268._.js 404 in 193ms
 GET /_next/static/chunks/src_app_layout_tsx_68b267f5._.js 404 in 192ms
 GET /_next/static/chunks/node_modules_next_dist_client_components_builtin_global-error_2ab5ba2b.js 404 in 236ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js 404 in 374ms
 GET /_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_6aaa83c7._.js 404 in 377ms
 GET /_next/static/chunks/node_modules_next_dist_compiled_next-devtools_index_d575f738.js 404 in 299ms
 GET /_next/static/chunks/node_modules_next_dist_compiled_0f1b9fd4._.js 404 in 280ms
 GET /_next/static/chunks/src_0d525f49._.js.map 404 in 283ms
 GET /_next/static/chunks/node_modules_next_dist_client_20b209c9._.js 404 in 279ms
 GET /_next/static/chunks/node_modules_9b292ba4._.js.map 404 in 320ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/node_modules_next_dist_445d8acf._.js 404 in 319ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/node_modules_%40swc_helpers_cjs_8e433861._.js 404 in 252ms
 GET /_next/static/chunks/_e69f0d32._.js 404 in 251ms
 GET /_next/static/chunks/_01f48b92._.js 404 in 190ms
 GET /_next/static/chunks/node_modules_next_dist_01fcdebf._.js 404 in 202ms
 GET /_next/static/chunks/src_app_favicon_ico_mjs_f9cadd25._.js 404 in 202ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /_next/static/chunks/node_modules_b55780ac._.js 404 in 218ms
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Authentication successful for: <EMAIL>
Authentication successful for: <EMAIL>
 GET /_next/static/chunks/src_app_layout_tsx_68b267f5._.js 404 in 150ms
 GET /_next/static/chunks/src_55585268._.js 404 in 151ms
 GET /_next/static/chunks/node_modules_next_dist_client_components_builtin_global-error_2ab5ba2b.js 404 in 115ms
Authentication successful for: <EMAIL>
Authentication successful for: <EMAIL>
Authentication successful for: <EMAIL>
Authentication successful for: <EMAIL>
Authentication successful for: <EMAIL>
 ✓ Compiled /settings in 218ms (755 modules)
 ✓ Compiled in 1365ms (400 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
Session callback triggered: {
  hasSession: true,
  hasToken: true,
  tokenId: 'test-user-id',
  tokenSub: 'test-user-id',
  tokenEmail: '<EMAIL>',
  tokenRole: undefined,
  tokenKeys: [
    'name',     'email',
    'sub',      'id',
    'nwaEmail', 'iat',
    'exp',      'jti'
  ]
}
Session updated with token data: { id: 'test-user-id', email: '<EMAIL>', role: 'USER' }
 GET /dashboard 200 in 2653ms
 GET /notifications 200 in 2619ms
 GET /settings 200 in 2619ms
 GET /leaderboard 200 in 2656ms
 GET / 200 in 2667ms
 ○ Compiling /api/auth/initiate-oauth ...
 ✓ Compiled /api/auth/initiate-oauth in 543ms (540 modules)
 GET /api/auth/initiate-oauth?callbackUrl=/dashboard 307 in 625ms
 ✓ Compiled /auth/signin in 338ms (1139 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET /auth/signin 200 in 685ms
 GET /api/auth/initiate-oauth?callbackUrl=/dashboard 307 in 36ms
No token found, redirecting to signin
 ✓ Compiled /api/auth/[...nextauth] in 459ms (394 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET /api/auth/signin/member-portal?callbackUrl=%2Fdashboard 200 in 692ms
 ✓ Compiled /favicon.ico in 138ms (396 modules)
 GET /favicon.ico 200 in 214ms
 ✓ Compiled / in 275ms (775 modules)
[next-auth][warn][DEBUG_ENABLED] 
https://next-auth.js.org/warnings#debug_enabled
 GET / 200 in 719ms
No token found, redirecting to signin
No token found, redirecting to signin
No token found, redirecting to signin
 GET /api/auth/signin/member-portal?callbackUrl=%2Ficons%2Ficon-192x192.png 200 in 84ms
 GET /api/auth/signin/member-portal?callbackUrl=%2Ficons%2Ficon.svg 200 in 96ms
