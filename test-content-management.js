/**
 * Content Management Testing Script
 * 
 * This script tests video creation and management functionality
 * Run with: node test-content-management.js
 */

const BASE_URL = `http://localhost:${process.env.PORT || 3000}`;

// Mock session for testing (in real app, this would come from authentication)
const mockSession = {
  user: {
    id: 'test-admin-id',
    email: '<EMAIL>',
    role: 'ADMIN'
  }
};

async function testContentManagement() {
  console.log('🎬 Testing Content Management Functionality...\n');

  // Test 1: Get Videos List
  console.log('1️⃣ Testing Video List Retrieval...');
  try {
    const videosResponse = await fetch(`${BASE_URL}/api/videos?type=all&limit=10`);
    
    if (videosResponse.ok) {
      const videosData = await videosResponse.json();
      console.log('✅ Videos Retrieved Successfully!');
      console.log('   Total Videos:', videosData.total || 0);
      console.log('   Videos in Response:', videosData.videos?.length || 0);
      
      if (videosData.videos && videosData.videos.length > 0) {
        const firstVideo = videosData.videos[0];
        console.log('   Sample Video:');
        console.log('     - Title:', firstVideo.title);
        console.log('     - Platforms:', Object.keys(firstVideo.platforms || {}));
        console.log('     - Status:', firstVideo.status);
      }
    } else {
      console.log('❌ Failed to retrieve videos:', videosResponse.status);
    }
  } catch (error) {
    console.log('❌ Video List Request Failed:', error.message);
  }

  // Test 2: Test Video Search
  console.log('\n2️⃣ Testing Video Search...');
  try {
    const searchResponse = await fetch(`${BASE_URL}/api/videos/search?q=test`);
    
    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      console.log('✅ Video Search Working!');
      console.log('   Search Results:', searchData.videos?.length || 0);
    } else {
      console.log('❌ Video Search Failed:', searchResponse.status);
    }
  } catch (error) {
    console.log('❌ Video Search Request Failed:', error.message);
  }

  // Test 3: Test Video Creation (Mock - requires authentication)
  console.log('\n3️⃣ Testing Video Creation Structure...');
  const testVideo = {
    title: 'Test Video - Multi-Platform',
    description: 'This is a test video for platform testing',
    platforms: {
      youtubeUrl: 'https://youtube.com/watch?v=test123',
      tiktokUrl: 'https://tiktok.com/@user/video/test123',
      rumbleUrl: 'https://rumble.com/test123'
    },
    thumbnailUrl: 'https://example.com/thumbnail.jpg',
    type: 'nwa'
  };

  console.log('✅ Video Creation Payload Structure:');
  console.log('   Title:', testVideo.title);
  console.log('   Platforms:', Object.keys(testVideo.platforms));
  console.log('   Type:', testVideo.type);
  console.log('   Note: Actual creation requires authentication');

  // Test 4: Test Platform URL Validation
  console.log('\n4️⃣ Testing Platform URL Validation...');
  const platformTests = [
    { platform: 'YouTube', url: 'https://youtube.com/watch?v=dQw4w9WgXcQ', valid: true },
    { platform: 'TikTok', url: 'https://tiktok.com/@user/video/123', valid: true },
    { platform: 'Rumble', url: 'https://rumble.com/video123', valid: true },
    { platform: 'Invalid', url: 'https://invalid-platform.com/video', valid: false }
  ];

  platformTests.forEach(test => {
    const isValidUrl = test.url.startsWith('https://') && test.url.length > 10;
    const status = isValidUrl ? '✅' : '❌';
    console.log(`   ${status} ${test.platform}: ${test.url}`);
  });

  // Test 5: Test Admin Features Access
  console.log('\n5️⃣ Testing Admin Features...');
  try {
    const adminResponse = await fetch(`${BASE_URL}/api/admin/analytics`);
    
    if (adminResponse.status === 403 || adminResponse.status === 401) {
      console.log('✅ Admin Routes Properly Protected (401/403)');
    } else if (adminResponse.ok) {
      console.log('✅ Admin Analytics Accessible');
    } else {
      console.log('❌ Unexpected Admin Response:', adminResponse.status);
    }
  } catch (error) {
    console.log('❌ Admin Test Failed:', error.message);
  }

  // Test 6: Test Engagement Endpoints
  console.log('\n6️⃣ Testing Engagement Features...');
  try {
    const leaderboardResponse = await fetch(`${BASE_URL}/api/leaderboard`);
    
    if (leaderboardResponse.ok) {
      const leaderboardData = await leaderboardResponse.json();
      console.log('✅ Leaderboard Accessible!');
      console.log('   Entries:', leaderboardData.entries?.length || 0);
    } else {
      console.log('❌ Leaderboard Failed:', leaderboardResponse.status);
    }
  } catch (error) {
    console.log('❌ Leaderboard Test Failed:', error.message);
  }

  console.log('\n🎯 Content Management Testing Complete!');
}

// Run the tests
testContentManagement().catch(console.error);