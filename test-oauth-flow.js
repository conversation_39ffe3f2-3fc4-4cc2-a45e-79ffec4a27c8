#!/usr/bin/env node

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Test the OAuth flow between NWAPromote and Member Portal
async function testOAuthFlow() {
  console.log('🔐 Testing OAuth Flow Between NWAPromote and Member Portal\n');
  
  try {
    // Step 1: Test OAuth initiation
    console.log('Step 1: Testing OAuth initiation...');
    const initiateResponse = await makeRequest('http://localhost:3002/api/auth/signin/member-portal?callbackUrl=/dashboard');
    
    if (initiateResponse.statusCode === 307) {
      console.log('✅ OAuth initiation successful - redirecting to Member Portal');
      console.log('📍 Redirect URL:', initiateResponse.headers.location);
      
      // Parse the redirect URL to check parameters
      const redirectUrl = new URL(initiateResponse.headers.location);
      console.log('🔍 OAuth Parameters:');
      console.log('   - response_type:', redirectUrl.searchParams.get('response_type'));
      console.log('   - client_id:', redirectUrl.searchParams.get('client_id'));
      console.log('   - scope:', redirectUrl.searchParams.get('scope'));
      console.log('   - state:', redirectUrl.searchParams.get('state'));
      console.log('   - redirect_uri:', redirectUrl.searchParams.get('redirect_uri'));
      
      // Step 2: Test if Member Portal OAuth endpoint is accessible
      console.log('\nStep 2: Testing Member Portal OAuth endpoint...');
      const memberPortalTest = await makeRequest(redirectUrl.href);
      
      if (memberPortalTest.statusCode === 200 || memberPortalTest.statusCode === 302) {
        console.log('✅ Member Portal OAuth endpoint is accessible');
      } else {
        console.log('❌ Member Portal OAuth endpoint returned:', memberPortalTest.statusCode);
      }
      
    } else {
      console.log('❌ OAuth initiation failed with status:', initiateResponse.statusCode);
    }
    
    // Step 3: Test OpenID configuration
    console.log('\nStep 3: Testing OpenID configuration...');
    const oidcResponse = await makeRequest('http://localhost:3001/.well-known/openid-configuration');
    
    if (oidcResponse.statusCode === 200) {
      console.log('✅ OpenID configuration is accessible');
      try {
        const oidcConfig = JSON.parse(oidcResponse.body);
        console.log('🔍 OIDC Configuration:');
        console.log('   - Issuer:', oidcConfig.issuer);
        console.log('   - Authorization endpoint:', oidcConfig.authorization_endpoint);
        console.log('   - Token endpoint:', oidcConfig.token_endpoint);
        console.log('   - Userinfo endpoint:', oidcConfig.userinfo_endpoint);
      } catch (e) {
        console.log('⚠️  Could not parse OIDC configuration');
      }
    } else {
      console.log('❌ OpenID configuration not accessible:', oidcResponse.statusCode);
    }
    
    // Step 4: Check environment configuration
    console.log('\nStep 4: Checking environment configuration...');
    console.log('🔍 Configuration:');
    console.log('   - NWAPromote URL: http://localhost:3002');
    console.log('   - Member Portal URL: http://localhost:3001');
    console.log('   - Client ID: nwapromote-client-local');
    console.log('   - Callback URL: http://localhost:3002/api/auth/callback/member-portal');
    
    console.log('\n🎉 OAuth flow test completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Open your browser to: http://localhost:3002/auth/signin');
    console.log('2. Click "Sign In with Member Portal"');
    console.log('3. You should be redirected to the Member Portal for authentication');
    console.log('4. After signing in there, you should be redirected back to NWAPromote dashboard');
    
  } catch (error) {
    console.error('❌ OAuth flow test failed:', error.message);
  }
}

// Helper function to make HTTP requests
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const module = urlObj.protocol === 'https:' ? https : http;
    
    const req = module.request({
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'OAuth-Test-Script'
      }
    }, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(5000, () => {
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Run the test
testOAuthFlow();
