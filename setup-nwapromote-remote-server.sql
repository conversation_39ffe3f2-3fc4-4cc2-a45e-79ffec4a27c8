-- Add <PERSON>Promote as a remote server in the Member Portal database
-- Run this script in the Member Portal's PostgreSQL database

INSERT INTO remote_servers (
    id,
    name,
    client_id,
    client_secret,
    redirect_uris,
    default_scopes,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'NWAPromote',
    'nwapromote-client-local',
    'da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff',
    '["http://localhost:3002/api/auth/callback/member-portal"]',
    '["read:profile"]',
    NOW(),
    NOW()
) ON CONFLICT (client_id) DO UPDATE SET
    redirect_uris = EXCLUDED.redirect_uris,
    default_scopes = EXCLUDED.default_scopes,
    updated_at = NOW();

-- Verify the insertion
SELECT id, name, client_id, redirect_uris, default_scopes
FROM remote_servers
WHERE client_id = 'nwapromote-client-local';