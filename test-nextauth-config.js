#!/usr/bin/env node

// Test NextAuth configuration
console.log('🔍 Testing NextAuth Configuration\n');

// Check environment variables
console.log('1. Environment Variables:');
console.log(`   NEXTAUTH_URL: ${process.env.NEXTAUTH_URL || 'NOT SET'}`);
console.log(`   MEMBER_PORTAL_URL: ${process.env.MEMBER_PORTAL_URL || 'NOT SET'}`);
console.log(`   CLIENT_ID: ${process.env.CLIENT_ID ? 'SET' : 'NOT SET'}`);
console.log(`   CLIENT_SECRET: ${process.env.CLIENT_SECRET ? 'SET' : 'NOT SET'}`);
console.log(`   NEXTAUTH_SECRET: ${process.env.NEXTAUTH_SECRET ? 'SET' : 'NOT SET'}`);

// Check if required env vars are set
const requiredEnvVars = ['NEXTAUTH_URL', 'MEMBER_PORTAL_URL', 'CLIENT_ID', 'CLIENT_SECRET', 'NEXTAUTH_SECRET'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.log('\n❌ Missing environment variables:');
  missingEnvVars.forEach(varName => console.log(`   - ${varName}`));
} else {
  console.log('\n✅ All required environment variables are set');
}

// Test NextAuth configuration import
console.log('\n2. Testing NextAuth Configuration Import:');
try {
  const { createAuthOptions } = require('./src/lib/auth-config.ts');
  console.log('✅ Auth configuration can be imported');
} catch (error) {
  console.log('❌ Failed to import auth configuration:', error.message);
}

// Test OAuth provider configuration
console.log('\n3. OAuth Provider Configuration:');
console.log('   Provider ID: member-portal');
console.log('   Authorization URL: ${MEMBER_PORTAL_URL}/api/oauth/authorize');
console.log('   Token URL: ${MEMBER_PORTAL_URL}/api/oauth/token');
console.log('   Userinfo URL: ${MEMBER_PORTAL_URL}/api/oauth/userinfo');
console.log('   Redirect URI: http://localhost:3002/api/auth/callback/member-portal');

console.log('\n4. Expected OAuth Flow:');
console.log('   1. User clicks Sign In');
console.log('   2. NextAuth redirects to: ${MEMBER_PORTAL_URL}/api/oauth/authorize');
console.log('   3. Member Portal authenticates user');
console.log('   4. Member Portal redirects to: http://localhost:3002/api/auth/callback/member-portal');
console.log('   5. NextAuth processes callback and creates session');
console.log('   6. User redirected to dashboard');

console.log('\n5. Troubleshooting Steps:');
console.log('   - Ensure Member Portal is running on ${MEMBER_PORTAL_URL}');
console.log('   - Verify OAuth client is registered in Member Portal');
console.log('   - Confirm redirect URI is registered: http://localhost:3002/api/auth/callback/member-portal');
console.log('   - Check Member Portal OAuth logs for errors');
console.log('   - Clear browser cache and try again');

console.log('\n🎯 Test completed. Check the output above for any issues.');