This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

### Local Dev (without Docker)

Run the development server:

```bash
npm run dev
```

Open http://localhost:3000 with your browser to see the result.

### Docker/Compose (App + PostgreSQL + Redis)

Prerequisites: Docker and Docker Compose installed.

```bash
# Build and start all services (app, postgres, redis)
docker compose up --build

# In another terminal, watch logs
docker compose logs -f nwapromote-app

# Stop services
docker compose down
```

By default, the app is available at http://localhost:3002 and exposes a liveness endpoint at `/api/health` (used by container healthchecks).

### Local app + containerized DB/cache

If you prefer to run Next.js locally and only containerize Postgres and Redis:

```bash
# Start only DB and cache
docker compose up -d nwapromote-postgresql nwapromote-redis

# Ensure your .env has PostgreSQL URLs for local code
# Example (already set by default in this repo):
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/nwapromote
# REDIS_URL=redis://localhost:6379

# Run the app locally
npm run dev
```

- Container names (for docker ps): nwapromote-postgresql, nwapromote-redis. You can `docker compose logs -f nwapromote-postgresql` as needed.
- Prisma migrate/generate (connecting to the containerized DB):

```bash
# from host (local code), after DB is up
npx prisma generate
npx prisma migrate dev   # or: npx prisma migrate deploy
```

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## PostgreSQL Migration Information

This project has been migrated from MySQL to PostgreSQL. All database operations now use PostgreSQL 16.

### Key Changes

- Prisma datasource provider changed from `mysql` to `postgresql`
- Database connection strings updated to use PostgreSQL format
- All database queries and operations are now PostgreSQL-compatible
- MySQL-specific references have been removed from the codebase

### Database Schema Compatibility

The Prisma schema has been updated to work with PostgreSQL. Some field types have been adjusted for PostgreSQL compatibility:

- Text fields now use PostgreSQL `TEXT` type
- DateTime fields use PostgreSQL's native datetime types
- All indexes and constraints are PostgreSQL-compatible

## Data Migration from MySQL (Legacy)

If you have existing data in a MySQL database that needs to be migrated to PostgreSQL, you can use tools like `pgloader`:

### Using pgloader

1. Install pgloader:
   ```bash
   # On macOS
   brew install pgloader
   
   # On Ubuntu/Debian
   apt-get install pgloader
   ```

2. Create a migration command file (`migrate.sql`):
   ```sql
   LOAD DATABASE
        FROM mysql://user:password@localhost/source_db
        INTO postgresql://postgres:postgres@localhost:5432/nwapromote
   
   WITH include drop, create tables, create indexes, reset sequences,
        foreign keys, data only
   
   SET work_mem to '16MB', maintenance_work_mem to '512 MB';
   ```

3. Run the migration:
   ```bash
   pgloader migrate.sql
   ```

### Manual Export/Import Method

1. Export data from MySQL:
   ```bash
   mysqldump -u user -p source_db > mysql_backup.sql
   ```

2. Convert MySQL dump to PostgreSQL format using tools like `mysql2pgsql` or manually adjust the SQL syntax.

3. Import into PostgreSQL:
   ```bash
   psql -U postgres -d nwapromote -f postgresql_backup.sql
   ```

### Important Considerations

- Always backup your data before migration
- Test the migration process on a development environment first
- Some MySQL-specific features may need manual adjustment for PostgreSQL compatibility
- Verify all data integrity after migration

## Rollback to MySQL (Development Only)

In case you need to temporarily switch back to MySQL for development:

1. Update `prisma/schema.prisma`:
   ```prisma
   datasource db {
     provider = "mysql"
     url      = env("DATABASE_URL")
   }
   ```

2. Update `.env`:
   ```env
   DATABASE_URL="mysql://user:password@localhost:3306/database_name"
   ```

3. Recreate Prisma client:
   ```bash
   npx prisma generate
   ```

4. Update docker-compose.yml to use MySQL service instead of PostgreSQL.

**Note**: This is only for development purposes. The project is designed to work with PostgreSQL in production.

## Permissions Catalog (for Member Portal)

Call from Member Portal with a Bearer token:

```
curl -sS -H "Authorization: Bearer $PERMISSIONS_API_TOKEN" \
  "$APP_URL/api/permissions"
```

Response 200:
```
{
  "roles": ["USER","ADMIN","NWA_TEAM"],
  "permissions": ["view_document","send_documents","snooze_notifications","view_notifications"]
}
```

## Smoke Test: Permissions Catalog Endpoint

To verify the /api/permissions endpoint is working:

1. Ensure your `.env.local` contains a valid `PERMISSIONS_API_TOKEN` and the app is running.
2. Run:

```bash
curl -i -H "Authorization: Bearer $PERMISSIONS_API_TOKEN" \
  "http://localhost:3000/api/permissions"
```

You should receive a 200 response with the roles and permissions JSON. If you receive 401 or 429, check your token or rate limit.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Running Tests

Jest will automatically pick up all test files in `src/__tests__` and `src/app/api/permissions/__tests__` (if present):

```bash
npm test
```