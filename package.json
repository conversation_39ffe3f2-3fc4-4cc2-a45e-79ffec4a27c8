{"name": "nwa<PERSON>rom<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p ${PORT:-3002}", "build": "rm -rf .next && next build", "start": "next start", "lint": "next lint", "test": "jest --watchAll=false", "test:watch": "jest --watch", "test:run": "jest --watchAll=false --passWithNoTests", "test:unit": "jest --testPathPattern=__tests__ --watchAll=false", "test:integration": "jest --testPathPattern=integration --watchAll=false", "test:a11y": "jest --testPathPattern=accessibility --watchAll=false", "debug:jwt": "ts-node scripts/test-oauth-jwt.ts", "security:audit": "ts-node scripts/performance-audit.ts", "performance:audit": "ts-node scripts/performance-audit.ts", "lighthouse:audit": "node scripts/lighthouse-audit.js", "bundle:analyze": "cross-env ANALYZE=true next build", "prisma:seed": "ts-node --project ./tsconfig.seed.json prisma/seed.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:push": "prisma db push --accept-data-loss", "db:seed": "npm run prisma:seed", "db:setup": "npm run db:push && npm run db:seed", "smoke": "scripts/smoke.sh"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.13.0", "@radix-ui/react-slot": "^1.2.3", "@types/dompurify": "^3.2.0", "@types/jsdom": "^21.1.7", "@types/redis": "^4.0.11", "@types/validator": "^13.15.2", "class-variance-authority": "^0.7.1", "cross-env": "^10.0.0", "dompurify": "^3.2.6", "jsdom": "^26.1.0", "lucide-react": "^0.536.0", "next": "15.4.5", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.2", "redis": "^5.8.0", "validator": "^13.15.15", "web-push": "^3.6.7", "workbox-webpack-plugin": "^7.3.0", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^30.0.0", "@types/minimatch": "^3.0.5", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "bcryptjs": "^3.0.2", "chrome-launcher": "^1.2.0", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "lighthouse": "^12.8.1", "next-test-api-route-handler": "^5.0.0", "prisma": "^6.13.0", "tailwindcss": "^4", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}