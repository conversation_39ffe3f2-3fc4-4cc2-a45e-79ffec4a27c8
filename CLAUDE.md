# Claude Code Configuration

This project uses Claude Code for AI-assisted development with custom commands for the Agent OS framework.

## Custom Commands

The following custom commands are available in `.claude/commands/`:

- `agent-os.sh` - Main command manager for all Agent OS instructions
- `execute-instruction.sh` - Execute any instruction file from `.agent-os/instructions/`
- `execute-tasks.sh` - Execute tasks following Agent OS Task Execution Rules
- `plan-product.sh` - Plan product features following Agent OS Product Planning Rules
- `create-spec.sh` - Create specifications following Agent OS Spec Creation Rules
- `analyze-product.sh` - Analyze product following Agent OS Product Analysis Rules
- `shadcn.sh` - Manage ShadCN UI components

## Usage

To use these commands with Claude Code, simply reference them in your prompts or run them directly from the command line:

```bash
.claude/commands/agent-os.sh
```

For more information about each command, see `.claude/commands/README.md`.