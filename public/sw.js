const CACHE_NAME = 'nwa-promote-v4'; // Version bump to force cache refresh and SW logic updates
const STATIC_CACHE_URLS = [
  '/',
  '/dashboard',
  '/leaderboard',
  '/notifications',
  '/settings',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  // Skip service worker installation in insecure contexts
  if (typeof self === 'undefined' || !self.isSecureContext) {
    console.log('Skipping service worker installation in insecure context');
    return;
  }
  
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('Service Worker installed successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker installation failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  // Skip service worker activation in insecure contexts
  if (typeof self === 'undefined' || !self.isSecureContext) {
    console.log('Skipping service worker activation in insecure context');
    return;
  }
  
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker activated successfully');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  // Skip service worker fetch handling in insecure contexts
  if (typeof self === 'undefined' || !self.isSecureContext) {
    console.log('Skipping service worker fetch handling in insecure context');
    return;
  }
  
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!event.request.url.startsWith('http')) {
    return;
  }

  // Bypass Service Worker for API and SSE (realtime) requests to prevent stream caching
  const accept = event.request.headers.get('accept') || '';
  const isSSE = accept.includes('text/event-stream') || event.request.url.includes('/api/realtime/');
  const isAPI = event.request.url.includes('/api/');
  if (isSSE || isAPI) {
    // Handle API requests with proper error handling to prevent rejected promises
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // Pass through successful responses and redirects
          return response;
        })
        .catch((error) => {
          // Handle network errors gracefully
          console.log('Service Worker: API request failed, allowing browser to handle:', error.message);
          
          // Create a proper error response instead of re-throwing
          return new Response(
            JSON.stringify({
              error: 'Network Error',
              message: 'Unable to connect to the API. Please check your connection.'
            }),
            {
              status: 503,
              statusText: 'Service Unavailable',
              headers: { 'Content-Type': 'application/json' }
            }
          );
        })
    );
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        // Return cached version if available
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise, fetch from network
        return fetch(event.request)
          .then((response) => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response for caching
            const responseToCache = response.clone();

            // Cache static assets only (never cache API or SSE)
            if (event.request.url.includes('.js') ||
                event.request.url.includes('.css') ||
                event.request.url.includes('.png') ||
                event.request.url.includes('.jpg') ||
                event.request.url.includes('.svg')) {
              event.waitUntil(
                caches.open(CACHE_NAME)
                  .then((cache) => {
                    cache.put(event.request, responseToCache);
                  })
              );
            }

            return response;
          })
          .catch((error) => {
            console.error('Fetch failed:', error);
            
            // If network fails, try to serve a cached fallback
            if (event.request.destination === 'document') {
              return caches.match('/')
                .then(fallbackResponse => {
                  if (fallbackResponse) {
                    return fallbackResponse;
                  }
                  // If no fallback, throw the error to be handled below
                  throw error;
                })
                .catch(() => {
                  // If we can't get a fallback, re-throw the original error
                  throw error;
                });
            }
            
            // For API requests, check if we're actually offline
            if (event.request.url.includes('/api/')) {
              // Only return offline response if we're actually offline
              // Otherwise, re-throw the error
              if (!navigator.onLine) {
                // Ensure we're returning a properly constructed Response
                try {
                  return new Response(
                    JSON.stringify({
                      error: 'Offline',
                      message: 'You are currently offline. Please check your connection.'
                    }),
                    {
                      status: 503,
                      statusText: 'Service Unavailable',
                      headers: { 'Content-Type': 'application/json' }
                    }
                  );
                } catch (responseError) {
                  console.error('Failed to create Response object:', responseError);
                  // Re-throw the original error
                  throw error;
                }
              } else {
                // If we're online, re-throw the error
                throw error;
              }
            }
            
            // For other requests, re-throw the error
            throw error;
          });
      })
      .catch((error) => {
        // Log the error and re-throw it to let the browser handle it
        console.error('Service worker error:', error);
        throw error;
      })
  );
});

// Push notification event
self.addEventListener('push', (event) => {
  // Skip push notifications in insecure contexts
  if (typeof self === 'undefined' || !self.isSecureContext) {
    console.log('Skipping push notifications in insecure context');
    return;
  }
  
  console.log('Push notification received:', event);
  
  const options = {
    body: 'New video available for sharing!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Videos',
        icon: '/icons/icon-96x96.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/icon-96x96.png'
      }
    ]
  };

  if (event.data) {
    try {
      const payload = event.data.json();
      options.body = payload.body || options.body;
      options.title = payload.title || 'NWA Promote';
      options.data = { ...options.data, ...payload.data };
    } catch (error) {
      console.error('Error parsing push payload:', error);
      options.title = 'NWA Promote';
    }
  } else {
    options.title = 'NWA Promote';
  }

  event.waitUntil(
    self.registration.showNotification(options.title, options)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  // Skip notification click handling in insecure contexts
  if (typeof self === 'undefined' || !self.isSecureContext) {
    console.log('Skipping notification click handling in insecure context');
    return;
  }
  
  console.log('Notification clicked:', event);
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/dashboard')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.matchAll({ type: 'window' })
        .then((clientList) => {
          for (const client of clientList) {
            if (client.url === '/' && 'focus' in client) {
              return client.focus();
            }
          }
          if (clients.openWindow) {
            return clients.openWindow('/');
          }
        })
    );
  }
});

// Background sync event (for future use)
self.addEventListener('sync', (event) => {
  // Skip background sync in insecure contexts
  if (typeof self === 'undefined' || !self.isSecureContext) {
    console.log('Skipping background sync in insecure context');
    return;
  }
  
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Perform background sync operations
      console.log('Performing background sync...')
    );
  }
});