import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable standalone output for Docker
  output: 'standalone',
  // Source map configuration for better debugging
  productionBrowserSourceMaps: false, // Disable in production for better performance
  // PWA and experimental configuration
  experimental: {
    // Disabled webpackBuildWorker to prevent module loading conflicts with NextAuth v5
    // webpackBuildWorker: true,
    // Note: nodeMiddleware requires Next.js canary version, using default Edge Runtime instead
    serverActions: {
      // Allow dev access from local network IP
      allowedOrigins: [`localhost:${process.env.PORT || 3002}`, `*************:${process.env.PORT || 3002}`],
    },
  },
  // Webpack configuration for better source maps in development (removed to prevent performance warnings)
  // webpack: (config, { dev, isServer }) => {
  //   if (dev) {
  //     config.devtool = isServer ? 'source-map' : 'eval-source-map';
  //   }
  //   return config;
  // },
  // CORS is now handled in middleware.ts for better dynamic origin control
  images: {
    // Enable image optimization
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'i.pravatar.cc',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'i.ytimg.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'p16-sign.tiktokcdn-us.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'p19-sign.tiktokcdn-us.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'sp.rmbl.ws',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'rumble.com',
        port: '',
        pathname: '/**',
      },
      // More flexible patterns for video thumbnails
      {
        protocol: 'https',
        hostname: '*.ytimg.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.tiktokcdn.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.tiktokcdn-us.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;