# OAuth2 Integration in Local Development and Single Server Deployment

This document explains how OAuth2 authentication works in local development environments and single-server deployments where multiple applications run on the same machine with different ports.

## Overview

In local development and single-server deployments, multiple applications often run on the same machine but on different ports (e.g., 3000, 3001, 3002). This setup requires special consideration for OAuth2 integration because:

1. All applications share the same IP address (localhost or 192.168.1.x)
2. Each application uses a different port
3. OAuth2 redirect URIs must exactly match registered values
4. Cross-application communication must be properly configured

## Local Development Setup

### Network Configuration

During development, applications typically use:
- **Member Portal**: http://localhost:3000
- **Remote Server 1**: http://localhost:3001
- **Remote Server 2**: http://localhost:3002

### OAuth2 Configuration for Local Development

Each remote server must register its exact redirect URI with the Member Portal:

1. **Remote Server 1** registers:
   - Redirect URI: `http://localhost:3001/callback`

2. **Remote Server 2** registers:
   - Redirect URI: `http://localhost:3002/callback`

### Example Registration Process

```
POST /api/remote-servers
{
  "name": "Photo Tracker",
  "url": "http://localhost:3001",
  "redirectUris": ["http://localhost:3001/callback"]
}
```

## Single Server Deployment

### Network Configuration

In production, all applications might run on the same server with different ports:
- **Member Portal**: https://portal.nwa.org (port 443/80)
- **Remote Server 1**: https://tracker.nwa.org (port 443/80)
- **Remote Server 2**: https://docs.nwa.org (port 443/80)

Or using port-based routing:
- **Member Portal**: http://*************:3000
- **Remote Server 1**: http://*************:3001
- **Remote Server 2**: http://*************:3002

### Reverse Proxy Configuration

For production deployments, it's recommended to use a reverse proxy (like Nginx or Apache) to handle routing:

```nginx
# Nginx configuration example
server {
    listen 80;
    server_name portal.nwa.org;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

server {
    listen 80;
    server_name tracker.nwa.org;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## OAuth2 Flow in Shared Server Environment

### Authorization Request

When a user on Remote Server 1 (http://localhost:3001) initiates OAuth2 authentication:

```
GET http://localhost:3000/oauth/authorize?
  response_type=code&
  client_id=tracker-client-id&
  redirect_uri=http://localhost:3001/callback&
  scope=read:profile&
  state=xyz123
```

### Redirect Back to Remote Server

After user authentication, Member Portal redirects back:

```
HTTP 302 Found
Location: http://localhost:3001/callback?code=AUTH_CODE&state=xyz123
```

### Token Exchange

Remote Server 1 exchanges the code for a token:

```
POST http://localhost:3000/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=AUTH_CODE&
redirect_uri=http://localhost:3001/callback&
client_id=tracker-client-id&
client_secret=tracker-client-secret
```

### User Information Retrieval

Remote Server 1 retrieves user information:

```
GET http://localhost:3000/oauth/userinfo
Authorization: Bearer ACCESS_TOKEN
```

## Docker Container Considerations

### Container Networking

When using Docker, containers can communicate through:
1. **Host networking** - Containers share host network
2. **Custom bridge networks** - Containers can reach each other by name
3. **Port mapping** - Expose container ports to host

### Docker Compose Example

```yaml
version: '3.8'
services:
  member-portal:
    build: ./member-portal
    ports:
      - "3000:3000"
    networks:
      - nwa-network
  
  photo-tracker:
    build: ./photo-tracker
    ports:
      - "3001:3001"
    networks:
      - nwa-network
    environment:
      - MEMBER_PORTAL_URL=http://member-portal:3000
  
  document-manager:
    build: ./document-manager
    ports:
      - "3002:3002"
    networks:
      - nwa-network
    environment:
      - MEMBER_PORTAL_URL=http://member-portal:3000

networks:
  nwa-network:
    driver: bridge
```

### Service Discovery

In Docker containers, applications can reference each other by service name:
- Member Portal: `http://member-portal:3000`
- Photo Tracker: `http://photo-tracker:3001`
- Document Manager: `http://document-manager:3002`

## Redirect URI Registration for Different Environments

### Development Environment
```
http://localhost:3001/callback
http://127.0.0.1:3001/callback
http://localhost:3002/callback
http://127.0.0.1:3002/callback
```

### Testing Environment
```
http://test.tracker.nwa.org/callback
http://test.docs.nwa.org/callback
```

### Production Environment
```
https://tracker.nwa.org/callback
https://docs.nwa.org/callback
```

## Security Considerations

### Local Development Security

1. **Use localhost instead of IP addresses** when possible
2. **Register all development redirect URIs** with the Member Portal
3. **Use different client IDs/secrets** for development and production
4. **Implement proper CORS policies** for cross-port communication

### Network Security

1. **Firewall Configuration**
   - Only expose necessary ports to external networks
   - Restrict internal container communication as needed
   - Use network segmentation for sensitive services

2. **Service Isolation**
   - Run each service in its own container
   - Use separate networks for different security zones
   - Implement service mesh for complex deployments

## Troubleshooting Common Issues

### Port Conflicts

1. **Check running processes**:
   ```bash
   netstat -tulpn | grep :3000
   ```

2. **Kill conflicting processes**:
   ```bash
   kill -9 <PID>
   ```

3. **Use different ports**:
   ```bash
   # Instead of 3000, use 3005
   npm run dev -- --port 3005
   ```

### CORS Issues

Configure CORS properly in Member Portal:

```javascript
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'https://portal.nwa.org',
    'https://tracker.nwa.org',
    'https://docs.nwa.org'
  ],
  credentials: true
}));
```

### Redirect URI Mismatches

Ensure exact matches including:
- Protocol (http vs https)
- Host (localhost vs IP address)
- Port number
- Path

### Docker Networking Issues

1. **Check container connectivity**:
   ```bash
   docker exec -it photo-tracker ping member-portal
   ```

2. **Verify network configuration**:
   ```bash
   docker network ls
   docker network inspect nwa-network
   ```

## Best Practices

### Environment Configuration

Use environment variables for different environments:

```env
# Development
MEMBER_PORTAL_URL=http://localhost:3000
CLIENT_ID=dev-client-id
CLIENT_SECRET=dev-client-secret

# Production
MEMBER_PORTAL_URL=https://portal.nwa.org
CLIENT_ID=prod-client-id
CLIENT_SECRET=prod-client-secret
```

### Service Registration

Register all possible redirect URIs:
1. Development URLs (localhost, 127.0.0.1)
2. Testing URLs (test.subdomain.domain.com)
3. Production URLs (subdomain.domain.com)

### Monitoring and Logging

Implement proper logging for OAuth2 flows:
1. Log all authorization requests
2. Track successful and failed token exchanges
3. Monitor user info requests
4. Alert on suspicious patterns

## Example Setup for Development Team

### Team Member 1
- Member Portal: http://localhost:3000
- Photo Tracker: http://localhost:3001
- Document Manager: http://localhost:3002

### Team Member 2
- Member Portal: http://localhost:3005
- Photo Tracker: http://localhost:3006
- Document Manager: http://localhost:3007

Each team member uses different base ports to avoid conflicts.

## Conclusion

OAuth2 integration in local development and single-server deployments requires careful attention to:
1. Exact redirect URI matching
2. Proper network configuration
3. Environment-specific settings
4. Security considerations

## Permissions Catalog Endpoint (for Member Portal)

The application exposes a catalog endpoint for the Member Portal to discover roles and permissions required by this site.

- Method: GET
- Path: /api/permissions
- Auth: Authorization: Bearer ${PERMISSIONS_API_TOKEN}
- Response 200:
```
{
  "roles": ["USER","ADMIN","NWA_TEAM"],
  "permissions": [
    "view_document",
    "send_documents",
    "snooze_notifications",
    "view_notifications"
  ]
}
```

CORS: If MEMBER_PORTAL_URL is set, only that origin will receive CORS allow headers. All other origins will not receive Access-Control-Allow-Origin.

By following these guidelines, development teams can effectively implement OAuth2 authentication even when multiple applications run on the same machine with different ports.