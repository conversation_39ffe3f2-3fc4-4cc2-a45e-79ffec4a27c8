/**
 * User Registration Testing Script
 * 
 * This script tests the user registration functionality
 * Run with: node test-user-registration.js
 */

const BASE_URL = `http://localhost:${process.env.PORT || 3000}`;

async function testUserRegistration() {
  console.log('🧪 Testing User Registration Functionality...\n');

  // Test 1: Health Check
  console.log('1️⃣ Testing API Health...');
  try {
    const healthResponse = await fetch(`${BASE_URL}/api/health`);
    const healthData = await healthResponse.json();
    console.log('✅ API Health:', healthData.status);
  } catch (error) {
    console.log('❌ API Health Check Failed:', error.message);
    return;
  }

  // Test 2: Valid Registration
  console.log('\n2️⃣ Testing Valid User Registration...');
  const testUser = {
    name: 'Test User',
    email: `test.user.${Date.now()}@example.com`,
    password: 'SecurePassword123!'
  };

  try {
    const registerResponse = await fetch(`${BASE_URL}/api/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser),
    });

    if (registerResponse.ok) {
      const userData = await registerResponse.json();
      console.log('✅ User Registration Successful!');
      console.log('   User ID:', userData.id);
      console.log('   Name:', userData.name);
      console.log('   Email:', userData.email);
      console.log('   Role:', userData.role);
    } else {
      const errorData = await registerResponse.json();
      console.log('❌ Registration Failed:', errorData.error);
    }
  } catch (error) {
    console.log('❌ Registration Request Failed:', error.message);
  }

  // Test 3: Duplicate Email Registration
  console.log('\n3️⃣ Testing Duplicate Email Registration...');
  try {
    const duplicateResponse = await fetch(`${BASE_URL}/api/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser), // Same email as before
    });

    if (!duplicateResponse.ok) {
      const errorData = await duplicateResponse.json();
      console.log('✅ Duplicate Email Properly Rejected:', errorData.error);
    } else {
      console.log('❌ Duplicate Email Should Have Been Rejected');
    }
  } catch (error) {
    console.log('❌ Duplicate Test Failed:', error.message);
  }

  // Test 4: Invalid Data Registration
  console.log('\n4️⃣ Testing Invalid Data Registration...');
  const invalidUser = {
    name: '',
    email: 'invalid-email',
    password: '123'
  };

  try {
    const invalidResponse = await fetch(`${BASE_URL}/api/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidUser),
    });

    if (!invalidResponse.ok) {
      const errorData = await invalidResponse.json();
      console.log('✅ Invalid Data Properly Rejected:', errorData.error);
    } else {
      console.log('❌ Invalid Data Should Have Been Rejected');
    }
  } catch (error) {
    console.log('❌ Invalid Data Test Failed:', error.message);
  }

  console.log('\n🎯 User Registration Testing Complete!');
}

// Run the tests
testUserRegistration().catch(console.error);