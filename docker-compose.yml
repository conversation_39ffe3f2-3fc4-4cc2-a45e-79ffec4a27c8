services:
  # PostgreSQL Database
  postgres:
    image: postgres:16
    container_name: nwapromote-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: nwapromote
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - '5434:5432'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 20

  # Redis Cache
  redis:
    image: redis:7
    container_name: nwapromote-redis
    restart: unless-stopped
    command: ["redis-server", "--appendonly", "yes"]
    ports:
      - '6380:6379'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 20

  
  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nwapromote-app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # App
      NODE_ENV: development
      PORT: 3000
      HOSTNAME: 0.0.0.0
      NEXT_PUBLIC_APP_URL: http://localhost:3002
      # Database
      DATABASE_URL: ********************************************/nwapromote
      # Redis
      REDIS_URL: redis://redis:6379
    ports:
      - '3002:3000'
    env_file:
      - .env

volumes:
  pgdata:
